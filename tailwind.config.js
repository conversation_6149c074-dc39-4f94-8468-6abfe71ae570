/** @type {import('tailwindcss').Config} */
const plugin = require("tailwindcss/plugin");
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],

  theme: {
    screens: {
      "2xl": { min: "1535px" },
      xl: { min: "1279px" },
      lx: { min: "1090px" },
      lg: { min: "1030px" },
      md: { min: "767px" },
      sm: { min: "639px" },
    },
    extend: {
      fontFamily: {
        sora: ["sora", "Arial", "sans-serif"],
        lora: ["lora", "Arial", "serif"],
        inter: ["Inter", "sans-serif"],
      },
      colors: {
        primary: {
          purple: "#4F4CD8",
          baseWhite: "#FCFCFC",
          neutral200: "#dcdcdc",
          neutral300: "#cccccc",
          neutral1000: "#5b5b5b",
          neutral100: "#EFEFEF",
          neutral800: "#7B7B7B",
          neutralt1: "#2A2A2A",
          neutralt2: "#5A5A5A",
          neutral500: "#ABABAB",
          baseBlack: "#1A1A1A",
          purple800: "#2A2872",
          purple500: "#817FE3",
          purple200: "#CDCCF4",
          purple100: "#E6E5F9",
        },
      },
      borderRadius: {
        "except-tl": "0px 24px 24px 24px", // Adjust as needed
        "except-tr": "24px 0px 24px 24px", // Adjust as needed
      },
      boxShadow: {
        "custom-black": "0px 4px 6px #00000040", // Customize the offset and blur as needed
      },
      animation: {
        "spin-slow-fast": "spin 1s cubic-bezier(0.42, 0, 1, 1) infinite", // Custom animation with a slower spin
      },
    },
  },

  plugins: [
    plugin(function ({ addUtilities }) {
      addUtilities({
        ".scrollbar-hide": {
          "-ms-overflow-style": "none" /* IE and Edge */,
          "scrollbar-width": "none" /* Firefox */,
        },
        ".scrollbar-hide::-webkit-scrollbar": {
          display: "none" /* Chrome, Safari, and Opera */,
        },
      });
    }),
  ],
};
