/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { RouteNames } from "../utils/RouteNames";
import { useF<PERSON>, Submit<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { useMutation } from "react-query";
import AuthInput from "../Constants/AuthInput";
import { AuthServices } from "../utils/Services/AuthServices";
import { toast } from "react-toastify";
import Login from "./Login";
import { FaSpinner } from "react-icons/fa";
type SignUpProps = {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
};
const AuthForm = () => {
  const [isLogin, setIsLogin] = useState(false);
  const mutation = useMutation((data: SignUpProps) =>
    AuthServices.signup(data)
  );
  const [searchParams] = useSearchParams()

  const fromHomepageEmail = searchParams.get('email')

  const {
    formState: { isDirty, isValid, errors },
    handleSubmit,
    watch,
    trigger,
    setValue,
    control,
  } = useForm<SignUpProps>({
    mode: "onChange",
  });
  const navigate = useNavigate();
  // Watch all form values
  const formValues = watch();

  // Save form values to sessionStorage explicitly
  useEffect(() => {
    if (isDirty) {
      sessionStorage.setItem("signupFormData", JSON.stringify(formValues));
    }
  }, [formValues, isDirty]);

  // Retrieve form values from sessionStorage when component mounts
  useEffect(() => {
    const savedData = sessionStorage.getItem("signupFormData");
    if (savedData) {
      const parsedData = JSON.parse(savedData);
      Object.keys(parsedData).forEach((key) => {
        setValue(key as keyof SignUpProps, parsedData[key]);
      });
      trigger();
     
    }
  }, [setValue, trigger]);
  useEffect(()=>{
    if(fromHomepageEmail){
      setValue('email',fromHomepageEmail)
      console.log('email',fromHomepageEmail)
    }
  },[])

  // Clear sessionStorage on refresh
  useEffect(() => {
    const handlePageRefresh = () => {
      sessionStorage.removeItem("signupFormData");
    };

    window.addEventListener("beforeunload", handlePageRefresh);

    return () => {
      window.removeEventListener("beforeunload", handlePageRefresh);
    };
  }, []);

  const onSubmitForm: SubmitHandler<SignUpProps> = (data) => {
    const signupData = {
      ...data,
    };

    mutation.mutate(signupData, {
      onSuccess: async (data) => {
        toast.success(data?.data?.message);
        navigate(RouteNames.verifySignup, {
          state: { email: signupData.email },
        });
      },
      onError: (error: any) => {
        toast.error(
          error?.response ? error?.response?.data?.error : error?.message
        );
      },
    });
  };



  return (
    <div className="flex flex-col lg:flex-row w-full">
      {/* Left Side: Form */}
      <div className="flex-1 flex justify-center  md:px-8">
        <div className="w-full flex-col flex gap-3 md:max-w-[496px]">
          <div className="flex rounded-[16px] bg-primary-neutral100 md:flex justify-center mb-4">
            <button
              onClick={() => setIsLogin(false)}
              className={`px-1 py-3 md:px-4 md:py-3 font-sora text-[14px] flex-1 rounded-[16px] focus:outline-none ${
                !isLogin
                  ? "bg-primary-baseBlack text-primary-baseWhite"
                  : "bg-primary-neutral100 text-primary-neutral800"
              }`}
            >
              Create An Account
            </button>
            <button
              onClick={() => setIsLogin(true)}
              className={`md:px-4 px-1 py-3 md:py-3 font-sora text-[14px] flex-1 rounded-[16px] focus:outline-none ${
                isLogin
                  ? "bg-primary-baseBlack text-primary-baseWhite"
                  : "bg-primary-neutral100 text-primary-neutral800"
              }`}
            >
              Log In
            </button>
          </div>

          {/* Form Fields */}
          {!isLogin ? (
            <form className="mt-0" onSubmit={handleSubmit(onSubmitForm)}>
              <div>
                <Controller
                  name="firstName"
                  rules={{
                    required: "First Name  is required",
                    pattern: {
                      value: /^[A-Za-z'-\s]+$/,
                      message:
                        "Please use letters only. Characters like #, @, &, %,spaces and numbers (0-9) are not allowed.",
                    },
                  }}
                  control={control}
                  render={({ field }) => (
                    <AuthInput
                      label="First Name"
                      type="text"
                      placeholder="Enter your first name"
                      errorMessage={
                        errors.firstName && errors?.firstName?.message
                      }
                      {...field}
                    />
                  )}
                />

                <Controller
                  name="lastName"
                  rules={{
                    required: "Last Name  is required",
                    pattern: {
                      value: /^[A-Za-z'-\s]+$/,
                      message:
                        "Please use letters only. Characters like #, @, &, %,spaces and numbers (0-9) are not allowed.",
                    },
                  }}
                  control={control}
                  render={({ field }) => (
                    <AuthInput
                      label="Last Name (Surname)"
                      type="text"
                      placeholder="Enter your last name"
                      errorMessage={errors.lastName && errors.lastName.message}
                      {...field}
                    />
                  )}
                />

                <Controller
                  name="email"
                  rules={{
                    required: "Email address is required",
                    pattern: {
                      value: /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/,
                      message: "Invalid Email format",
                    },
                  }}
                  control={control}
                  render={({ field }) => (
                    <AuthInput
                      label="Email Address"
                      type="email"
                      placeholder="Enter your email address"
                      errorMessage={errors.email && errors.email.message}
                      {...field}
                    />
                  )}
                />
                <div className="">
                   <Controller
                  name="phoneNumber"
                  rules={{
                    pattern: {
                      value: /^\d{11}$/,
                      message: "Phone number must be exactly 11 digits",
                    },
                  }}
                  control={control}
                  render={({ field }) => (
                    <AuthInput
                      label="Whatsapp Number"
                      type="tel"
                      placeholder="Enter your whatsapp number (080 *** ****)"
                      errorMessage={errors.phoneNumber && errors.phoneNumber.message}
                      {...field}
                    />
                  )}
                />
                </div>
                <p className="font-sora text-[12px] text-[#5B5B5B]">By creating account, you agree to our <Link target="_blank" className="text-primary-purple" to='https://huxxle.com/terms-of-use'>Terms of Use </Link>  and <Link className="text-primary-purple" to='https://huxxle.com/privacy-policy' target="blank" > Privacy Policy</Link>.</p>

               
              </div>
              <div className="mt-4">
                <button
                  type="submit"
                  disabled={!isDirty || !isValid}
                  className={`text-primary-baseWhite  font-sora hover:bg-[#6866DE] h-[49px] px-4 rounded-[16px]  focus:outline-none focus:shadow-outline w-full ${
                    !isDirty || !isValid || mutation.isLoading
                      ? "opacity-50 bg-primary-purple cursor-not-allowed "
                      : " bg-primary-purple"
                  }`}
                >
                  {mutation.isLoading ? (
                    <p className="flex justify-center animate-spin-slow-fast ">
                      <FaSpinner size={20} />
                    </p>
                  ) : (
                    "Create Account"
                  )}
                </button>
              </div>
            </form>
          ) : (
            <Login />
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthForm;
