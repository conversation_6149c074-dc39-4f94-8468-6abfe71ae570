import  { useState, useRef, useEffect } from 'react';
import { Whatsapp } from 'iconsax-react';

const DraggableChatButton = ({ talkToUs }:any) => {
  const buttonRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ x: 20, y: 20 });
  const [isDragging, setIsDragging] = useState(false);
  const dragStart = useRef({ x: 0, y: 0 });
  const initialPosition = useRef({ x: 0, y: 0 });

  const handlePointerDown = (e:any) => {
    setIsDragging(false);
    const clientX = e.touches ? e.touches[0].clientX : e.clientX;
    const clientY = e.touches ? e.touches[0].clientY : e.clientY;
    dragStart.current = { x: clientX, y: clientY };
    initialPosition.current = { ...position };

    window.addEventListener('pointermove', handlePointerMove);
    window.addEventListener('pointerup', handlePointerUp);
    window.addEventListener('touchmove', handlePointerMove);
    window.addEventListener('touchend', handlePointerUp);
  };

  const handlePointerMove = (e:any) => {
    setIsDragging(true);
    const clientX = e.touches ? e.touches[0].clientX : e.clientX;
    const clientY = e.touches ? e.touches[0].clientY : e.clientY;
    const dx = dragStart.current.x - clientX;
    const dy = dragStart.current.y - clientY;

    const newX = initialPosition.current.x + dx;
    const newY = initialPosition.current.y + dy;

    const { innerWidth, innerHeight } = window;
    if (!buttonRef.current) return;
    const buttonRect = buttonRef.current.getBoundingClientRect();

    // Constrain button within viewport
    const constrainedX = Math.min(
      innerWidth - buttonRect.width - 10,
      Math.max(10, newX)
    );
    const constrainedY = Math.min(
      innerHeight - buttonRect.height - 10,
      Math.max(10, newY)
    );

    setPosition({ x: constrainedX, y: constrainedY });
  };

  const handlePointerUp = () => {
    setTimeout(() => setIsDragging(false), 0);
    window.removeEventListener('pointermove', handlePointerMove);
    window.removeEventListener('pointerup', handlePointerUp);
    window.removeEventListener('touchmove', handlePointerMove);
    window.removeEventListener('touchend', handlePointerUp);
  };

  const handleClick = () => {
    if (!isDragging) {
      talkToUs();
    }
  };

  useEffect(() => {
    // Adjust position if window resized
    const handleResize = () => {
      const { innerWidth, innerHeight } = window;
      if (!buttonRef.current) return;
      const buttonRect = buttonRef.current.getBoundingClientRect();
      setPosition((prev) => ({
        x: Math.min(prev.x, innerWidth - buttonRect.width - 10),
        y: Math.min(prev.y, innerHeight - buttonRect.height - 10),
      }));
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div
      ref={buttonRef}
      onPointerDown={handlePointerDown}
      onTouchStart={handlePointerDown}
      onClick={handleClick}
      className="fixed animate-bounce cursor-pointer z-[9999999]"
      style={{ right: position.x, bottom: position.y }}
    >
      <p className="shadow-md shadow-gray-200 font-sora text-[12px] border-[#075E54] border-[1px] rounded-[32px] flex gap-2 items-center py-1 px-2 bg-[#fff]">
        <span className="hidden md:block">Chat with us</span>
        <Whatsapp color="#128C7E" variant="Bold" size={32} />
      </p>
    </div>
  );
};

export default DraggableChatButton;
