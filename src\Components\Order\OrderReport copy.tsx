/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from "react";
import DateInputPicker from "../../Constants/DateInputPicker";
import Dropdown from "../../Constants/DropDown";

const OrderReport = ({ closeModal, isOpen }: any) => {
  const [reportStartDate, setReportStartDate] = useState(new Date());
  const [reportEndDate, setReportEndDate] = useState(new Date());
  const handleSelect = (value: string) => {
    console.log("Selected:", value);
  };

  return (
    <>
      <div className="fixed font-sora  top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
        <div
          className={`fixed top-0 right-0 h-screen md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
            isOpen ? 'translate-x-0' : 'translate-x-full'
          }`}>
          <div>
            <span
              onClick={closeModal}
              className="close-drawer text-4xl font-bold cursor-pointer my-4 flex justify-end z-[999999]">
              &times;
            </span>
            <div className="overflow-y-auto h-screen scrollbar-none hidescroll ">
              <div className="pl-6">
                <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                  Generate Order Reports
                </h2>
                <p className=" text-sm text-[#919191] font-sora ">
                  Provide the Information below to adjust stock units{' '}
                </p>
              </div>
              <div className="p-6 rounded-2xl  flex flex-col gap-6 last:mb-[200px]">
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                    Order Report Type{' '}
                  </p>
                  <Dropdown
                    options={['Option 1', 'Option 2', 'Option 3']}
                    onSelect={handleSelect}
                    placeholder="Select Order Report Type"
                  />
                </div>

                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                    Report Starts
                  </p>{' '}
                  <DateInputPicker
                    selectedDate={reportStartDate}
                    onDateChange={setReportStartDate}
                  />
                </div>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                    Report Ends
                  </p>
                  <DateInputPicker
                    selectedDate={reportEndDate}
                    onDateChange={setReportEndDate}
                  />
                </div>

                <div className="p-0  mb-20 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                    Format Type
                  </p>
                  <Dropdown
                    options={['Option 1', 'Option 2', 'Option 3']}
                    onSelect={handleSelect}
                    placeholder="Select Format Type"
                  />
                </div>

                <div className="fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5  p-10">
                  <div className="flex w-full px-7.5 gap-2.5">
                    <button
                      onClick={closeModal}
                      className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2">
                      Back
                    </button>
                    <button className="bg-[#4f4cd8] w-full text-[#fcfcfc] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 h-[49px] rounded-2xl cursor-pointer flex-2">
                      Generate Order Report
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default OrderReport;
