/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip, Legend } from 'recharts';
import { COLORS } from './pieColors';

interface DataPie {
  data: Array<{ label: string; value: number }>;
  isLoading: any;
}

const SalesByChannelPieChart: React.FC<DataPie> = ({
  data,
  isLoading,
}: DataPie) => {
  const totalValue = Array.isArray(data)
    ? data.reduce((sum: number, entry) => sum + entry.value, 0)
    : 0;

  return (
    <div>
      {isLoading ? (
        ''
      ) : (
        <>
          {totalValue === 0 ? (
            <p className="my-[50px] font-sora text-[16px] text-primary-neutralt1">
              No data available to display
            </p>
          ) : (
            <PieChart width={400} height={400}>
              <Pie
                data={data}
                dataKey="value"
                nameKey="label"
                cx="50%"
                cy="50%"
                outerRadius={120}
                fill="#8884d8"
                label={({ name, value }) =>
                  `${name}: ${value.toLocaleString()}`
                }>
                {data.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                    data-label={entry.label}
                  />
                ))}
              </Pie>
              <Tooltip />
              <Legend
                wrapperStyle={{
                  color: '#333',
                  fontSize: '14px',
                  fontFamily: 'Arial, sans-serif',
                  width: '100%',
                  display: 'flex',
                }}
                iconType="circle"
              />
            </PieChart>
          )}
        </>
      )}
    </div>
  );
};

export default SalesByChannelPieChart;
