import {
  Setting,
  Home2,
  Shop,
  ChartCircle,
  DocumentText,
 
} from 'iconsax-react';
import { RouteNames } from '../utils/RouteNames';

export const Pages = [
  {
    path: RouteNames.overview,
    name: 'Overview',
    icon: Home2,
  },
  {
    path: RouteNames.inventory,
    name: 'Inventory',
    icon: DocumentText,
  },
  {
    path: RouteNames.order,
    name: 'Orders',
    icon: Shop,
  },
  {
    path: RouteNames.analytics,
    name: 'Analytics',
    icon: ChartCircle,
  },
];

export const PagesOther = [
  {
    path: '/account-settings',
    name: 'Account Settings',
    icon: Setting,
  },
  // {
  //   path: '/logout',
  //   name: 'Logout',
  //   icon: LogoutCurve,
  // },
];

