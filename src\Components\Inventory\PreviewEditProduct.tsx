/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQuery } from 'react-query';
import { useUserAuthStore } from '../../store/auth';
import { inventoryServices } from '../../utils/Services/InventoryServices';
import { toast } from 'react-toastify';
import { Notifications } from '../../utils/Services/Notification';
import { useEffect, useRef } from 'react';

const PreviewEditProduct = ({
  productDetails,
  coverImageId,
  onBack,
  productId,
  refetch,
  onClose,
  productRefetch,
}: any) => {
  const modalRef = useRef<HTMLDivElement>(null); // Create a reference to the modal

  useEffect(() => {
    if (modalRef.current) {
      modalRef.current.scrollTo({ top: 0, behavior: 'smooth' }); // Scroll the modal container to the top
    }
  }, []);
  const user = useUserAuthStore((state) => state.user);
  const mutation = useMutation((formData: FormData) =>
    inventoryServices.editProduct(formData)
  );
  const { refetch: notificationRefetch } = useQuery(
    ["notifications", user.vendorId],
    () => Notifications.getNotification(user.vendorId),
    {
      enabled: !!user.vendorId,
      onSuccess: () => {},
      onError: (error: any) => {
        toast.error("Failed to fetch notifications");
        console.log("notification error:", error);
      },
    }
  );
  const editProduct = () => {
    const formData = new FormData();
    formData.append('SKUNumber', productDetails.SKU);
    formData.append('productName', productDetails.productName);
    formData.append('category', productDetails.category);
    formData.append('description', productDetails.description);
    formData.append('stockLevel', productDetails.stockLevel);
    formData.append('reorderLevel', productDetails.reorderLevel);
    formData.append('supplierName', productDetails.supplierName);
    formData.append(
      'discounted_price',
      productDetails.discountedPrice.toString()
    );
    formData.append('storageLocation', productDetails.storageLocation);
    formData.append('purchasePrice', productDetails.purchasePrice);
    formData.append('supplierEmail', productDetails.supplierEmail);
    formData.append('sellingPrice', productDetails.sellingPrice.toString());
    formData.append('discount', productDetails.discount.toString());
    formData.append('batchNumber', productDetails.batchNumber);
    formData.append('notes', productDetails.notes);
    formData.append('itemID', productDetails.itemID);
    formData.append('productId', productId);
    formData.append(
      'supplierPhoneNumber',
      productDetails.countryCode + productDetails.businessPhone
    );
    formData.append('userEmail', user.userEmail);
    formData.append('vendorId', user.vendorId);
    productDetails.images.forEach((imageObject: any) => {
      if (imageObject?.file) {
        formData.append('productImages', imageObject.file);
      } else if (imageObject) {
        formData.append('productImages', imageObject);
      }
    });
    mutation.mutate(formData, {
      onSuccess: (response) => {
        productRefetch();
        toast.success(response?.data?.message);
        refetch();
        notificationRefetch();
        onClose();
      },
      onError: (error: any) => {
        toast.error(error?.response?.data?.error || error?.message);
      },
    });
  };
  return (
    <div ref={modalRef}  className="h-full overflow-auto">
      <div>
        <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
          Preview Your Product
        </h2>
        <p className=" text-sm text-[#919191] font-sora ">
          Kindly review your entry and confirm
        </p>
      </div>
      <div className="pb-1">
        <div className="p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6 last:mb-[200px]">
          <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
            Product Information
          </h4>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Product Name
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {productDetails?.productName}
            </p>
          </div>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              SKU Number
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {productDetails?.SKU}
            </p>
          </div>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Category
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {productDetails?.category}
            </p>
          </div>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Description
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {' '}
              {productDetails?.description}
            </p>
          </div>
        </div>

        <div className="p-3 md:p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6 last:mb-[200px]">
          <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
            Product Images
          </h4>
          <div className="add-product-photo flex flex-col gap-5 items-center">
            <div>
              <p className="text-sm font-sora font-normal text-[#5b5b5b] mb-4 text-left">
                Main Image
              </p>

              {productDetails?.images?.length > 0 && (
                <img
                  src={
                    productDetails?.images[coverImageId]?.previewUrl ||
                    productDetails?.images[coverImageId]
                  }
                  className="w-[490px] h-[424px] rounded-lg object-cover"
                  alt="Main Product"
                />
              )}
            </div>
            <div className="additional-images-container flex flex-col mt-2.5 w-full">
              <p className="text-sm font-sora font-normal text-[#5b5b5b] mb-4 text-left">
                Additional Images
              </p>
              <div className="additional-images flex gap-2.5 w-full justify-start">
                {productDetails?.images
                  ?.filter((_: any, index: any) => index !== coverImageId)
                  .map((image: any, index: any) => (
                    <img
                      key={index}
                      src={image?.previewUrl || image}
                      className="w-[140px] h-[100px] rounded-lg object-cover cursor-pointer"
                      alt={`Additional Product ${index}`}
                    />
                  ))}
              </div>
            </div>
          </div>
        </div>

        <div className="p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6 last:mb-[200px]">
          <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
            Inventory Details
          </h4>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Initial Stock Level
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {' '}
              {productDetails?.stockLevel}
            </p>
          </div>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Reorder Stock Level
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {' '}
              {productDetails?.reorderLevel}
            </p>
          </div>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Supplier Name
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {' '}
              {productDetails?.supplierName}
            </p>
          </div>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Supplier Phone Number (Optional)
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {' '}
              {productDetails?.countryCode + productDetails?.businessPhone}
            </p>
          </div>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Supplier Email (Optional)
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {productDetails?.supplierEmail}
            </p>
          </div>

          <div className="p-0 flex flex-col gap-2 relative">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Storage Location (Optional)
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {productDetails?.storageLocation}
            </p>
          </div>
        </div>

        <div className="p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6 last:mb-[200px]">
          <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
            Pricing Information
          </h4>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Purchase Price
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              ₦<span> {productDetails?.purchasePrice?.toLocaleString()}</span>
            </p>
          </div>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Selling Price
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              ₦ <span>{productDetails?.sellingPrice?.toLocaleString()}</span>
            </p>
          </div>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Apply Discount % (Optional)
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {`${productDetails?.discount || '0'} ${'%'}`}
            </p>
          </div>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Discounted Price
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              ₦ <span>{productDetails?.discountedPrice.toLocaleString()}</span>
            </p>
          </div>
        </div>

        <div className="p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6 last:mb-[200px] md:mb-[200px] bottom-add-product">
          <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
            Additional Details
          </h4>
          <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Batch Number (Optional)
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {productDetails?.batchNumber}
            </p>
          </div>
          <div className=" border-b-[0.5px] border-[#cccccc] flex flex-col gap-2 ">
            <p className="text-xs font-sora font-normal text-[#5b5b5b]">
              Notes
            </p>
            <p className="text-sm font-sora font-normal text-[#5b5b5b]">
              {productDetails?.notes}
            </p>
          </div>
        </div>
      </div>
      <div className=" fixed bottom-0 left-0 w-full  flex justify-around z-50 bg-white py-2.5 p-2 md:p-5">
        <div className="flex w-full px-7.5 gap-2.5">
          <button
            onClick={onBack}
            className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border h-[49px] border-[#dcdcdc] px-12  rounded-2xl cursor-pointer flex-1/2">
            Back
          </button>
          <button
            type="button"
            disabled={mutation.isLoading}
            onClick={editProduct}
            className={`${
              mutation.isLoading ? 'opacity-50 cursor-auto' : ''
            } w-full text-[#fcfcfc] h-[49px] bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6  rounded-2xl cursor-pointer flex-2 `}>
            {mutation.isLoading ? 'Editing Product ...' : ' Edit Product'}
          </button>
        </div>
      </div>
    </div>
  );
};
export default PreviewEditProduct;
