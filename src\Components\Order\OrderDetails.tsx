/* eslint-disable @typescript-eslint/no-explicit-any */

import {
  CallCalling,
  ClipboardClose,
  DirectboxReceive,
  MessageText1,
  Profile,
  Receipt1,
  ReceiptEdit,
  Trash,
  Whatsapp,
} from 'iconsax-react';
import { useEffect } from 'react';
import { FormatPrice } from '../../utils/FormatPrice';
import { useState } from 'react';
import { orderServices } from './../../utils/Services/Order';
import { toast } from 'react-toastify';
import { useMutation, useQueryClient } from 'react-query';
import { useUserAuthStore } from '../../store/auth';
import InvoiceDrop from './InvoiceDrop';
import ConfirmationModal from '../../Constants/ConfirmationModal';
import PaymentModal from '../PaymentModal';

interface OrderDetailsProps {
  isOpen: boolean;
  closeModal: () => void;
  order: any;
  isLoading: any;
  openEditOrder: any;
  viewingId: any;
  refetch: any;
  deleteSingleOrder: any;
  deleteSingleOrderModal: any;
  orderDetailsRefetch: any;
  deleteLoading: any;
  setDeleteSingleOrderModal: any;
  handleDownloadInvoice: any;
}
const OrderDetails: React.FC<OrderDetailsProps> = ({
  isOpen,
  closeModal,
  order,
  isLoading,
  openEditOrder,
  viewingId,
  refetch,
  deleteSingleOrder,
  orderDetailsRefetch,
  deleteLoading,
  deleteSingleOrderModal,
  setDeleteSingleOrderModal,
  handleDownloadInvoice,
}) => {
  const user = useUserAuthStore((state) => state.user);
  const queryClient = useQueryClient();
  const markOrder = useMutation(orderServices.markOrder);
  const [currentStatus, setCurrentStatus] = useState<string>(
    order?.orderStatus
  );
  const [openInvoiceModal, setOpenInvoiceModal] = useState<boolean>(false);
  const [openReceiptModal, setOpenReceiptModal] = useState<boolean>(false);
  const [isStatusLoading, setIsStatusLoading] = useState<boolean>(false);
  const [isModalOpen, setModalOpen] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(
    null
  );
  const [copyPhone, setCopyPhone] = useState<boolean>(false);
  const [modalConfig, setModalConfig] = useState({
    isOpen: false,
    title: '',
    text: '',
    actionButtonText: '',
    actionHandler: () => {},
  });
  const mutation = useMutation((data: any) => orderServices.sendAsEmail(data));

  const openDeleteProductModal = (productId: string) => {
    setSelectedProductId(productId);
    setDeleteSingleOrderModal(true);
  };
  const handleDelete = () => {
    if (selectedProductId) {
      deleteSingleOrder(selectedProductId);
    }
  };

  console.log('in order details', order);
  useEffect(() => {
    if (order?.orderStatus) {
      setCurrentStatus(order.orderStatus);
    }
    if (isOpen) {
      document.body.classList.add('no-scroll');
    } else {
      document.body.classList.remove('no-scroll');
    }

    return () => {
      document.body.classList.remove('no-scroll');
    };
  }, [isOpen, order]);
  if (!isOpen) return null;

  const handleInvoiceModal = () => {
    setOpenInvoiceModal((prev) => !prev);
  };
  const handleReceiptModal = () => {
    setOpenReceiptModal((prev) => !prev);
  };

  // Generic handler for sending invoice or receipt
  const handleSendEmail = (type: 'invoice' | 'receipt') => {
    const emailData = {
      vendorId: user.vendorId,
      orderId: order.orderId,
      customerId: order.customerPhone,
      type,
      publicUrl: order.publicUrl,
      invoiceDownload: type === 'invoice' ? order.invoiceDownload : undefined,
      receiptDownload: type === 'receipt' ? order.receiptDownload : undefined,
      paymentStatus: order.paymentStatus,
      paymentMethod: order?.paymentMethod || '',
      orderDate: order.orderDate,
      paidOn: order?.paymentDate || '',
      grandTotal: order.grandTotal,
      subTotalFee: order.subTotalFee,
      deliveryFee: order.deliveryFee,
      discountedFee: order.discountedFee,
    };

    mutation.mutate(emailData, {
      onSuccess: async (data) => {
        toast.success(data?.data?.message || `${type} sent successfully`);
        setOpenInvoiceModal(false);
        setOpenReceiptModal(false);
      },
      onError: (error: any) => {
        if (error?.response?.status === 429) {
          toast.error(error?.response?.data?.message || 'Too many requests');
        } else {
          toast.error(
            error?.response?.data?.error ||
              error?.message ||
              'An error occurred'
          );
        }
      },
    });
  };
  const handleSendInvoiceAsEmail = () => handleSendEmail('invoice');
  const handleSendReceiptAsEmail = () => handleSendEmail('receipt');

  const handleDownloadReceipt = () => {
    if (!order?.receiptDownload) {
      toast.error('Receipt download URL is not available.');
      return;
    }

    const link = document.createElement('a');
    link.href = order?.receiptDownload;
    link.download = `Receipt_${order.orderNumber}.pdf`;
    link.target = '_blank';
    link.click();
    document.body.removeChild(link);
    toast.success('Receipt downloaded successfully.');
  };

  const handlePhoneNumber = () => {
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    if (isMobile) {
      // Initiate phone call on mobile
      window.location.href = `tel:+234${order.customerPhone}`;
    } else {
      // Copy phone number on desktop
      navigator.clipboard.writeText(`+234${order.customerPhone}`).then(
        () => {
          setCopyPhone(true);
          setTimeout(() => setCopyPhone(false), 4000);
        },
        (err) => {
          console.error('Failed to copy phone number:', err);
        }
      );
    }
  };

  const Socials = [
    {
      icon: Whatsapp,
      bgColor: 'bg-[#28A745]',
      borderColor: 'border-[#28A745]',
      color: '#FCFCFC',
      onclick: () => {
        window.open(`https://wa.me/+234${order.customerPhone}`);
      },
    },
    {
      icon: CallCalling,
      color: '#FF9900',
      borderColor: ' border-[#FF9900]',
      onclick: () => {
        handlePhoneNumber();
      },
    },
    {
      icon: MessageText1,
      borderColor: 'border-[#4F4CD8]',
      color: '#4F4CD8',
      onclick: () => {
        window.location.href = `mailto:${order.customerEmail}`;
      },
    },
  ];

  const openConfirmationModal = (
    status: string,
    isEntireOrder: boolean,
    productIds: string[] = []
  ) => {
    setModalConfig({
      isOpen: true,
      title: `Are you sure you want to “Mark As ${status}”`,
      text: `This action will mark ${
        isEntireOrder ? 'this order' : 'the selected product'
      } as “${status}”, The status will be changed to “${status}”. ${
        status === 'Delivered'
          ? 'It implies your customer has received this order.'
          : status === 'Shipped'
          ? 'It implies you have sent this out for delivery.'
          : 'It implies your customer has returned this order.'
      }`,
      actionButtonText: `Mark As ${status}`,
      actionHandler: () => handleMarkOrder(status, isEntireOrder, productIds),
    });
  };

  const createPayload = (
    status: string,
    isEntireOrder: boolean,
    productIds: string[] = []
  ): any => ({
    vendorId: user.vendorId,
    orderId: order?.orderId,
    vendorEmail: user.userEmail,
    orderStatus: status,
    productIds: isEntireOrder ? [] : productIds,
    editEntireOrder: isEntireOrder,
  });

  const handleMarkOrder = async (
    status: string,
    isEntireOrder: boolean,
    productIds: string[] = []
  ) => {
    const payload = createPayload(status, isEntireOrder, productIds);

    setIsStatusLoading(true);
    try {
      const updatedOrder = await markOrder.mutateAsync(payload);
      setCurrentStatus(status);
      queryClient.invalidateQueries('orderTable');
      orderDetailsRefetch();
      refetch();
      toast.success(updatedOrder?.message);
      setModalConfig((prev) => ({ ...prev, isOpen: false }));
    } catch (error: any) {
      toast.error(
        error?.response ? error?.response?.data?.error : error?.message
      );
    } finally {
      setIsStatusLoading(false);
    }
  };

  const handleMark = (status: string) => openConfirmationModal(status, true);
  const handleMarkSingleOrder = (productIds: string[], status: string) => {
    openConfirmationModal(status, false, productIds);
  };

  const openModal = () => {
    setModalOpen(true);
  };

  return (
    <div className="fixed inset-0 z-30 font-sora bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full w-full md:max-w-[580px] bg-white   p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
        <div>
          <span
            onClick={closeModal}
            className=" text-4xl font-bold cursor-pointer my-0 flex justify-end z-50 ">
            &times;
          </span>
          <div className="overflow-y-auto h-screen flex flex-col gap-4 scrollbar-none hidescroll ">
            <div>
              <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                Order Details
              </h2>
              <p className=" text-sm text-[#919191] font-sora ">
                See and manage the details of your orders.
              </p>
            </div>

            <div className=" rounded-2xl flex flex-col gap-6 bg-[#F5F5F5] px-[24px] py-[16px]">
              {' '}
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
              ) : (
                <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                  Order Overview
                </h4>
              )}
              <div className=" flex flex-col gap-5 relative  ">
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Order ID
                    </p>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      {order?.orderId || 'N/A'}
                    </p>
                  )}
                </div>
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Order Status
                    </p>
                  )}

                  <div className=" flex items-center justify-between">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                    ) : (
                      <div className=" flex items-center justify-between w-full">
                        <p
                          className={`text-[14px] px-3 py-1 text-center rounded-2xl font-sora font-normal ${
                            currentStatus === 'Pending'
                              ? 'text-[#FF9900] bg-[#FFF2DF]'
                              : currentStatus === 'Shipped'
                              ? 'bg-[#E6E5F9] text-[#4F4CD8]'
                              : currentStatus === 'Delivered'
                              ? 'bg-[#EAF7ED] text-[#28A745]'
                              : 'bg-[#DCDCDC] text-primary-baseBlack'
                          }`}>
                          {currentStatus}
                        </p>
                        {currentStatus === 'Pending' ? (
                          <button
                            onClick={() => handleMark('Shipped')}
                            className="px-3 text-[14px] py-1 text-primary-baseWhite rounded-2xl bg-primary-purple font-sora">
                            {' '}
                            Mark As Shipped
                          </button>
                        ) : currentStatus === 'Shipped' ? (
                          <button
                            onClick={() => handleMark('Delivered')}
                            disabled={isLoading}
                            className="px-3 text-[14px] py-1 text-primary-baseWhite rounded-2xl bg-[#28A745] font-sora">
                            {' '}
                            Mark As Delivered
                          </button>
                        ) : currentStatus === 'Delivered' ? (
                          <button
                            onClick={() => handleMark('Returned')}
                            className="px-3 text-[14px] py-1 text-primary-baseWhite rounded-2xl bg-primary-baseBlack font-sora">
                            {' '}
                            Mark As Returned
                          </button>
                        ) : (
                          ''
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Order Date
                    </p>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      {order?.orderDate}
                    </p>
                  )}
                </div>
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Ordered From
                    </p>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      {order?.channel}
                    </p>
                  )}
                </div>
                <div className="flex flex-col gap-3 pb-6 ">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Customer
                    </p>
                  )}

                  <div className="flex justify-between items-center">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                    ) : (
                      <p className="text-[14px] font-sora font-normal flex items-center gap-2 text-[#5b5b5b]">
                        <Profile
                          variant="Bold"
                          className="text-primary-purple"
                        />{' '}
                        {order?.customerName}
                      </p>
                    )}

                    <div className="flex gap-6 items-center">
                      {Socials.map((item, index) => (
                        <button
                          onClick={item.onclick}
                          key={index}
                          className="w-[36px] relative h-[36px]">
                          <item.icon
                            size={36}
                            color={item.color}
                            className={`${item.bgColor}   border-[1px] ${item.borderColor} p-2 rounded-2xl`}
                          />
                          {copyPhone && item.icon === CallCalling && (
                            <div className="absolute w-[150px] mt-[10px] left-1/2 transform -translate-x-1/2 text-[14px] text-primary-neutralt1 bg-white px-2 py-2 rounded shadow">
                              Contact Copied
                            </div>
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className=" rounded-2xl flex flex-col gap-6 bg-[#F5F5F5] px-[24px] py-[16px]">
              {' '}
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px]"></div>
              ) : (
                <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                  Order Summary
                </h4>
              )}
              <div className=" rounded-2xl flex flex-col gap-6 bg-[#F5F5F5] ">
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                ) : (
                  <div className="p-6 rounded-2xl  flex- bg-primary-baseWhite flex-col gap-6 relative last:border-none ">
                    {order?.orderSummary
                      .slice(0, 3)
                      .map((product: any, index: any) => (
                        <div
                          className="border-b pb-4 last:border-none "
                          key={index}>
                          <div className="flex flex-col md:flex-row md:items-center  w-full justify-between">
                            <div className="flex items-center gap-2">
                              <div>
                                <img
                                  className="w-[100px] h-[100px] object-contain rounded-2xl"
                                  src={product?.productImage || 'N/A'}
                                  alt={product?.productName || 'N/A'}
                                />
                              </div>
                              <div className="flex flex-col gap-3">
                                <p className="text-[#919191] font-sora text-[12px]">
                                  {product?.productSKU}
                                </p>
                                <h4 className="text-[16px] font-sora text-primary-neutralt1">
                                  {product?.productName || 'N/A'}
                                </h4>
                                <p className="text-[#919191] font-sora text-[12px]">
                                  Quantity: {product?.quantity || 'N/A'}
                                </p>
                              </div>
                            </div>
                            <div className=" md:text-right w-full md:w-fit ">
                              <div className="flex flex-col gap-3 text-right">
                                <h4 className="font-sora text-[#28A745] text-[16px] font-semibold">
                                  ₦{' '}
                                  {FormatPrice(
                                    product?.discountedPrice || 'N/A'
                                  )}
                                </h4>
                                <p className="text-[14px] text-[#919191] font-sora">
                                  Unit Price: ₦{' '}
                                  {FormatPrice(
                                    product?.discountedPrice || 'N/A'
                                  )}
                                </p>

                                <div className="flex w-full items-center gap-2 justify-end">
                                  {product?.orderStatus === 'Pending' ? (
                                    <button
                                      type="button"
                                      onClick={() =>
                                        handleMarkSingleOrder(
                                          [product?.productId],
                                          'Shipped'
                                        )
                                      }
                                      className="text-sm w-full md:w-fit text-[#2A2A2A] text-nowrap rounded-2xl border border-[#4F4CD8] px-5 py-[5px] font-normal">
                                      {' '}
                                      Mark As Shipped
                                    </button>
                                  ) : product?.orderStatus === 'Shipped' ? (
                                    <button
                                      type="button"
                                      onClick={() =>
                                        handleMarkSingleOrder(
                                          [product?.productId],
                                          'Delivered'
                                        )
                                      }
                                      className="text-sm w-full md:w-fit text-[#2A2A2A] text-nowrap rounded-2xl border border-[#4F4CD8] px-5 py-[5px] font-normal">
                                      {' '}
                                      Mark As Delivered
                                    </button>
                                  ) : product?.orderStatus === 'Delivered' ? (
                                    <button
                                      type="button"
                                      onClick={() =>
                                        handleMarkSingleOrder(
                                          [product?.productId],
                                          'Returned'
                                        )
                                      }
                                      className="text-sm w-full md:w-fit text-[#2A2A2A] text-nowrap rounded-2xl border border-[#4F4CD8] px-5 py-[5px] font-normal">
                                      {' '}
                                      Mark As Returned
                                    </button>
                                  ) : (
                                    ''
                                  )}

                                  <button
                                    type="button"
                                    onClick={() =>
                                      openDeleteProductModal(product.productId)
                                    }
                                    className="border border-[#DCDCDC] rounded-full p-2">
                                    <Trash size="16" color="#DC3545" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                )}{' '}
              </div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px]"></div>
              ) : (
                <div className="flex flex-col gap-4">
                  <div className="flex items-center justify-between gap-3 pb-6 border-b-[0.5px] border-primary-neutral300">
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Total
                    </p>
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      ₦
                      {FormatPrice(
                        order?.orderSummary?.reduce(
                          (total: any, item: any) =>
                            total + (item?.totalAmount || 0),
                          0
                        )
                      )}
                    </p>
                  </div>
                  <div className="flex justify-between gap-3 pb-6 border-b-[0.5px] border-primary-neutral300">
                    <p className="text-[14px]  font-sora font-normal text-[#DC3545]">
                      Discount Fee
                    </p>
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      - ₦{FormatPrice(order?.discountedFee)}
                    </p>
                  </div>
                  <div className="flex justify-between gap-3 pb-6 border-b-[0.5px] border-primary-neutral300">
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Delivery Fee
                    </p>
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      ₦{FormatPrice(order?.deliveryFee)}
                    </p>
                  </div>
                  <div className="flex justify-between items-start gap-3 pb-6 ">
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Grand Total
                    </p>
                    <p className="text-[20px] font-sora font-semibold text-[#28A745]">
                      ₦{FormatPrice(order?.grandTotal)}
                    </p>
                  </div>
                </div>
              )}
            </div>

            <div className=" rounded-2xl flex flex-col gap-6 bg-[#F5F5F5] px-[24px] py-[16px]">
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px]"></div>
              ) : (
                <div>
                  <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                    Delivery Information
                  </h4>
                  <div className=" flex flex-col gap-5 relative  ">
                    <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                      <p className="text-[12px] font-sora font-normal text-[#919191]">
                        Sent Out For Delivery
                      </p>
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        {order?.deliveryDate || 'Not Provided'}
                      </p>
                    </div>
                    <div className="flex flex-col gap-3 ">
                      <p className="text-[12px] font-sora font-normal text-[#919191]">
                        Address
                      </p>
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        {order?.deliveryAddress || 'Not Provided'}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className=" rounded-2xl flex flex-col gap-6 bg-[#F5F5F5] px-[24px] py-[16px]">
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px]"></div>
              ) : (
                <div>
                  {' '}
                  <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                    Payment Information
                  </h4>
                  <div className=" flex flex-col gap-5   ">
                    <div className="flex flex-col gap-3 pb-4">
                      <p className="text-[12px] font-sora font-normal text-[#919191]">
                        Payment Status
                      </p>
                      <div className="flex justify-between items-center relative">
                        <div className="flex gap-4 items-center">
                          {order?.paymentStatus === 'Paid' ? (
                            <p className=" w-[10px] h-[10px] rounded-full bg-green-700"></p>
                          ) : (
                            <p className=" w-[10px] h-[10px] rounded-full bg-[#FF9900]"></p>
                          )}
                          <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                            {/* {Data[1].status} */}

                            {order?.paymentStatus}
                          </p>
                        </div>

                        <button
                          onClick={handleInvoiceModal}
                          className=" border-primary-purple text-primary-baseBlack border-[1px] rounded-2xl px-[16px] py-[4px]">
                          {' '}
                          Send Invoice
                        </button>
                        {openInvoiceModal && (
                          <InvoiceDrop
                            onClose={handleInvoiceModal}
                            positionStyles="right-0 mb-44"
                            options={[
                              {
                                label: mutation.isLoading
                                  ? 'sending . . .'
                                  : 'Send As Email',
                                onClick: handleSendInvoiceAsEmail,
                                disabled: mutation.isLoading,
                              },
                              {
                                label: 'Download Invoice',
                                onClick: handleDownloadInvoice,
                              },
                            ]}
                          />
                        )}
                      </div>
                    </div>

                    {order?.paymentStatus === 'Paid' && (
                      <>
                        <div className="flex flex-col gap-3 py-4 border-y-[1px] border-primary-neutral300">
                          <p className="text-[12px] font-sora font-normal text-[#919191]">
                            Payment made on
                          </p>
                          <div className="flex justify-between items-center">
                            <div className="flex gap-4 items-center">
                              <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                                {/* {Data[1].status} */}

                                {order?.paymentDate || '-'}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col  pb-4 gap-3 border-b-[1px] border-primary-neutral300 relative">
                          <p className="text-[12px] font-sora font-normal text-[#919191]">
                            Payment Method
                          </p>
                          <div className="flex justify-between items-center ">
                            <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                              {order?.paymentMethod}
                            </p>
                            <button
                              onClick={handleReceiptModal}
                              className=" border-primary-purple text-primary-baseBlack border-[1px] rounded-2xl px-[16px] py-[4px]">
                              Send Receipt
                            </button>
                            {openReceiptModal && (
                              <InvoiceDrop
                                onClose={handleReceiptModal}
                                positionStyles="right-0 mb-44"
                                options={[
                                  {
                                    label: mutation.isLoading
                                      ? 'sending . . .'
                                      : 'Send As Email',
                                    onClick: handleSendReceiptAsEmail,
                                    disabled: mutation.isLoading,
                                  },
                                  {
                                    label: 'Download Receipt',
                                    onClick: handleDownloadReceipt,
                                  },
                                ]}
                              />
                            )}
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                  {order?.paymentProof !== '' && (
                    <div className="flex flex-col gap-3 mt-5 ">
                      <p className="text-[12px] font-sora font-normal text-[#919191]">
                        Proof of Payment
                      </p>
                      <div className="flex justify-between items-center">
                        <div className="flex gap-4 items-center">
                          <button
                            onClick={openModal}
                            className="text-[14px] gap-2 flex items-center font-sora  text-primary-purple font-bold">
                            <Receipt1 size={14} variant="Bold" /> View Payment
                            Proof
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
            <div className="md:mt-[190px] mt-[250px]"></div>
          </div>

          {/* bottom nav */}
          <div className=" border-t-[1px] rounded-2xl fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5  p-10">
            <button
              type="button"
              onClick={() => openEditOrder(viewingId)}
              className=" flex flex-col items-center cursor-pointer">
              <div className="bg-[#F5F5F5] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                <ReceiptEdit size="24" color="#2A2A2A" />{' '}
              </div>
              <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                Edit
              </p>
            </button>
            <button
              type="button"
              onClick={handleDownloadInvoice}
              className=" flex flex-col items-center cursor-pointer">
              <div className=" bg-[#EAF7ED] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                <DirectboxReceive size="24" color="#28A745" />{' '}
              </div>
              <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                Invoice
              </p>
            </button>

            <div
              id="cancel-button"
              onClick={closeModal}
              className=" flex flex-col items-center cursor-pointer">
              <div className="bg-[#FAF2F2] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                <ClipboardClose size="24" color="#DC3545" />
              </div>
              <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                Cancel
              </p>
            </div>
          </div>
        </div>
      </div>

      <ConfirmationModal
        isOpen={modalConfig.isOpen}
        title={modalConfig.title}
        text={modalConfig.text}
        actionButtonText={
          isStatusLoading ? 'Loading...' : modalConfig.actionButtonText
        }
        cancelButtonText="No, Cancel"
        onActionClick={() => {
          modalConfig.actionHandler();
        }}
        onCancelClick={() =>
          setModalConfig((prev) => ({ ...prev, isOpen: false }))
        }
        actionButtonDisabled={isStatusLoading}
      />

      {deleteSingleOrderModal && (
        <ConfirmationModal
          isOpen={deleteSingleOrderModal}
          title="Are you sure you want to delete this product?"
          text="This action will remove this product from your order."
          actionButtonText={deleteLoading ? 'deleting . . .' : 'Delete Product'}
          cancelButtonText="No, Cancel"
          onActionClick={handleDelete}
          onCancelClick={() => setDeleteSingleOrderModal(false)}
          actionButtonStyle="bg-[#DC3545]"
          actionButtonDisabled={deleteLoading}
        />
      )}
      {isModalOpen && (
        <PaymentModal
          isOpen={isModalOpen}
          onClose={() => setModalOpen(false)}
          order={order}
          imageSrc={order?.paymentProof}
          refetch={orderDetailsRefetch}
          refetchTable ={refetch}
        />
      )}
    </div>
  );
};

export default OrderDetails;
