import { Icon } from "iconsax-react";

interface Cards {
  title?: string;
  image?: Icon | string;
  value?: number;
  bgIcon?: string;
  iconColor?: string;
}

const CardComponent: React.FC<Cards> = ({ title, image, value,bgIcon,iconColor }) => {
  return (
    <div className="border-[1px] w-[256px] border-primary-neutral200 rounded-[16px] bg-primary-baseWhite px-[24px] py-[16px]">
      <div className="flex  items-center gap-2">
        {typeof image === 'string' ? (
          <img
            src={image}
            alt={title}
            className={`p-[8px] ${iconColor} ${bgIcon}`}
          />
        ) : (
          image && (
            <image className={`p-[8px] ${iconColor} ${bgIcon}`} />
          )
        )}
        <h2 className="font-sora text-primary-neutral1000 text-[14px]">
          {title}
        </h2>
      </div>
      <p className="font-sora mt-[15px] text-[24px] text-primary-neutralt1 font-bold">
        {value}
      </p>
    </div>
  );
};

export default CardComponent;