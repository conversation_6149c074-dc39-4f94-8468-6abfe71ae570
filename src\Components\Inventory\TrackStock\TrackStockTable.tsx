/* eslint-disable @typescript-eslint/no-explicit-any */
import TrackTablehead from "./TrackTablehead";
import Table from "../../../utils/Table";
import { ArrowLeft2, ArrowRight2, More } from "iconsax-react";
import React, { useEffect, useState } from "react";
import StockDetails from "./StockDetails";
import { TrackStockServices } from "../../../utils/Services/TrackStockServices";
import { toast } from "react-toastify";
import { useQuery } from "react-query";
import { useUserAuthStore } from "../../../store/auth";
import ProductCard from "../../../utils/ProductCard";
import logo from "../../../../public/Favicon.png";
import TrackstockDropdown from "./TrackstockDropdown";
import AdjustProduct from "../AdjustProduct";
import RestockProduct from "../RestockProduct";
import { inventoryServices } from "../../../utils/Services/InventoryServices";
import AiemptyLogo from "../../../assets/Aiempty.svg"

interface tableData {
  date: string;
  productId: string;
  movementType: string;
  productImage: string;
  productName: string;
  quantity: number;
  reason: string;
  skuNumber: string;
}

interface tableDataProps {
  data: tableData[];
  dataIsLoading: any;
}

const TrackStockTable: React.FC<tableDataProps> = ({ data, dataIsLoading }) => {
  const [viewProductModal, setViewProductModal] = useState<any>(null);
  const [adjustProductModal, setAdjustProductModal] = useState<any>(null);
  const [restockProductModal, setRestockProductModal] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [productDetails, setProductDetails] = useState<any>(null);
  const [filterCategory, setFilterCategory] = useState("");
  const userDetail = useUserAuthStore((state) => state.user);
  const [productID, setProductID] = useState<any>(null);
  const [vendorId, setVendorId] = useState<any>(null);
  const rowsPerPage = 10;
  const [dropdownModal, setDropdownModal] = useState<{
    productId: any;
    position: { top: number; left: number };
  } | null>(null);

  const handleActionClick = (event: React.MouseEvent, productId: any) => {
    event.stopPropagation();
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    setDropdownModal({
      productId,
      position: { top: rect.bottom + window.scrollY, left: rect.left },
    });
  };

  const handleViewProduct = (row: any) => {
    setVendorId(userDetail?.vendorId || null);
    setProductID(row.productId || null);
    setViewProductModal(true);
  };
  const handleAdjustProduct = (row: any) => {
    setVendorId(userDetail?.vendorId || null);
    setProductID(row.productId || null);
    setAdjustProductModal(true);
  };

  const handleRestockProduct = (row: any) => {
    setVendorId(userDetail?.vendorId || null);
    setProductID(row.productId || null);
    setRestockProductModal(true);
  };
  // Query to fetch product details
  const { isLoading, refetch: refetchStockDetails } = useQuery(
    ["details", vendorId, productID],
    () =>
      TrackStockServices.GetStockTrackerDetails({
        vendorId: vendorId as string,
        productId: productID as string,
      }),
    {
      enabled: !!vendorId && !!productID,

      onSuccess: (response) => {
        setProductDetails(response?.data);
      },
      onError: (error: any) => {
        toast.error("Failed to load product details");
        console.error("Error fetching product details:", error);
      },
    }
  );
  const {
    data: productSelected,
    isError: detailsError,
    isLoading: detailsIsLoading,
  } = useQuery(
    ["product_details", vendorId, productID],
    () => inventoryServices.getProductDetails(vendorId, productID),
    {
      enabled: !!vendorId && !!productID,
    }
  );
  const selectedProduct = productSelected?.data;
  useEffect(() => {
    if (viewProductModal) {
      document.body.classList.add("no-scroll");
      document.title = "Stock Details - Huxxle"; // Set the title when adding a product
    } else {
      document.body.classList.remove("no-scroll");
      document.title = "Track Stock"; // Set the default title
    }

    return () => {
      document.body.classList.remove("no-scroll");
      document.title = "Track Stock"; // Reset title on cleanup
    };
  }, [viewProductModal]);

  const headers = [
    {
      title: "Product Name",
      key: "productName",
      render: (row: any) => (
        <div className="flex items-center gap-2">
          <img
            src={row.productImage}
            alt="product"
            className="h-[30px] w-[30px] rounded-full object-cover"
          />
          <p className="truncate font-bold">{row.productName}</p>
        </div>
      ),
    },
    {
      title: "SKU Number",
      key: "skuNumber",
      render: (row: any) => <div>{row.skuNumber}</div>,
    },
    {
      title: "Date",
      key: "date",
      render: (row: any) => <div className="truncate">{row.date}</div>,
    },
    {
      title: "Quantity",
      key: "quantity",
      render: (row: any) => <div>{row.quantity}</div>,
    },
    {
      title: "Movement Type",
      key: "movementType",
      render: (row: any) => (
        <div
          className={`text-center px-4 py-1 rounded-xl truncate w-fit ${
            row.movementType === "in"
              ? "text-[#28a745] bg-[#EAF7ED]"
              : row.movementType === "out"
              ? "text-[#DC3545] bg-[#FFF3E0]"
              : "text-[#FFC107] bg-[#F8D7DA]"
          }`}
        >
          {row.movementType}
        </div>
      ),
    },
    {
      title: "Reason",
      key: "reason",
      render: (row: any) => (
        <div className="truncate max-w-[141px]">{row.reason}</div>
      ),
    },
    {
      title: "Action",
      render: (row: any) => (
        <div className="relative">
          <More
            size="16"
            color="#1A1A1A"
            className="cursor-pointer"
            onClick={(event) => handleActionClick(event, row.productId)}
          />
        </div>
      ),
    },
  ];

  const handleNextPage = () => {
    if (currentPage < Math.ceil(data?.length / rowsPerPage)) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Filter the data based on the search query and category
  const filteredData = data?.filter((item) => {
    const matchesSearch = item.productName
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesCategory = filterCategory
      ? item.movementType.toLowerCase() === filterCategory.toLowerCase()
      : true;

    return matchesSearch && matchesCategory;
  });
  const paginatedData = filteredData?.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );

  return (
    <div className="my-10 bg-white border border-[#dedede] font-sora rounded-3xl">
      <TrackTablehead
        onSearch={(e: React.ChangeEvent<HTMLInputElement>) =>
          setSearchQuery(e.target.value)
        }
        onFilterChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
          setFilterCategory(e.target.value)
        }
      />{" "}
      <div className="relative">
        <div className="relative">
          {dataIsLoading ? (
            <div className="text-center py-10 flex justify-center">
              {" "}
              <img
                className="animate-spin w-[50px] h-[50px]"
                src={logo}
                alt="logo"
              />
              .
            </div>
          ) : (
            <>
              {data && data.length > 0 ? (
                <>
                  <div className="hidden md:block">
                    <Table
                      rows={paginatedData}
                      headers={headers}
                      showHead={true}
                      allowRowClick
                      onRowClick={handleViewProduct}
                    />
                  </div>
                  {paginatedData.map((product: any, index: any) => (
                    <div key={index} className="md:hidden mt-4 px-5">
                      <ProductCard
                        productName={product.productName}
                        productImage={product.productImage}
                        productSKU={product.skuNumber}
                        date={product.date}
                        movementType={product.movementType}
                        onClick={() => handleViewProduct(product)}
                      />
                    </div>
                  ))}
                </>
              ) : (
                <div className="flex mb-5 flex-col items-center justify-center text-center mt-8 space-y-4">
                  <img src={AiemptyLogo} alt="No Data" className="w-16 h-16" />
                  <p className="text-sm font-sora text-[#5B5B5B]">
                    You currently have no stock in inventory Add New Products
                    using the button above
                  </p>
                </div>
              )}
            </>
          )}
        </div>

        {/* {dataIsLoading ? (
          <div className="flex justify-center items-center py-20">
            {' '}
            LOADING . . .
          </div>
        ) : (
          <Table
            rows={paginatedData}
            headers={headers}
            showHead={true}
            allowRowClick
            onRowClick={handleViewProduct}
          />
        )} */}

        <div className="flex px-5 justify-between md:justify-end items-center pt-5 md:pr-5 pb-5">
          <div id="itemInfo" className="mr-[15px]">
            <p className="font-normal text-sm font-sora text-[#5A5A5A]">
              {`${(currentPage - 1) * rowsPerPage + 1} - ${Math.min(
                currentPage * rowsPerPage,
                data?.length
              )} of ${data?.length} items`}
            </p>
          </div>
          <div className="pagination-controls  flex gap-[10px]">
            <button
              id="prevBtn"
              className="bg-white hidden md:inline text-[#5a5a5a] border border-[#dedede] !font-inter py-2 px-4 rounded-full cursor-pointer text-sm transition-all duration-300 outline-none hover:bg-[#f5f5f5]"
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
            >
              Previous
            </button>

            <button
              id="prevBtn"
              className="bg-white md:hidden text-[#5a5a5a] border border-[#dedede] !font-inter py-2 px-4 rounded-full cursor-pointer text-sm transition-all duration-300 outline-none hover:bg-[#f5f5f5]"
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
            >
              <ArrowLeft2 />
            </button>
            <button
              id="nextBtn"
              className="bg-white hidden md:inline text-[#5a5a5a] border border-[#dedede] !font-inter py-2 px-4 rounded-full cursor-pointer text-sm transition-all duration-300 outline-none hover:bg-[#f5f5f5]"
              onClick={handleNextPage}
              disabled={currentPage === Math.ceil(data?.length / rowsPerPage)}
            >
              Next
            </button>

            <button
              id="nextBtn"
              className="bg-white md:hidden text-[#5a5a5a] border border-[#dedede] !font-inter py-2 px-4 rounded-full cursor-pointer text-sm transition-all duration-300 outline-none hover:bg-[#f5f5f5]"
              onClick={handleNextPage}
              disabled={currentPage === Math.ceil(data?.length / rowsPerPage)}
            >
              <ArrowRight2 />
            </button>
          </div>
        </div>
      </div>
      {dropdownModal && (
        <TrackstockDropdown
          productId={dropdownModal.productId}
          position={dropdownModal.position}
          onClose={() => setDropdownModal(null)}
          onViewProduct={(id) => {
            setDropdownModal(null);
            handleViewProduct({ productId: id });
          }}
          onAdjustProduct={(id) => {
            setDropdownModal(null);
            handleAdjustProduct({ productId: id });
          }}
          onRestockProduct={(id) => {
            setDropdownModal(null);
            handleRestockProduct({ productId: id });
          }}
        />
      )}
      {viewProductModal && (
        <StockDetails
          onClose={() => setViewProductModal(false)}
          isOpen={viewProductModal}
          productDetails={productDetails}
          isLoading={isLoading}
          refetch={refetchStockDetails}
          selectedProduct={productSelected}
          detailsIsLoading={detailsIsLoading}
          detailsError={detailsError}
        />
      )}
      {adjustProductModal && (
        <AdjustProduct
          onClose={() => setAdjustProductModal(false)}
          isOpen={adjustProductModal}
          productDetails={productDetails}
          refetch={() => {}}
        />
      )}
      {restockProductModal && (
        <RestockProduct
          onClose={() => setRestockProductModal(false)}
          isOpen={restockProductModal}
          // viewingId={viewingId}
          productDetails={selectedProduct}
          // isError={isError}
          refetch={() => {}}
          // stockRefetch={refetch}
        />
      )}
    </div>
  );
};

export default TrackStockTable;
