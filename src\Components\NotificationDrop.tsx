import { useState } from "react";
import {  useNavigate } from "react-router-dom";
import { RouteNames } from "../utils/RouteNames";
interface dropdown {
  title: string;
  content: React.ReactNode;
  buttonName: string;
  buttonAction?: () => void;
  created: string;
  readStatus: string;
  readAction: (id:string) => void;
  id: string;
}

const NotificationDrop: React.FC<dropdown> = ({
  title,
  content,
  buttonName,
  buttonAction,
  created,
  readStatus,
  readAction,id
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
    const navigate = useNavigate();
  const toggleDrop = (): void => {
    setIsOpen(!isOpen);
  };
   const toggleDropandRead = (id:string)=>{
     setIsOpen(!isOpen);
     if (!isOpen && readStatus === "Unread") {
       setTimeout(() => { readAction(id) }, 5000); // Mark as read
      }
  };
   const handleNavigation = (route: string): void => {
     navigate(route); // Navigate to the specified route
     setIsOpen(false); // Close modal
     if (buttonAction) {
       buttonAction(); // Execute any additional button action if provided
     }
   };
  return (
    <div
      className={`${
        isOpen
          ? "border-b-[3px] border-[#4F4CD8]"
          : "border-[1px] border-[#DCDCDC]"
      } bg-[#F7F7F7]   rounded-lg px-4 py-3 w-full`}
    >
      {/* Header Section */}
      <div
        className="flex justify-between items-center cursor-pointer"
        onClick={() => toggleDropandRead(id)}
      >
        <h2 className="text-[16px] font-sora font-semibold text-[#2A2A2A]">
          {title}
        </h2>
        {readStatus === "Unread" &&
          (isOpen ? (
            <p className="font-sora text-[#5B5B5B] text-[14px]">{created}</p>
          ) : (
            <span className="text-[#DC3545] text-2xl">●</span>
          ))}
        {readStatus === "read" &&
          (isOpen ? (
            <p className="font-sora text-[#5B5B5B] text-[14px]">{created}</p>
          ) : (
            ""
          ))}
      </div>

      {/* Content Section */}
      {isOpen && (
        <div onClick={toggleDrop} className="mt-2 cursor-pointer ">
          <p className="font-sora text-[#5B5B5B] text-[14px]">{content}</p>
          <button
            onClick={() =>
              handleNavigation(
                buttonName === "Manage Stock"
                  ? RouteNames.inventory
                  : RouteNames.order
              )
            }
            className="mt-3 px-4 py-2 bg-[#4F4CD8] text-white rounded-[8px] text-sm"
          >
            {buttonName}
          </button>
        </div>
      )}
    </div>
  );
};

export default NotificationDrop;
