/* eslint-disable @typescript-eslint/no-explicit-any */

import {  ArrowDown2, ArrowUp2 } from "iconsax-react";
import NotificationDrop from "./NotificationDrop";
import { useState } from "react";
import { useMutation } from "react-query";
import { useUserAuthStore } from "../store/auth";
import { Notifications } from "../utils/Services/Notification";
import { toast } from "react-toastify";

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  activeTab: string;
  setActiveTab: (tab: string) => void;
  data: any;
  unreadCount: any;
  refetch: any;
}

const NotificationModal: React.FC<NotificationModalProps> = ({
  isOpen,
  onClose,
  activeTab,
  setActiveTab,
  data,
  unreadCount,refetch
}) => {
  const formatTime = (e: any) => {
    if (!e) return "";
    const timePart = e.split("T")[1]; // "14:11:32Z"
    const [hours, minutes] = timePart.split(":"); // ["14", "11", "32Z"]
    return `${hours}:${minutes}`;
  };

  // Utility to format the date into "YYYY-MM-DD"
  const formatDate = (date:any) => date.toISOString().split("T")[0];

  // Get today's and yesterday's dates
  const today = new Date();
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);

  // Format dates for comparison
  const todayDate = formatDate(today);
  const yesterdayDate = formatDate(yesterday);

  // Categorize notifications
  const todayNotifications = data?.filter(
    (notification: any) => notification.created.split("T")[0] === todayDate
  );
  const yesterdayNotifications = data?.filter(
    (notification: any) => notification.created.split("T")[0] === yesterdayDate
  );
  const otherNotifications = data?.filter(
    (notification: any) =>
      notification.created.split("T")[0] !== todayDate &&
      notification.created.split("T")[0] !== yesterdayDate
  );

  const unreadToday = todayNotifications.filter(
    (n:any) => n.readStatus === "Unread"
  );
  const unreadYesterday = yesterdayNotifications.filter(
    (n:any) => n.readStatus === "Unread"
  );
  const unreadOlder = otherNotifications.filter(
    (n:any) => n.readStatus === "Unread"
  );


 console.log('unreadData:', unreadToday) 

  const renderNotifications = (notification: any) =>
    notification.map((notification: any) => (
      <NotificationDrop
        key={notification.id}
        id={notification.id}
        title={notification?.title}
        content={notification?.message}
        buttonName={notification?.action?.actionType}
        created={formatTime(notification?.created)}
        readStatus={notification.readStatus}
        readAction={readNotification}
        buttonAction={onClose}
       
      />
    ));
  
  const [dropYesterday, setDropYesterday] = useState<boolean>(false)
  const [older, setOlder] = useState<boolean>(false)
  const [todays, setTodays] = useState<boolean>(false)
  const handleYesterday = () => {
    setDropYesterday((prev)=> !prev)
  }
  const handleOlder = () => {
    setOlder((prev) => !prev);
  };
    const handleToday = () => {
      setTodays((prev) => !prev);
    };
  const user = useUserAuthStore((state) => state.user)
  const readMutate = useMutation(Notifications.readNotification)

  const readNotification = (id:string ) => {
    const payload = {
      vendorId: user.vendorId,
      notificationIds: [id]
    }
    try {
      readMutate.mutate(payload, {
        onSuccess:()=> {
          refetch()
        },
        onError: (error: any) => {
            toast.error(error?.response?.data?.error || error?.message);
        }
      })
    } catch (error) {
      console.log(error)
    }
  }


  const markAllAsRead = () => {
    // Collect all notification IDs
    const allNotificationIds = data?.map(
      (notification: any) => notification.id
    );

    const payload = {
      vendorId: user.vendorId,
      notificationIds: allNotificationIds, // Send all IDs
    };

    console.log("Mark All as Read Payload:", payload); // Debugging

    try {
      readMutate.mutate(payload, {
        onSuccess: (response) => {
          toast.success(
            response?.data?.message || "All notifications marked as read"
          );
          refetch()
        },
        onError: (error: any) => {
          toast.error(
            error?.response?.data?.error || error?.message || "Error occurred"
          );
        },
      });
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };

  return (
    <>
      <div className="fixed font-sora top-0 left-0 w-full h-full bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
        <div
          className={`fixed top-0 right-0 md:w-[580px] w-full bg-white backdrop-blur-[10px] overflow-y-auto h-full scrollbar-none hidescroll z-30 p-1 md:p-5 transition-transform duration-700 ease-in-out ${
            isOpen ? "translate-x-0" : "translate-x-full"
          }`}
        >
          <div>
            <span
              onClick={onClose}
              className="close-drawer text-4xl pr-4 font-bold cursor-pointer my-0 flex justify-end z-[999999] mt-4 sm:mt-0"
            >
              &times;
            </span>
            <div className="overflow-y-auto h-full scrollbar-none hidescroll">
              <div className="pl-6">
                <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold">
                  Notifications ({unreadCount})
                </h2>
                <p className="text-sm text-[#919191] font-sora">
                  See all notifications sent to your account here{" "}
                </p>
              </div>

              <div className="flex pl-6 mt-8 items-center justify-around border-b mb-4">
                <button
                  className={`pb-2 mr-4 font-semibold ${
                    activeTab === "all"
                      ? "text-[#4F4CD8] font-bold relative after:content-[''] after:absolute after:left-0 after:right-0 after:bottom-0 after:h-[5px] after:bg-[#4F4CD8] after:w-[100%]"
                      : "text-gray-500"
                  }`}
                  onClick={() => setActiveTab("all")}
                >
                  <p className="px-10 text-[14px] md:text-[16px]">
                    {" "}
                    All Notifications
                  </p>{" "}
                </button>
                <button
                  className={`pb-2 font-semibold ${
                    activeTab === "unread"
                      ? "text-[#4F4CD8] font-bold relative after:content-[''] after:absolute after:left-0 after:right-0 after:bottom-0 after:h-[5px] after:bg-[#4F4CD8] after:w-[100%]"
                      : "text-gray-500"
                  }`}
                  onClick={() => setActiveTab("unread")}
                >
                  <p className="px-10 text-[14px] md:text-[16px">Unread</p>
                </button>
              </div>

              <div className="border px-3 rounded-2xl">
                <div className="overflow-y-auto mt-7 px-1 md:px-6 h-full">
                  {activeTab === "all" ? (
                    <div>
                      <div>
                        <div>
                          <div className="flex justify-between">
                            <h3 className="text-[16px] font-semibold mb-8 md:mb-12">
                              All Notification
                            </h3>

                            {unreadCount > 0 && (
                              <h3
                                onClick={markAllAsRead}
                                className="text-[16px] cursor-pointer text-[#4F4CD8] font-semibold mb-2"
                              >
                                Mark All as Read
                              </h3>
                            )}
                          </div>
                          <div
                            onClick={handleToday}
                            className="flex cursor-pointer justify-between"
                          >
                            <h3 className="text-[16px] font-semibold mb-4">
                              Today
                            </h3>
                            <div className="cursor-pointer">
                              {todays ? (
                                <ArrowUp2 size={24} />
                              ) : (
                                <ArrowDown2 size={24} />
                              )}
                            </div>
                          </div>
                          {todays ? (
                            <div className="flex mt-2 flex-col gap-4">
                              {todayNotifications.length > 0 ? (
                                renderNotifications(todayNotifications)
                              ) : (
                                <p>No notifications for today.</p>
                              )}
                            </div>
                          ) : (
                            ""
                          )}
                        </div>

                        <div className="mt-5">
                          <div
                            onClick={handleYesterday}
                            className="flex cursor-pointer justify-between items-center"
                          >
                            <h3 className="text-[16px] font-semibold mb-4">
                              Yesterday
                            </h3>
                            <div className="cursor-pointer">
                              {dropYesterday ? (
                                <ArrowUp2 size={24} />
                              ) : (
                                <ArrowDown2 size={24} />
                              )}
                            </div>
                          </div>

                          {dropYesterday ? (
                            <div className="flex mt-2 flex-col gap-4">
                              {yesterdayNotifications.length > 0 ? (
                                renderNotifications(yesterdayNotifications)
                              ) : (
                                <p>No notifications for yesterday.</p>
                              )}
                            </div>
                          ) : (
                            ""
                          )}
                        </div>

                        <div className="mt-5 mb-10">
                          <div
                            onClick={handleOlder}
                            className="flex cursor-pointer justify-between items-center"
                          >
                            <h3 className="text-[16px] font-semibold mb-4">
                              Older Notifications
                            </h3>
                            <div className="cursor-pointer">
                              {older ? (
                                <ArrowUp2 size={24} />
                              ) : (
                                <ArrowDown2 size={24} />
                              )}
                            </div>
                          </div>

                          {older ? (
                            <div className="flex mt-2 flex-col gap-4">
                              {otherNotifications.length > 0 ? (
                                renderNotifications(otherNotifications)
                              ) : (
                                <p>No notifications older dates.</p>
                              )}
                            </div>
                          ) : (
                            ""
                          )}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex justify-between">
                        <h3 className="text-base md:text-[16px] font-semibold mb-8 md:mb-12">
                          Unread Notifications
                        </h3>

                        {unreadCount > 0 && (
                          <h3
                            onClick={markAllAsRead}
                            className="text-[16px] cursor-pointer text-[#4F4CD8] font-semibold mb-2"
                          >
                            Mark All as Read
                          </h3>
                        )}
                      </div>

                      <div>
                        <div>
                          <div
                            onClick={handleToday}
                            className="flex cursor-pointer justify-between"
                          >
                            <h3 className="text-[16px] font-semibold mb-4">
                              Today
                            </h3>
                            <div className="cursor-pointer">
                              {todays ? (
                                <ArrowUp2 size={24} />
                              ) : (
                                <ArrowDown2 size={24} />
                              )}
                            </div>
                          </div>
                          {todays ? (
                            <div className="flex mt-2 flex-col gap-4">
                              {unreadToday.length > 0 ? (
                                renderNotifications(unreadToday)
                              ) : (
                                <p>No notifications for today.</p>
                              )}
                            </div>
                          ) : (
                            ""
                          )}
                        </div>

                        <div className="mt-5">
                          <div
                            onClick={handleYesterday}
                            className="flex cursor-pointer justify-between items-center"
                          >
                            <h3 className="text-[16px] font-semibold mb-4">
                              Yesterday
                            </h3>
                            <div className="cursor-pointer">
                              {dropYesterday ? (
                                <ArrowUp2 size={24} />
                              ) : (
                                <ArrowDown2 size={24} />
                              )}
                            </div>
                          </div>

                          {dropYesterday ? (
                            <div className="flex mt-2 flex-col gap-4">
                              {unreadYesterday.length > 0 ? (
                                renderNotifications(unreadYesterday)
                              ) : (
                                <p>No notifications for yesterday.</p>
                              )}
                            </div>
                          ) : (
                            ""
                          )}
                        </div>

                        <div className="mt-5 mb-10">
                          <div
                            onClick={handleOlder}
                            className="flex cursor-pointer justify-between items-center"
                          >
                            <h3 className="text-[16px] font-semibold mb-4">
                              Older Notifications
                            </h3>
                            <div className="cursor-pointer">
                              {older ? (
                                <ArrowUp2 size={24} />
                              ) : (
                                <ArrowDown2 size={24} />
                              )}
                            </div>
                          </div>
                          {older ? (
                            <div className="flex mt-2 flex-col gap-4">
                              {unreadOlder.length > 0 ? (
                                renderNotifications(unreadOlder)
                              ) : (
                                <p>No notifications for older dates.</p>
                              )}
                            </div>
                          ) : (
                            ""
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default NotificationModal;
