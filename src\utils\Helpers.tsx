import { jwtDecode } from 'jwt-decode';

interface MyToken {
  name: string;
  exp: number;
}

/**
 * Validate if the token is valid or has expired
 * @param token the jwt token
 * @returns boolean
 */
export const isTokenValid = (token: string) => {
  try {
    if (!token) return false;
    const decoded = jwtDecode<MyToken>(token);
    if (Date.now() >= decoded.exp * 1000) {
      return false;
    }
    return true;
  } catch (error) {
    console.error('Token decoding failed:', error);
    return false;
  }
};

export const generateSku = (productName: string) => {
  let prefix;
  if (productName.includes(' ')) {
    const words = productName.split(' ');
    prefix = (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
  } else {
    prefix = productName.charAt(0).toUpperCase();
  }
  const randomNumber = Math.floor(10000 + Math.random() * 90000);
  return `${prefix}${randomNumber}`;
};
