<svg width="180" height="202" viewBox="0 0 180 202" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_2543_24609)">
<path d="M0 10.5762C0 5.05332 4.47715 0.576172 10 0.576172H170C175.523 0.576172 180 5.05332 180 10.5762V191.576C180 197.099 175.523 201.576 170 201.576H10C4.47716 201.576 0 197.099 0 191.576V10.5762Z" fill="#F5F5F5"/>
<g clip-path="url(#clip0_2543_24609)">
<g opacity="0.5">
<path d="M128.507 96.8391C126.684 119.632 107.697 126.504 107.697 126.504C106.299 129.973 104.481 138.788 104.481 138.788H79.1216C79.1216 138.788 77.3031 129.973 75.9058 126.504C75.9058 126.504 56.9136 119.632 55.0951 96.8391C53.8316 80.9833 65.1091 57.5811 91.6625 57.5762H91.9351C118.489 57.6009 129.771 81.018 128.507 96.8391Z" fill="#FF9900"/>
</g>
<g opacity="0.7">
<path d="M125.366 96.9729C123.706 117.823 106.334 124.091 106.334 124.091C105.056 127.262 103.396 138.793 103.396 138.793H80.2067C80.2067 138.793 78.5419 127.262 77.2635 124.091C77.2635 124.091 59.8915 117.808 58.2266 96.958C57.0721 82.4698 67.3933 61.0694 91.6724 61.0645H91.9251C116.209 61.0694 126.53 82.4698 125.366 96.9729Z" fill="#FF9900"/>
</g>
<path d="M78.9192 100.895L77.4579 101.168L84.9135 141.083L86.3747 140.81L78.9192 100.895Z" fill="white"/>
<path d="M104.672 100.897L97.2444 140.817L98.7058 141.089L106.134 101.169L104.672 100.897Z" fill="white"/>
<path d="M91.8013 114.241C89.532 114.241 87.4509 112.12 85.9892 109.41C85.7391 109.755 85.4177 110.043 85.0466 110.253C84.6756 110.463 84.2636 110.591 83.8387 110.628C80.1374 110.628 78.641 102.938 78.5766 102.611L79.0721 102.522C79.0721 102.596 80.5586 110.133 83.8437 110.133C84.4977 110.133 85.1567 109.677 85.7563 108.944C84.6181 106.738 83.9787 104.309 83.8833 101.828C83.8833 99.7028 84.6712 98.4839 86.0536 98.4839C87.6639 98.4839 88.5856 99.7821 88.5856 102.042C88.5059 104.519 87.7278 106.924 86.341 108.978C87.7234 111.634 89.6955 113.75 91.7914 113.75C93.377 113.75 94.9626 112.511 96.2508 110.282C96.3648 110.078 96.4788 109.87 96.5878 109.657C95.1013 108.062 94.2738 105.446 94.2738 102.953C94.2738 100.213 95.2945 98.4938 96.9346 98.4938C97.2501 98.4933 97.5595 98.5807 97.8282 98.7461C98.0968 98.9115 98.3141 99.1484 98.4558 99.4303C99.5657 101.348 98.9513 105.956 97.1824 109.563C97.872 110.259 98.8092 110.652 99.7887 110.658C102.613 110.658 104.744 103.077 104.773 103.003L105.269 103.137C105.175 103.464 103.004 111.154 99.8184 111.154C98.7635 111.144 97.752 110.733 96.9891 110.004L96.7067 110.5C95.7157 112.199 94.0707 114.241 91.8013 114.241ZM96.9594 98.9794C95.359 98.9794 94.7941 101.105 94.7941 102.943C94.7941 104.925 95.458 107.571 96.8405 109.182C98.4508 105.768 99.0553 101.417 98.0544 99.683C97.9585 99.4731 97.8043 99.2952 97.6102 99.1705C97.4161 99.0457 97.1902 98.9794 96.9594 98.9794ZM86.0586 98.9794C84.6018 98.9794 84.3838 100.763 84.3838 101.828C84.4802 104.145 85.0662 106.414 86.1031 108.488C87.3387 106.562 88.0241 104.334 88.0851 102.046C88.0851 100.897 87.8175 98.9794 86.0486 98.9794H86.0586Z" fill="white"/>
<path d="M107.682 138.837H75.9207V156.318H107.682V138.837Z" fill="#211F8F"/>
<path d="M104.06 156.299C102.192 159.614 98.6094 162.577 91.7815 162.577C84.9536 162.577 81.3761 159.604 79.4982 156.299H104.06Z" fill="#211F8F"/>
<path d="M104.06 156.299C103.505 157.289 102.811 158.196 101.999 158.989L80.1374 157.31C79.9075 156.99 79.6942 156.66 79.4982 156.319L104.06 156.299Z" fill="#100F46"/>
<path d="M108.608 142.172L74.9892 145.279V143.753L108.608 140.646V142.172Z" fill="#100F46"/>
<path d="M108.608 146.784L74.9892 149.891V148.37L108.608 145.258V146.784Z" fill="#100F46"/>
<path d="M108.608 151.398L74.9892 154.504V152.983L108.608 149.872V151.398Z" fill="#100F46"/>
<g opacity="0.5">
<path d="M63.0925 91.5328C64.2569 91.726 65.7681 85.7157 72.2294 78.7589C75.0983 75.672 85.9892 71.822 85.2063 68.2396C84.4235 64.6572 74.7068 68.3387 69.8163 74.0022C61.9429 83.1143 62.0222 91.3494 63.0925 91.5328Z" fill="white"/>
<path d="M120.411 92.7809C122.552 92.7809 122.557 89.4512 120.411 89.4512C118.266 89.4512 118.246 92.7809 120.411 92.7809Z" fill="white"/>
<path d="M109.912 117.814C111.081 118.71 116.764 113.463 119.326 107.205C121.457 102.012 121.803 95.447 120.391 95.0655C118.979 94.6839 118.033 103.692 116.313 107.012C114.143 111.209 108.658 116.862 109.912 117.814Z" fill="white"/>
</g>
<path d="M89.458 42.7998C89.2145 40.9271 89.08 39.0419 89.0551 37.1536C89.008 35.2673 89.0733 33.38 89.2506 31.5015C89.4951 33.374 89.6276 35.2594 89.6476 37.1477C89.6937 39.0338 89.6304 40.921 89.458 42.7998Z" fill="#A6A6A6"/>
<path d="M63.6677 49.9924C62.5195 48.4942 61.4611 46.9293 60.498 45.306C59.5169 43.6962 58.6306 42.0305 57.8438 40.3174C58.9915 41.8105 60.048 43.3715 61.0075 44.9919C61.9934 46.6041 62.8817 48.2739 63.6677 49.9924Z" fill="#A6A6A6"/>
<path d="M44.934 69.1169C43.1905 68.3942 41.4911 67.5692 39.8447 66.6463C38.1863 65.7443 36.5837 64.7432 35.0457 63.6484C36.7888 64.3744 38.4881 65.2014 40.135 66.125C41.7921 67.0271 43.3945 68.0261 44.934 69.1169Z" fill="#A6A6A6"/>
<path d="M38.2924 95.0489C36.4217 95.2923 34.5384 95.4268 32.6521 95.4518C30.7659 95.4991 28.8785 95.4339 27 95.2562C30.7468 94.7963 34.5313 94.7268 38.2924 95.0489Z" fill="#A6A6A6"/>
<path d="M45.4672 120.827C43.9679 121.972 42.4032 123.028 40.7808 123.991C39.1711 124.978 37.5032 125.866 35.7863 126.651C37.284 125.504 38.849 124.448 40.4727 123.487C42.0823 122.501 43.7502 121.612 45.4672 120.827Z" fill="#A6A6A6"/>
<path d="M135.042 119.915C138.525 121.381 141.842 123.214 144.937 125.384C141.46 123.907 138.147 122.075 135.048 119.915H135.042Z" fill="#A6A6A6"/>
<path d="M141.708 93.9825C143.578 93.7383 145.462 93.6058 147.348 93.5856C149.234 93.5333 151.122 93.5967 153 93.7752C151.13 94.0196 149.246 94.1522 147.36 94.1721C145.474 94.2244 143.586 94.1611 141.708 93.9825Z" fill="#A6A6A6"/>
<path d="M134.515 68.1921C136.011 67.0459 137.574 65.9895 139.196 65.0283C140.805 64.0417 142.473 63.1533 144.19 62.3682C142.693 63.5164 141.128 64.5729 139.504 65.5319C137.895 66.5174 136.229 67.4057 134.515 68.1921Z" fill="#A6A6A6"/>
<path d="M115.384 49.4583C116.112 47.7257 116.939 46.0364 117.861 44.3986C118.762 42.7407 119.761 41.1382 120.853 39.5996C120.13 41.3412 119.305 43.0386 118.382 44.683C117.477 46.3321 116.476 47.9266 115.384 49.4583Z" fill="#A6A6A6"/>
</g>
</g>
<defs>
<filter id="filter0_ii_2543_24609" x="-1" y="-0.423828" width="182" height="203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2543_24609"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.575 0 0 0 0 0.575 0 0 0 0 0.575 0 0 0 0.11 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2543_24609" result="effect2_innerShadow_2543_24609"/>
</filter>
<clipPath id="clip0_2543_24609">
<rect width="126" height="126" fill="white" transform="translate(27 38.5762)"/>
</clipPath>
</defs>
</svg>
