/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState } from 'react';
import DateInputPicker from '../../Constants/DateInputPicker';
import Dropdown from '../../Constants/DropDown';
import { useMutation } from 'react-query';
import { toast } from 'react-toastify';
import { useUserAuthStore } from '../../store/auth';
import { inventoryServices } from '../../utils/Services/InventoryServices';

const OrderReport = ({ closeModal, isOpen }: any) => {
  const [reportStartDate, setReportStartDate] = useState(new Date());
  const [reportEndDate, setReportEndDate] = useState(new Date());
  const [reportType, setReportType] = useState<string | null>(null);
  const [format, setFormat] = useState<string | null>(null);

  const user = useUserAuthStore((state) => state.user);
  const business = useUserAuthStore((state) => state.business);

  const modalRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen]);

  // Define mutations for each report type
  const salesReportMutation = useMutation(inventoryServices.salesReport);
  const stockReportMutation = useMutation(inventoryServices.stockReport);
  const valuationReportMutation = useMutation(
    inventoryServices.valuationReport
  );

  // Determine if any of the mutations are in the loading state
  const isLoading =
    salesReportMutation.isLoading ||
    stockReportMutation.isLoading ||
    valuationReportMutation.isLoading;

  const handleSelectReportType = (value: string) => {
    setReportType(value);
  };

  const handleSelectFormat = (value: string) => {
    setFormat(value);
  };

  const handleGenerateReport = async () => {
    if (!reportType || !format) {
      toast.error('Please select both the report type and format.');
      return;
    }

    const payload = {
      businessName: business.business_name,
      startDate: reportStartDate.toISOString().split('T')[0],
      endDate: reportEndDate.toISOString().split('T')[0],
      format: format,
      vendorId: user.vendorId,
      userEmail: user.userEmail,
    };

    const handleResponse = (response: any) => {
      if (response && response.data) {
        const { downloadUrl, message } = response.data;
        if (downloadUrl) {
          // Automatically trigger the download
          const link = document.createElement('a');
          link.href = downloadUrl;
          link.download = 'report';
          link.target = '_blank';
          link.click();
          toast.success('Report downloaded successfully.');
          closeModal();
        } else {
          // Show the message from the response
          toast.info(message || 'No data found for the given period.');
        }
      }
    };

    try {
      switch (reportType) {
        case 'Generate Sales Report':
          salesReportMutation.mutate(payload, {
            onSuccess: handleResponse,
            onError: (error) => {
              console.error('Error generating sales report:', error);
              toast.error('Failed to generate sales report.');
            },
          });
          break;
        case 'Generate Stock Movement':
          stockReportMutation.mutate(payload, {
            onSuccess: handleResponse,
            onError: (error) => {
              console.error('Error generating stock movement report:', error);
              toast.error('Failed to generate stock movement report.');
            },
          });
          break;
        case 'Generate Order Valuation':
          valuationReportMutation.mutate(payload, {
            onSuccess: handleResponse,
            onError: (error) => {
              console.error('Error generating order valuation report:', error);
              toast.error('Failed to generate order valuation report.');
            },
          });
          break;
        default:
          throw new Error('Invalid report type');
      }
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('Failed to generate the report.');
    }
  };

  return (
    <>
      <div
        ref={modalRef}
        className="fixed font-sora top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out"
        tabIndex={-1}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
          }
        }}>
        <div
          className={`fixed top-0 right-0 h-full md:w-[580px] w-full bg-white backdrop-blur-[10px] z-30 p-1 md:p-5 transition-transform duration-700 ease-in-out ${
            isOpen ? 'translate-x-0' : 'translate-x-full'
          }`}>
          <div>
            <span
              onClick={closeModal}
              className="close-drawer text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]">
              &times;
            </span>
            <div className="overflow-y-auto h-screen scrollbar-none hidescroll">
              <div className="pl-6">
                <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold">
                  Generate Order Reports
                </h2>
                <p className="text-sm text-[#919191] font-sora">
                  Provide the information below to adjust stock units
                </p>
              </div>
              <div className="p-6 rounded-2xl flex flex-col gap-6 last:mb-[200px]">
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0">
                  <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                    Order Report Type
                  </p>
                  <Dropdown
                    options={[
                      'Generate Sales Report',
                      'Generate Stock Movement',
                      'Generate Order Valuation',
                    ]}
                    onSelect={handleSelectReportType}
                    placeholder="Select Order Report Type"
                  />
                </div>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0">
                  <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                    Report Starts
                  </p>
                  <DateInputPicker
                    selectedDate={reportStartDate}
                    onDateChange={setReportStartDate}
                  />
                </div>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0">
                  <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                    Report Ends
                  </p>
                  <DateInputPicker
                    selectedDate={reportEndDate}
                    onDateChange={setReportEndDate}
                  />
                </div>
                <div className="p-0 mb-20 flex flex-col gap-2.5 relative last:border-b-0">
                  <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                    Format Type
                  </p>
                  <Dropdown
                    options={['pdf', 'csv']}
                    onSelect={handleSelectFormat}
                    placeholder="Select Format Type"
                  />
                </div>
                <div className="fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2 md:p-10">
                  <div className="flex w-full px-7.5 gap-2.5">
                    <button
                      onClick={closeModal}
                      className="bg-transparent text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2">
                      Back
                    </button>
                    <button
                      onClick={handleGenerateReport}
                      disabled={isLoading}
                      className={`${
                        isLoading ? 'bg-[#4e4cd898]' : 'bg-[#4f4cd8]'
                      } w-full text-[#fcfcfc] text-sm font-sora font-semibold border border-[#dcdcdc] px-6  h-[49px] rounded-2xl cursor-pointer flex-2`}>
                      {isLoading ? 'Generating Report...' : 'Generate Report'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default OrderReport;
