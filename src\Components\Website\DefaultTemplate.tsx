import {
  CallCalling,
  Facebook,
  Instagram,
  SearchNormal1,
  ShoppingBag,
  Sms,
  Sort,
  Whatsapp,
} from "iconsax-react";
import { Bi<PERSON>ogo<PERSON>ik<PERSON>, BiLogoTwitter } from "react-icons/bi";
import { products } from "../../utils/MockData";

const DefaultTemplate = ({ primaryColor, business, user, bg }: any) => {
  return (
    <div>
      {/* topheader */}
      <div
        className={`flex bg-[${primaryColor}] py-2 gap-2 md:gap-0 flex-col md:flex-row md:items-center justify-between px-5 md:px-4 font-sora`}
      >
        <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
          <div className="flex items-center gap-2">
            <CallCalling size="6" color="#FCFCFC" />
            <span className="text-[10px] text-white">
              {business?.business_phone}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Sms size="6" color="#FCFCFC" />
            <span className="text-[10px] text-white">{user.userEmail}</span>
          </div>
        </div>

        <div className="w-full md:w-auto mx-auto overflow-hidden">
          <div className="marquee">
            <span className="text-[10px] text-white font-bold mx-4">
              Holiday Mega Sales 25% OFF
            </span>
          </div>
        </div>

        <div className="flex gap-4 items-center">
          <Instagram size={16} color="white" />

          <BiLogoTiktok size={16} color="white" />

          <BiLogoTwitter size={16} color="white" />

          <Whatsapp size={16} color="white" />

          <Facebook size={16} variant="Outline" color="white" />
        </div>
      </div>
      {/* second header */}
      <div className="flex flex-wrap pt-4 items-center justify-between pb-3  md:py-2 px-5 md:px-4 bg-[#FCFCFC] font-sora">
        <div className="flex items-center w-full md:w-fit  justify-between gap-6 mb-4 md:mb-0">
          <div className="flex flex-row sm:flex-row items-center font-semibold gap-2 sm:gap-4 text-[#1A1A1A]">
            <img
              alt="businessLogo"
              className="rounded-full bg-contain w-[50px] h-[50px] py-2 px-2 text-xs "
              src={business?.business_image}
              width={50}
              height={50}
            />
            <p className="text-sm">{business?.business_name}</p>
          </div>
        </div>

        <div className="flex flex-row items-center gap-4 w-full md:w-auto">
          {/* Desktop search bar */}
          <div className="text-[#9B9B9B] md:w-[300px] flex items-center bg-[#F5F5F5] px-4 rounded-2xl flex-1">
            <SearchNormal1 size="20" color="#0A0A0A" />
            <input
              type="text"
              readOnly
              placeholder="What are you looking for?"
              className="w-full py-3 outline-none bg-[#F5F5F5] text-xs"
            />
          </div>

          <button
            type="button"
            className="text-[#4F4CD8] border border-[#B4B2EE] flex gap-2 items-center justify-center py-2.5 px-4 rounded-2xl"
          >
            <ShoppingBag size="20" color="#4f4cd8" />
            <span className="hidden md:block text-xs">Cart</span>
            <span className="bg-[#DC3545] text-[10px] rounded-full text-[#FCFCFC] px-1.5 py-0.5">
              2
            </span>
          </button>
        </div>
      </div>
      {/* banner */}
      <div
        style={{
          backgroundImage: `url(${bg})`,
          backgroundPosition: "center",
          backgroundSize: "cover",
        }}
        className="flex justify-center items-center text-center font-sora pt-[270px] md:pt-[50px] pb-[50px] md:pb-[114px] bg-[#fcfcfc] px-4 md:px-0"
      >
        <div className="max-w-[398px] md:pl-0">
          <h1 className="font-semibold text-[36px] leading-[44px] md:text-[32px] text-[#000000]">
            Yaay! You have Reached{" "}
            <span className="text-[#4F4CD8]">{business?.business_name}</span>
          </h1>
          <p className="text-[#7B7B7B] font-light text-[16px]  md:text-[11px]  mt-4">
            Find everything you need, from the latest trends to everyday
            essentials. What would you like to buy today?
          </p>
        </div>
      </div>
      {/* filter */}
      <div className="flex flex-col md:flex-row items-center border-b border-[#CCCCCC] justify-between pt-4 pb-4 gap-4">
        <div className="text-[#4F4CD8] flex items-center outline-none text-[16px] font-sora font-normal w-full md:max-w-[358px] px-2 h-[35px] border border-[#B4B2EE] rounded-xl">
          All Products
        </div>
        <div className="flex items-center bg-[#1A1A1A] rounded-xl px-4 h-[35px] gap-2 w-full md:w-auto">
          <Sort size="20" color="#FCFCFC" />
          <p className="text-white text-sm">Sort By</p>
        </div>
      </div>
      {/* product list */}
      <div className="grid w-full grid-cols-2 gap-6  md:grid-cols-3  ">
        {products.map((p: any) => (
          <div key={p.id}>
            <div>
              <div className=" pt-[5px]  bg-[#f5f5f5] flex-col flex items-center justify-center  px-0 py-0">
                {/* Product Image */}

                <img
                  src={p.image}
                  alt="product"
                  className="md:w-[200px] h-[150px] md:h-[200px] object-contain mx-auto"
                  width={200}
                  height={200}
                />
              </div>
              <p className="text-[13px] truncate md:text-[13px] my-2 text-[#2A2A2A] font-normal">
                {p?.name}
              </p>
              <div className="flex justify-between text-sm md:text-[14px]">
                <span className="text-[#5B5B5B]">{p.category}</span>
                <span className="text-black font-semibold">
                  {" "}
                  ₦ {p.price?.toLocaleString()}
                </span>
              </div>
            </div>

            <button
              className={`cursor-pointer text-[14px] hover:bg-[${primaryColor}] hover:text-[#F5F5F5] border-[${primaryColor}] transition-all duration-500 p-2  md:rounded-2xl mt-1 flex items-center justify-between w-full border 
                    `}
            >
              Add To Cart <ShoppingBag color={primaryColor} size={16} />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DefaultTemplate;
