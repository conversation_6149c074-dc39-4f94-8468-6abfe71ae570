/* eslint-disable @typescript-eslint/no-explicit-any */

import Dropdown from "../../Constants/DropDown";
import { useState, useEffect, useRef } from "react";
import InputField from "../../Constants/InputField";
import { orderServices } from "./../../utils/Services/Order";
import { useMutation, useQuery } from "react-query";
import { useUserAuthStore } from "../../store/auth";
import { toast } from "react-toastify";
import { Notifications } from "../../utils/Services/Notification";

interface CreateOrderProps {
  closeModal: () => void;
  isOpen: boolean;
}

interface AddCustomerProps {
  vendorId: string;
  userEmail: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  billingAddress: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  shippingAddress: string;
  shippingCity: string;
  shippingState: string;
  shippingZipCode: string;
  country: string;
  saveAddress: string;
  socialMediaHandle: string;
  firstName: string;
  lastName: string;
}

const AddCustomer: React.FC<CreateOrderProps> = ({ closeModal, isOpen }) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    return () => {
      document.body.classList.remove("no-scroll");
    };
  }, [isOpen]);
  // const [selectedState, setSelectedState] = useState<string | null>(null);
  const user = useUserAuthStore((state) => state.user);
  const [add, setAdd] = useState<AddCustomerProps>({
    vendorId: user.vendorId,
    userEmail: user.userEmail,
    customerId: "",
    customerName: "",
    customerEmail: "",
    customerPhone: "",
    billingAddress: "",
    billingCity: "",
    billingState: "",
    billingZipCode: "",
    shippingAddress: "",
    shippingCity: "",
    shippingState: "",
    shippingZipCode: "",
    country: "",
    saveAddress: "Yes",
    socialMediaHandle: "",
    firstName: "",
    lastName: "",
  });
  const addCustomer = useMutation(orderServices.addCustomer);

  const handleSelect = (selectedValue: string, type: "state" | "country") => {
    if (type === "state") {
      setAdd((prev) => ({ ...prev, billingState: selectedValue }));
      console.log(`Selected state: ${selectedValue}`);
    } else if (type === "country") {
      setAdd((prev) => ({ ...prev, country: selectedValue }));
      console.log(`Selected country: ${selectedValue}`);
    }
  };
  const handleShippingState = (
    selectedValue: string,
    type: "state" | "country"
  ) => {
    if (type === "state") {
      setAdd((prev) => ({ ...prev, shippingState: selectedValue }));
      console.log(`Selected state: ${selectedValue}`);
    } else if (type === "country") {
      setAdd((prev) => ({ ...prev, country: selectedValue }));
      console.log(`Selected country: ${selectedValue}`);
    }
  };

  const statesOfNigeria = [
    "Abia",
    "Adamawa",
    "Akwa Ibom",
    "Anambra",
    "Bauchi",
    "Bayelsa",
    "Benue",
    "Borno",
    "Cross River",
    "Delta",
    "Ebonyi",
    "Edo",
    "Ekiti",
    "Enugu",
    "Gombe",
    "Imo",
    "Jigawa",
    "Kaduna",
    "Kano",
    "Katsina",
    "Kebbi",
    "Kogi",
    "Kwara",
    "Lagos",
    "Nasarawa",
    "Niger",
    "Ogun",
    "Ondo",
    "Osun",
    "Oyo",
    "Plateau",
    "Rivers",
    "Sokoto",
    "Taraba",
    "Yobe",
    "Zamfara",
    "Federal Capital Territory (FCT)",
  ];

  // State to track the toggle switch state
  const [isToggled, setIsToggled] = useState<boolean>(false);

  // Function to handle the toggle
  const handleToggle = () => {
    setIsToggled((prev) => {
      const newToggleState = !prev;

      // If toggling to 'true', copy billing to shipping
      if (newToggleState) {
        setAdd((prevAdd) => ({
          ...prevAdd,
          shippingAddress: prevAdd.billingAddress,
          shippingCity: prevAdd.billingCity,
          shippingState: prevAdd.billingState,
          shippingZipCode: prevAdd.billingZipCode,
        }));
      }

      return newToggleState;
    });
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setAdd((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

   const { refetch: notificationRefetch } = useQuery(
     ["notifications", user.vendorId],
     () => Notifications.getNotification(user.vendorId),
     {
       enabled: !!user.vendorId,
       onSuccess: () => {},
       onError: (error: any) => {
         console.log("notification error:", error);
       },
     }
   );

  const handleSubmit = async () => {
    const payload = {
      vendorId: add.vendorId,
      userEmail: add.userEmail,
      customerId: add.customerPhone,
      customerName: add.customerName,
      customerEmail: add.customerEmail,
      customerPhone: add.customerPhone,
      billingAddress: add.billingAddress,
      billingCity: add.billingCity,
      billingState: add.billingState,
      billingZipCode: add.billingZipCode,
      shippingAddress: isToggled ? add.billingAddress : add.shippingAddress,
      shippingCity: isToggled ? add.billingCity : add.shippingCity,
      shippingState: isToggled ? add.billingState : add.shippingState,
      shippingZipCode: isToggled ? add.billingZipCode : add.shippingZipCode,
      country: add.country,
      saveAddress: "Yes",
      socialMediaHandle: add.socialMediaHandle,
      firstName: add.firstName,
      lastName: add.lastName,
    };

   const allFieldsFilled = Object.entries(payload)
     .filter(([key]) => key !== "billingZipCode" && key !== "socialMediaHandle" && key !== "shippingZipCode"&& key !== 'customerEmail') // Exclude these keys
     .every(
       ([, value]) => value !== "" && value !== null && value !== undefined
     );


    if (!allFieldsFilled) {
      toast.error("Please fill all fields before submitting.");
      return;
    }

    console.log(payload);
    try {
      const response: any = await addCustomer.mutateAsync(payload); 
      if (response) {
        toast.success("Customer added successfully!");
        notificationRefetch()
        closeModal();
      } else {
        throw new Error("No response returned from API");
      }
    } catch (error) {
      console.error(error);
      toast.error("Failed to add customer");
    }
  };
  const modalRef = useRef<HTMLDivElement | null>(null);

  // Focus on the modal when it opens
  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen]);

  return (
    <div
      ref={modalRef}
      tabIndex={-1}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          e.preventDefault();
        }
      }}
      className="fixed font-sora  bg-black/10 top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-500 ease-in-out"
    >
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-1 md:p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? "translate-x-0 " : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="overflow-y-auto h-screen scrollbar-none hidescroll ">
            <div className="pl-6 ">
              <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                Add New Customer
              </h2>
              <p className=" text-sm text-[#919191] font-sora ">
                Provide your customer information below
              </p>
            </div>
            <div>
              <div className="p-6 rounded-2xl flex flex-col gap-6 last:mb-[200px]">
                <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                  Customer Profile
                </h4>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Name
                  </p>
                  <InputField
                    id="customer-name"
                    placeholder="Enter customer's name"
                    onChange={handleChange}
                    value={add.customerName}
                    name="customerName"
                  />
                </div>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Phone Number
                  </p>
                  <InputField
                    id="customer-phone;"
                    placeholder="************"
                    onChange={handleChange}
                    value={add.customerPhone}
                    name="customerPhone"
                  />
                </div>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Email (Required)
                  </p>
                  <InputField
                    id="customer-email"
                    placeholder="Enter customer's email"
                    onChange={handleChange}
                    value={add.customerEmail}
                    name="customerEmail"
                  />
                </div>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Social Media Handle (optional)
                  </p>
                  <InputField
                    id="customer-social"
                    placeholder="Enter any of the customer social media handle"
                    onChange={handleChange}
                    name="socialMediaHandle"
                    value={add.socialMediaHandle}
                  />
                </div>
                <div className="flex justify-between items-center">
                  <h4 className="font-semibold font-sora text-[#2a2a2a] md:text-base">
                    Customer Billing Address
                  </h4>
                  <div className="flex items-center md:gap-2">
                    <p className="text-[12px] text-primary-neutralt2">
                      same as shipping address
                    </p>
                    <div
                      className={`w-10 h-6 flex items-center bg-gray-300 rounded-full p-1 cursor-pointer ${
                        isToggled ? "bg-purple-600" : "bg-gray-300"
                      }`}
                      onClick={handleToggle}
                    >
                      {/* Circle inside the toggle */}
                      <div
                        className={`bg-white w-4 h-4 rounded-full shadow-md transform ${
                          isToggled ? "translate-x-5" : "translate-x-0"
                        } transition`}
                      ></div>
                    </div>
                  </div>
                </div>

                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Select Country
                  </p>
                  <Dropdown
                    options={["Nigeria"]}
                    onSelect={(selectedCountry) =>
                      handleSelect(selectedCountry, "country")
                    }
                    value={add.country}
                  />
                </div>

                <div className="p-0 flex items-center gap-2.5 relative last:border-b-0 ">
                  <div className="w-full">
                    <p className="text-[14px] font-sora mb-[5px] font-normal text-[#5b5b5b]">
                      First Name
                    </p>
                    <InputField
                      id="first-name"
                      placeholder="Enter First Name"
                      className="w-full"
                      onChange={handleChange}
                      name="firstName"
                      value={add.firstName}
                    />
                  </div>
                  <div className="w-full">
                    <p className="text-[14px] font-sora mb-[5px] font-normal text-[#5b5b5b]">
                      Last Name
                    </p>
                    <InputField
                      id="last-name"
                      placeholder="Enter Last Name"
                      className="w-full"
                      onChange={handleChange}
                      value={add.lastName}
                      name="lastName"
                    />
                  </div>
                </div>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Address
                  </p>
                  <InputField
                    id="address"
                    placeholder="Enter Street Address"
                    onChange={handleChange}
                    value={add.billingAddress}
                    name="billingAddress"
                  />
                </div>

                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    City
                  </p>
                  <InputField
                    id="city"
                    placeholder="Enter City Name"
                    onChange={handleChange}
                    value={add.billingCity}
                    name="billingCity"
                  />
                </div>
                <div className="p-0 flex-col flex md:flex-row md:space-y-0 space-y-4 items-center gap-2.5 relative last:border-b-0 ">
                  <div className="w-full">
                    <p className="text-[14px] font-sora mb-3 font-normal text-[#5b5b5b]">
                      State
                    </p>
                    <Dropdown
                      placeholder="Select State"
                      onSelect={(selectedState) =>
                        handleSelect(selectedState, "state")
                      }
                      options={statesOfNigeria}
                      value={add.billingState}
                    />
                  </div>
                  <div className="w-full">
                    <p className="text-[14px] font-sora mb-3 font-normal text-[#5b5b5b]">
                      Zip Code
                    </p>
                    <InputField
                      id="zip-code"
                      placeholder="Enter zip code"
                      className="w-full h-[49px]"
                      onChange={handleChange}
                      value={add.billingZipCode}
                      name="billingZipCode"
                    />
                  </div>
                </div>
                {!isToggled && (
                  <div>
                    <div className="flex justify-between items-center">
                      <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                        Customer Shipping Address
                      </h4>
                    </div>
                    <div className="p-0 flex mt-4  flex-col gap-2.5 relative last:border-b-0 ">
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        Address
                      </p>
                      <InputField
                        placeholder="Enter Street Address"
                        onChange={handleChange}
                        value={add.shippingAddress}
                        name="shippingAddress"
                      />
                    </div>

                    <div className="p-0 mt-4 flex flex-col gap-2.5 relative last:border-b-0 ">
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        City
                      </p>
                      <InputField
                        id="city"
                        placeholder="Enter City Name"
                        onChange={handleChange}
                        value={add.shippingCity}
                        name="shippingCity"
                      />
                    </div>
                    <div className="p-0 flex-col flex md:flex-row md:space-y-0 space-y-4 mt-4 items-center gap-2.5 relative last:border-b-0 ">
                      <div className="w-full">
                        <p className="text-[14px] mb-3 font-sora font-normal text-[#5b5b5b]">
                          State
                        </p>
                        <Dropdown
                          placeholder="Select State"
                          onSelect={(selectedState) =>
                            handleShippingState(selectedState, "state")
                          }
                          options={statesOfNigeria}
                          value={add.shippingState}
                        />
                      </div>
                      <div className="w-full ">
                        <p className="text-[14px] font-sora mb-3 font-normal text-[#5b5b5b]">
                          Zip Code
                        </p>
                        <InputField
                          id="zip-code"
                          placeholder="Enter zip code"
                          className="w-full h-[49px]"
                          onChange={handleChange}
                          value={add.shippingZipCode}
                          name="shippingZipCode"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2  md:p-10">
              <div className="flex w-full px-7.5 gap-2.5">
                <button
                  onClick={closeModal}
                  className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2"
                >
                  Back
                </button>
                <button
                  className={`${
                    addCustomer.isLoading ? "opacity-50 cursor-auto" : ""
                  } w-full text-[#fcfcfc] bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl cursor-pointer flex-2 `}
                  onClick={handleSubmit}
                  disabled={addCustomer.isLoading}
                >
                  {addCustomer.isLoading
                    ? "Adding Customer ..."
                    : "Add Customer"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default AddCustomer;
