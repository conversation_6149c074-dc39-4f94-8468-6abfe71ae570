import React, { useState, useEffect } from "react";
import HomeButtons from "../../Constants/HomeButtons";
import { Link } from "react-router-dom";
import { RouteNames } from "../../utils/RouteNames";
import AiemptyLogo from "../../assets/Aiempty.svg";

type TableContent = {
  description: string;
  table: {
    headers: string[];
    rows: (string | number)[][];
  };
};

type DataType = {
  tableId: number;
  content: TableContent;
}[];

type PaginationTablesProps = {
  data: DataType;
  isLoading: boolean;
  recommendations: DataType;
};

const PaginationTables: React.FC<PaginationTablesProps> = ({
  data = [],
  isLoading,
  // recommendations = [],
}) => {
  const [currentPage, setCurrentPage] = useState<number>(0);

  useEffect(() => {
    if (!data || data.length === 0) return;

    const interval = setInterval(() => {
      setCurrentPage((prevPage) =>
        prevPage < data.length - 1 ? prevPage + 1 : 0
      );
    }, 20000);

    return () => clearInterval(interval);
  }, [data.length,data]);

  const currentTable = data[currentPage];

  const handlePageClick = (pageIndex: number) => {
    setCurrentPage(pageIndex);
  };

  if (isLoading) {
    return (
      <div className="flex-1 flex flex-col w-full bg-primary-baseWhite p-6 border-[1px] border-primary-neutral200 rounded-2xl">
        <div className="flex gap-10 justify-between items-start">
          <div>
            <h5 className="font-sora text-[18px] font-semibold leading-[21.6px] text-primary-baseBlack">
              AI Recommendations
            </h5>
            <p className="text-[#9B9B9B] font-sora text-[14px]">
              Loading recommendations...
            </p>
          </div>
        </div>
        <div className="mt-auto">
          <p className="animate-pulse mt-[30px] h-[300px] bg-slate-200"></p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex-1 flex flex-col w-full bg-primary-baseWhite p-6 border-[1px] border-primary-neutral200 rounded-2xl">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row gap-4 md:gap-10 justify-between items-start">
        <div>
          <h5 className="font-sora text-[18px] font-semibold leading-[21.6px] text-primary-baseBlack">
            AI Recommendations
          </h5>
          <p className="text-[#9B9B9B] font-sora text-[14px]">
            Recommendations tailored to you
          </p>
        </div>
        <div className="hidden md:block">
          <Link to={RouteNames.analytics}>
            <HomeButtons
              bgColor=""
              textColor="text-primary-baseBlack"
              title="View Analytics"
              border="border-[1px]"
              borderColor="border-primary-neutral200"
            />
          </Link>
        </div>
      </div>

      {/* Table Content */}
      {data && currentTable ? (
        <>
          <p className="text-primary-neutral800 font-sora text-[14px] mt-[20px]">
            {currentTable.content.description}
          </p>

          <div className="overflow-x-auto scrollbar-hide border-[1px] mt-4 border-primary-neutral300 rounded-[24px]">
            <table className="w-full text-center border border-primary-neutral100 rounded-2xl border-collapse">
              <thead className="bg-primary-neutral100">
                <tr>
                  {currentTable.content.table.headers.map((header, index) => (
                    <th
                      key={index}
                      className="px-[8px] truncate max-w-[80px] overflow-hidden whitespace-nowrap text-[12px] py-[12px] font-sora text-[#919191]"
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {currentTable.content.table.rows.map((row, rowIndex) => (
                  <tr
                    key={rowIndex}
                    className="border-b-[1px]  border-primary-neutral300"
                  >
                    {row.map((cell, cellIndex) => (
                      <td
                        key={cellIndex}
                        className="px-[8px] truncate max-w-[30px] overflow-hidden whitespace-nowrap py-[12px] text-[14px] font-sora text-primary-neutral800"
                      >
                        {typeof cell === "string" &&
                        (cell.startsWith("http") ||
                          cell.startsWith("data:image")) ? (
                          <img
                            src={cell}
                            alt="Data Cell"
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : (
                          cell
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="flex justify-center mt-4">
            {data.map((_, index) => (
              <span
                key={index}
                onClick={() => handlePageClick(index)}
                className={`cursor-pointer mx-1 rounded-full ${
                  index === currentPage
                    ? "bg-primary-purple w-[30px] h-2"
                    : "bg-primary-neutral300 w-2 h-2"
                }`}
              />
            ))}
          </div>
        </>
      ) : (
        <div className="flex flex-col items-center justify-center text-center mt-8 space-y-4">
          <img src={AiemptyLogo} alt="No Data" className="w-16 h-16" />
          <p className="text-sm font-sora text-[#5B5B5B]">
            We currently do not have enough data to give you any
            recommendations. Once you add products and manage them, we surely
            will have something for you.
          </p>
        </div>
      )}

      {/* Mobile Button Section */}
      <div className="mt-6 md:hidden mb-4">
        <Link to={RouteNames.analytics} className="w-full flex justify-center">
          <HomeButtons
            bgColor="bg-white"
            textColor="text-primary-baseBlack"
            title="View Analytics"
            border="border-[1px]"
            borderColor="border-primary-neutral200"
            customStyles="w-full text-center py-2 px-4 rounded-xl hover:bg-gray-100"
          />
        </Link>
      </div>
    </div>
  );
};

export default PaginationTables;
