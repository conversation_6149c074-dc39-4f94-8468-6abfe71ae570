/* eslint-disable @typescript-eslint/no-explicit-any */

import {
  ArrowRight2,
  Copy,
  ReceiptEdit,
  ToggleOffCircle,
  Trash,
} from 'iconsax-react';
import React, { useState } from 'react';
import { FormatPrice } from '../../utils/FormatPrice';
import EditPromo from './EditPromo';
import EligibleCustomer from './EligibleCustomer';
import EligibleProduct from './EligibleProducts';

interface PromoDetailsProps {
  isOpen: boolean;
  closeModal: () => void;
  handleDeleteModal: () => void;
  handleDeactivateModal: () => void;
  promo: any;
  refetch: any;
  customer: any;
  products: any;
  isLoading: any;
  refetchTable: any;
}

const PromoDetails: React.FC<PromoDetailsProps> = ({
  isOpen,
  closeModal,
  promo,
  customer,
  products,
  refetch,
  isLoading,
  refetchTable,
  handleDeactivateModal,
  handleDeleteModal,
}) => {
  const [edit, setEdit] = useState<boolean>(false);
  const [eligible, setEligible] = useState<boolean>(false);
  const [eligibleProduct, setEligibleProduct] = useState<boolean>(false);

  if (!isOpen) return null; // Render nothing if the modal is not open
  const handleEdit = () => {
    setEdit((prev) => {
      if (prev) {
        refetch();
      }
      return !prev;
    });
  };

  const handleCustomer = () => {
    setEligible((prev) => {
      if (prev) {
        refetch(); // Call refetch only when closing the modal
      }
      return !prev;
    });
  };

  const handleProduct = () => {
    setEligibleProduct((prev) => {
      if (prev) {
        refetch(); // Call refetch only when closing the modal
      }
      return !prev;
    });
  };

  return (
    <div className="fixed inset-0 z-50 font-sora bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white z-30 p-3 md:p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]">
            &times;
          </span>
          <div className="overflow-y-auto h-screen flex flex-col gap-4 scrollbar-none hidescroll ">
            <div className="pl-6">
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
              ) : (
                <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                  Promo/Sales Details
                </h2>
              )}
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px] mt-3"></div>
              ) : (
                <p className=" text-sm text-[#919191] font-sora ">
                  See and mange the details of your promo/sales
                </p>
              )}
            </div>
            <div className="border-[1px] gap-4 rounded-2xl w-full border-primary-neutral300 p-4 md:p-6 flex flex-col">
              <div className="bg-primary-purple rounded-2xl p-3 md:p-4 flex flex-col gap-2">
                <div className="flex border-b border-[#6866DE] items-start py-3 justify-between">
                  <div className="flex flex-col gap-2">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] md:w-[120px] w-[70px]"></div>
                    ) : (
                      <h4 className=" font-sora text-[16px] font-semibold text-primary-baseWhite">
                        {promo?.promoName}
                      </h4>
                    )}
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] md:w-[200px] w-[100px]"></div>
                    ) : (
                      <h4 className="font-sora text-[#FFB340] text-[24px] font-semibold">
                        {promo?.promoValue} % OFF
                      </h4>
                    )}
                  </div>

                  <div className="flex text-left flex-col gap-2">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] md:w-[200px] w-[100px]"></div>
                    ) : (
                      <h4 className=" font-sora text-[12px] text-primary-neutral200">
                        Discount Code
                      </h4>
                    )}
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] md:w-[120px] w-[70px]"></div>
                    ) : (
                      <h4 className="font-sora gap-2 flex items-center text-primary-baseWhite text-[16px]">
                        {promo?.promoCode}
                        <span>
                          {' '}
                          <Copy size={16} />{' '}
                        </span>
                      </h4>
                    )}
                  </div>
                </div>

                <div className="flex border-b border-[#6866DE] items-center py-3 justify-between">
                  <div className="flex flex-col gap-2">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] md:w-[200px] w-[100px]"></div>
                    ) : (
                      <p className="text-[14px] leading-[22.4px] font-sora text-primary-neutral100">
                        {promo?.promoDescription}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex  items-end py-3 justify-between">
                  <div className="flex text-left flex-col gap-2">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] md:w-[120px] w-[70px]"></div>
                    ) : (
                      <h4 className=" font-sora text-[12px] text-primary-neutral200">
                        Offer Valid from
                      </h4>
                    )}
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] md:w-[200px] w-[100px]"></div>
                    ) : (
                      <h4 className="font-sora font-semibold  text-primary-baseWhite text-[14px]">
                        {`${new Date(
                          promo?.startDate
                        ).toLocaleDateString()} - ${new Date(
                          promo?.endDate
                        ).toLocaleDateString()}`}
                      </h4>
                    )}
                  </div>
                  <div className="flex flex-col gap-2">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                    ) : (
                      <h4
                        className={`font-sora ${
                          promo?.promoStatus === 'Active'
                            ? 'bg-[#EAF7ED] text-[#28A745]'
                            : promo?.promoStatus === 'Expired'
                            ? 'bg-[#FAF2F2] text-[#dc3545]'
                            : ' bg-[#FFF2DF] text-[#FF9900]'
                        }  rounded-2xl text-[12px]  p-2`}>
                        {promo?.promoStatus}
                      </h4>
                    )}
                  </div>
                </div>
              </div>
              <div className="grid grid-flow-row grid-cols-1 md:grid-cols-2 items-center  gap-3 ">
                <div className="p-4 flex-1 flex flex-col gap-2 border-[1px] border-primary-neutral300 rounded-2xl">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                  ) : (
                    <h4 className="text-[12px] text-primary-neutralt2">
                      Redemptions
                    </h4>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <p className="text-[18px] text-primary-purple font-sora font-semibold">
                      {promo?.redemptions}
                    </p>
                  )}
                </div>
                <div className="p-4 flex-1 flex flex-col gap-2 border-[1px] border-primary-neutral300 rounded-2xl">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <h4 className="text-[12px] text-primary-neutralt2">
                      Sales
                    </h4>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <p className="text-[18px] text-[#28A745] font-sora font-semibold">
                      ₦ {FormatPrice(promo?.revenueGenerated)}
                    </p>
                  )}
                </div>
                <div className="p-4 flex-1 flex flex-col gap-2 border-[1px] border-primary-neutral300 rounded-2xl">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <h4 className="text-[12px] text-primary-neutralt2">
                      Unique Customers
                    </h4>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <p className="text-[18px] text-[#DC3545] font-sora font-semibold">
                      {promo?.uniqueCustomers}
                    </p>
                  )}
                </div>
                <div className="p-4 flex-1 flex flex-col gap-2 border-[1px] border-primary-neutral300 rounded-2xl">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <h4 className="text-[12px] text-primary-neutralt2">
                      Most Sold
                    </h4>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <p className="text-[18px] text-[#E28800] font-sora font-semibold">
                      {promo?.mostSoldProduct || '-'}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className=" rounded-2xl  flex flex-col gap-6 bg-[#F5F5F5] px-[24px] py-[16px]">
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
              ) : (
                <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                  Eligibility
                </h4>
              )}

              <div className=" flex flex-col gap-5 relative  ">
                <div className="flex flex-col  gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <p className="text-[12px] font-sora font-normal text-[#919191]">
                      Customers
                    </p>
                  )}

                  <div className="flex flex-col md:flex-row md:justify-between gap-2 md:items-center">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                    ) : (
                      <p className="text-[14px]  font-sora font-normal text-[#5b5b5b]">
                        This discount applies to{' '}
                        {promo?.eligibleCustomers.length}{' '}
                        {promo?.eligibleCustomers.length > 1
                          ? 'customers'
                          : 'customer'}
                      </p>
                    )}
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                    ) : (
                      <button
                        onClick={handleCustomer}
                        className="font-sora  w-full md:w-[200px] justify-between flex items-center  gap-1 text-primary-purple rounded-3xl py-2 px-3 border-[1px] border-primary-purple bg-[#E6E5F9]">
                        View Customers <ArrowRight2 />
                      </button>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-3 pb-6 ">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <p className="text-[12px] font-sora font-normal text-[#919191]">
                      Products
                    </p>
                  )}

                  <div className="flex flex-col md:flex-row md:justify-between gap-2 md:items-center">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                    ) : (
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        This discount applies to{' '}
                        {promo?.eligibleProducts.length}{' '}
                        {promo?.eligibleProducts.length > 1
                          ? 'products'
                          : 'product'}
                      </p>
                    )}
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                    ) : (
                      <button
                        onClick={handleProduct}
                        className="font-sora md:w-[200px] justify-between w-full flex items-center  gap-1 text-primary-purple rounded-3xl py-2 px-3 border-[1px] border-primary-purple bg-[#E6E5F9]">
                        View Products <ArrowRight2 />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className=" rounded-2xl mb-[220px] flex flex-col gap-6 bg-[#F5F5F5] px-[24px] py-[16px]">
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
              ) : (
                <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                  Promo/Sales Performance & Insights
                </h4>
              )}

              <div className=" flex flex-col gap-5 relative  ">
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <p className="text-[12px] font-sora font-normal text-[#919191]">
                      Total Redemptions
                    </p>
                  )}

                  <div className="flex justify-between items-center">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                    ) : (
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        {promo?.totalRedemption}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300 ">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <p className="text-[12px] font-sora font-normal text-[#919191]">
                      Total Savings for Customers
                    </p>
                  )}

                  <div className="flex justify-between items-center">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                    ) : (
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        ₦{FormatPrice(promo?.totalSavings) || '-'}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300 ">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <p className="text-[12px] font-sora font-normal text-[#919191]">
                      Total Revenue Generated
                    </p>
                  )}

                  <div className="flex justify-between items-center">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                    ) : (
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        ₦{FormatPrice(promo?.revenueGenerated)}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300 ">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <p className="text-[12px] font-sora font-normal text-[#919191]">
                      Peak Usage
                    </p>
                  )}

                  <div className="flex justify-between items-center">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                    ) : (
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        {promo?.peakUsage || '-'}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300 ">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <p className="text-[12px] font-sora font-normal text-[#919191]">
                      Per Customer Limit
                    </p>
                  )}

                  <div className="flex justify-between items-center">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                    ) : (
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        {promo?.perCustomerLimit || '-'}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-3 pb-6  ">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <p className="text-[12px] font-sora font-normal text-[#919191]">
                      Total Usage Limit
                    </p>
                  )}
                  <div className="flex justify-between items-center">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                    ) : (
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        {promo?.usageLimit === null
                          ? 'Unlimited'
                          : ` Valid for the first ${promo?.usageLimit} customers`}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className=" border-t-[1px] rounded-2xl fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2 ">
            <div
              id="edit-button"
              onClick={handleEdit}
              className=" flex flex-col items-center cursor-pointer">
              <div className="bg-[#F5F5F5] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                <ReceiptEdit size="24" color="#2A2A2A" />{' '}
              </div>
              <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                Edit
              </p>
            </div>
            {/* <div
              id="duplicate-button"
              className=" flex flex-col items-center cursor-pointer"
            >
              <div className=" bg-[#EAF7ED] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                <DocumentCopy size="24" color="#28A745" />{" "}
              </div>
              <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                Duplicate
              </p>
            </div> */}
            <div
              id="deactivate-button"
              className=" flex flex-col items-center cursor-pointer">
              <div
                onClick={handleDeactivateModal}
                className="bg-[#FFF2DF] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                <ToggleOffCircle size="24" color="#FF9900" />{' '}
              </div>
              <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                Deactivate
              </p>
            </div>
            <div
              id="delete-button"
              className=" flex flex-col items-center cursor-pointer"
              onClick={handleDeleteModal}>
              <div className="bg-[#FAF2F2] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                <Trash size="24" color="#DC3545" />
              </div>
              <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                Delete
              </p>
            </div>
          </div>
        </div>
      </div>
      {edit && (
        <EditPromo
          products={products}
          customers={customer}
          isOpen={edit}
          closeModal={handleEdit}
          pomo={promo}
          refetchTable={refetchTable}
        />
      )}
      {eligible && (
        <EligibleCustomer
          promoCode={promo.promoCode}
          isOpen={eligible}
          closeModal={handleCustomer}
          refetch={refetch}
          customer={promo?.eligibleCustomers}
        />
      )}
      {eligibleProduct && (
        <EligibleProduct
          isOpen={eligibleProduct}
          product={promo?.eligibleProducts}
          closeModal={handleProduct}
          refetch={refetch}
          promoCode={promo.promoCode}
        />
      )}

     
     
    </div>
  );
};

export default PromoDetails;
