import { Save2 } from 'iconsax-react';
import NotifyTypeBox from '../../Constants/NotifyTypeBox';

const AlertSetting = () => {
  return (
    <div className="mt-6 flex items-center justify-center w-full">
      <div
        style={{ boxShadow: '0px 5px 50px 0px #1A1A1A14' }}
        className="max-w-[540px] w-full  py-6 px-4 md:px-6 rounded-2xl">
        <h4 className="text-[#2A2A2A] text-lg font-semibold">
          All notifications
        </h4>
        <p className="text-xs md:text-sm text-[#5B5B5B] font-normal">
          Turn on/ off notifications based on your preference.{' '}
        </p>
        <div className="border p-2 md:p-6 rounded-2xl mt-6">
          <h2 className="hidden md:block font-bold text-[#1A1A1A] text-base mb-4">
            Notification Types
          </h2>
          <NotifyTypeBox text=" New Payment Alert" type="SMS Alert" />
          <NotifyTypeBox text="New Order Alert" type="In-app Alert" />
          <NotifyTypeBox text="New Promotion Alert" type="In-app Alert" />
          <NotifyTypeBox text="System Update" type="In-app Alert" />
          <NotifyTypeBox text=" New Payment Alert" type="In-app Alert" />
        </div>
        <button
          type="button"
          className="bg-[#4F4CD8] mt-6  flex gap-[31px] px-4 py-3 rounded-2xl items-center font-semibold text-[#FCFCFC] text-sm">
          <span>Save Changes </span>
          <Save2 size="16" />
        </button>
      </div>
    </div>
  );
};
export default AlertSetting;
