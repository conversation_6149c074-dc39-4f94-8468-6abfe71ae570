// import AlertSetting from './AlertSetting';
// import SecuritySettings from './SecuritySettings';
import BusinessProfile from './BusinessProfile';
import PaymentSettings from './PaymentSettings';
import PersonalProfile from './PersonalProfile';
import Tabs from './Tabs';

const ActiveState = () => {
  const tabs = [
    {
      title: 'Personal Profile',
      content: <PersonalProfile />,
    },
    {
      title: 'Business Profile',
      content: <BusinessProfile />,
    },
    {
      title: 'Payment Settings',
      content: <PaymentSettings/>,
    },
    // {
    //   title: 'Security Settings',
    //   content: <SecuritySettings/>,
    // },
    // {
    //   title: 'Alert Settings',
    //   content: <AlertSetting/>,
    // },
  ];

  return (
    <>
      <div className="mt-6">
        <Tabs tabs={tabs} />
      </div>
    </>
  );
};

export default ActiveState;
