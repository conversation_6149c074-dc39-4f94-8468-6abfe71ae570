/* eslint-disable @typescript-eslint/no-explicit-any */

import { Box1, BoxTick, Setting5 } from 'iconsax-react';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import ProductDetails from '../ProductDetails';
import RestockProduct from '../RestockProduct';
import AdjustProduct from '../AdjustProduct';
import EditProduct from '../EditProduct';
import DeleteProduct from '../DeleteProduct';

interface MovementHistory {
  date: string;
  movementType: string;
  quantity: number;
  updatedStockLevel: number;
  reason: string;
}

interface StockDetailsProps {
  onClose: () => void;
  isOpen: boolean;
  productDetails: {
    productId: string;
    productName: string;
    category: string;
    itemID: string;
    sku: string;
    productImage: string;
    totalUnitsInStock: number;
    movementHistory: MovementHistory[];
    insight: string;
  } | null;
  isLoading?: boolean;
  isError?: boolean;
  refetch: any;
  selectedProduct: any;
  detailsIsLoading: any;
   detailsError: any;
}

const StockDetails: React.FC<StockDetailsProps> = ({
  onClose,
  isOpen,
  productDetails,
  isLoading,
  isError,
  refetch,
  selectedProduct,
  detailsIsLoading,
  detailsError,
}) => {
  useEffect(() => {
    if (isError) {
      toast.error('Failed to load product details');
    }
  }, [isError]);
  const [viewProductModal, setViewProductModal] = useState(false);
  const [viewingId, setViewingId] = useState<string>('');
  // State for restock product modal
  const [restockProductModal, setRestockProductModal] = useState(false);
  // State for adjust product modal
  const [adjustProductModal, setAdjustProductModal] = useState(false);
  const [editProductModal, setEditProductModal] = useState(false);
  const [deleteProductModal, setDeleteProductModal] = useState(false);

  const handleViewProduct = (productId: string) => {
    setViewingId(productId);
    setViewProductModal(true);
  };
  // Handler to open the restock modal
  const handleRestockProduct = (productId: string) => {
    setViewingId(productId);
    setRestockProductModal(true);
  };
  // Handler to open the restock modal
  const handleEditProduct = (productId: string) => {
    setViewingId(productId);
    setEditProductModal(true);
  };

  const handleDeleteProduct = (productId: string) => {
    setViewingId(productId);
    setDeleteProductModal(true);
  };

  // Handler to open the adjust product modal
  const handleAdjustProduct = (productId: string) => {
    setViewingId(productId);
    setAdjustProductModal(true);
  };

  return (
    <div className="fixed inset-0 z-50 font-sora bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
        <span
          onClick={onClose}
          className="text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]">
          &times;
        </span>

        <div className="overflow-y-auto h-screen scrollbar-none hidescroll">
          <div className="text-2xl font-sora leading-7 font-semibold flex justify-between items-center">
            <>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px]"></div>
              ) : (
                <span>
                  {productDetails?.productName} ({productDetails?.sku})
                </span>
              )}
            </>

            {isLoading ? (
              <div className="w-[40px] h-[40px] rounded-full bg-slate-200 animate-pulse "></div>
            ) : (
              <div className="rounded-full border border-[#CCCCCC] p-1.5">
                <img
                  src={productDetails?.productImage}
                  alt="Product"
                  className="w-8 h-8"
                />
              </div>
            )}
          </div>
          {isLoading ? (
            <div className="w-[200px] h-[10px] bg-slate-200 animate-pulse rounded-2xl"></div>
          ) : (
            <p className=" font-sora text-sm text-[#919191]">
              Category: <span>{productDetails?.category}</span>
            </p>
          )}

          {isLoading ? (
            <p className="w-[200px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
          ) : (
            <div>
              {' '}
              <p className="text-[#4F4CD8] text-2xl font-semibold mt-6">
                {productDetails?.totalUnitsInStock}Pcs
              </p>
              <p className="text-[#919191] text-sm">Current stock level</p>
            </div>
          )}

          <div className="mt-6 bg-[#F5F5F5] py-6 px-4 rounded-2xl">
            {isLoading ? (
              <p className="w-[200px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
            ) : (
              <p className="text-[#2A2A2A] font-semibold text-base">
                Movement Information History
              </p>
            )}

            {productDetails?.movementHistory?.map((movement, index) => (
              <div key={index} className="bg-white p-4 rounded-2xl mt-6">
                <div className="flex justify-between items-center border-b py-3.5 ">
                  {isLoading ? (
                    <p className="w-[200px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                  ) : (
                    <p className="text-[#5B5B5B] text-sm font-normal">
                      {movement.date}
                    </p>
                  )}

                  {isLoading ? (
                    <p className="w-[80px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                  ) : (
                    <p
                      className={`w-fit px-4 py-1 rounded-full text-xs ${
                        movement.movementType === 'in'
                          ? 'bg-[#EAF7ED] text-[#28A745]'
                          : movement.movementType === 'out'
                          ? 'bg-[#FAF2F2] text-[#DC3545]'
                          : 'bg-[#E6E5F9] text-[#4F4CD8]'
                      }`}>
                      {movement.movementType.charAt(0).toUpperCase() +
                        movement.movementType.slice(1)}
                    </p>
                  )}
                </div>
                <div className="py-3">
                  {isLoading ? (
                    <p className="w-[80px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                  ) : (
                    <p
                      className={`${
                        movement.movementType === 'in'
                          ? 'text-[#28A745]'
                          : 'text-[#DC3545]'
                      } text-lg font-normal`}>
                      {movement.movementType === 'in'
                        ? `+${movement.quantity} units`
                        : `-${movement.quantity} units`}
                    </p>
                  )}

                  <div className="flex justify-between items-center border-b py-3.5">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className="text-[#5B5B5B] text-xs font-normal">
                        {movement.reason}
                      </p>
                    )}

                    {isLoading ? (
                      <p className="w-[150px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className="text-[#5B5B5B] text-xs font-normal">
                        Updated Stock Level to {movement.updatedStockLevel}{' '}
                        Units
                      </p>
                    )}
                  </div>
                </div>
                {isLoading ? (
                  <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                ) : (
                  <p className="text-[#5B5B5B] text-sm py-3 font-normal">
                    {movement?.reason}
                  </p>
                )}
              </div>
            ))}
          </div>
          <div className="mt-6 bg-[#F5F5F5] py-6 px-4 rounded-2xl mb-[260px] md:mb-44">
            {isLoading ? (
              <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
            ) : (
              <h2 className="font-semibold text-[#2A2A2A] text-base">
                Insight Section
              </h2>
            )}

            {isLoading ? (
              <p className="w-[150px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
            ) : (
              <p className="mt-6 text-sm font-normal text-[#5B5B5B] whitespace-pre-line">
                {productDetails?.insight}
              </p>
            )}
          </div>
        </div>

        <div className="fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2 md:p-5">
          <div
            onClick={() => handleViewProduct(productDetails?.productId || '')}
            className="flex flex-col items-center cursor-pointer">
            <div className="bg-[#FFF2DF] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
              <Box1 size="24" color="#FF9900" />
            </div>
            <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
              View Product
            </p>
          </div>
          <div
            onClick={() => handleAdjustProduct(productDetails?.productId || '')}
            className="flex flex-col items-center cursor-pointer">
            <div className="bg-[#E6E5F9] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
              <Setting5 size="24" color="#4F4CD8" />
            </div>
            <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
              Adjust
            </p>
          </div>
          <div
            onClick={() =>
              handleRestockProduct(productDetails?.productId || '')
            }
            className="flex flex-col items-center cursor-pointer">
            <div className="bg-[#EAF7ED] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
              <BoxTick size="24" color="#28A745" />
            </div>
            <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
              Restock
            </p>
          </div>
        </div>
      </div>
      {/* Product Details Modal */}
      {viewProductModal && (
        <ProductDetails
          onClose={() => setViewProductModal(false)}
          isOpen={viewProductModal}
          viewingId={viewingId}
          openEditProduct={handleEditProduct}
          openRestockProduct={handleRestockProduct}
          openAdjustProduct={handleAdjustProduct}
          openDeleteProduct={handleDeleteProduct}
          productDetails={selectedProduct}
          isLoading={detailsIsLoading}
          isError={detailsError}
        />
      )}
      {/* Restock Product Modal */}
      {restockProductModal && (
        <RestockProduct
          onClose={() => setRestockProductModal(false)}
          isOpen={restockProductModal}
          viewingId={viewingId}
          productDetails={selectedProduct}
          oldStock={productDetails?.totalUnitsInStock}
          isError={isError}
          refetch={() => {}}
          stockRefetch={refetch}
        />
      )}

      {/* Adjust Product Modal */}
      {adjustProductModal && (
        <AdjustProduct
          onClose={() => setAdjustProductModal(false)}
          isOpen={adjustProductModal}
          viewingId={viewingId}
          productDetails={productDetails}
          isError={isError}
          refetch={() => {}}
          stockRefetch={refetch}
        />
      )}
      {editProductModal && (
        <EditProduct
          onClose={() => setEditProductModal(false)}
          isOpen={editProductModal}
          productDetails={selectedProduct}
          refetch={() => {}}
        />
      )}
      {deleteProductModal && (
        <DeleteProduct
          onClose={() => setDeleteProductModal(false)}
          isOpen={deleteProductModal}
          viewingId={deleteProductModal}
          productDetails={productDetails}
          refetch={() => {}}
        />
      )}
    </div>
  );
};

export default StockDetails;
