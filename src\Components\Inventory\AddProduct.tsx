/* eslint-disable @typescript-eslint/no-explicit-any */
import { ArrowDown2, ArrowUp2, DocumentUpload, Warning2 } from "iconsax-react";
import aiStar from "../../assets/aiStar.svg";
import InputField from "../../Constants/InputField";
import ProductImageCard from "./ProductImageCard";
import { useEffect, useRef, useState } from "react";
import PreviewAddProduct from "./PreviewAddProduct";
import { generateSku } from "../../utils/Helpers";
import { toast } from "react-toastify";
import { inventoryServices } from "../../utils/Services/InventoryServices";
import { useUserAuthStore } from "../../store/auth";
import { useMutation } from "react-query";
import thumbnail from "../../assets/video_thumbnail.jpg";
import QuantityInput from "../../Constants/QuantityInput";

interface ProductDetails {
  SKUNumber: string;
  userEmail: string;
  productName: string;
  category: string;
  description: string;
  initialStockLevel: string;
  reorderLevel: string;
  supplierName: string;
  discountedPrice: number;
  storageLocation: string;
  purchasePrice: string;
  supplierEmail: string;
  sellingPrice: number;
  discount: number | null;
  batchNumber: string;
  notes: string;
  productImages: any[];
  countryCode: string;
  businessPhone: string;
}

const AddProduct = ({ closeModal, isOpen, refetch }: any) => {
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [coverImageId, setCoverImageId] = useState<number>(0);
  const [isPreview, setIsPreview] = useState(false);
  const [productDetails, setProductDetails] = useState<ProductDetails>({
    SKUNumber: "",
    userEmail: "",
    productName: "",
    category: "",
    description: "",
    initialStockLevel: "",
    reorderLevel: "",
    supplierName: "",
    discountedPrice: 0,
    storageLocation: "",
    purchasePrice: "",
    supplierEmail: "",
    sellingPrice: 0,
    discount: null,
    batchNumber: "",
    notes: "",
    businessPhone: "",
    productImages: [],
    countryCode: "+234",
  });
  const [previewData, setPreviewData] = useState<ProductDetails | null>(null);
  const writeWithAIMutation = useMutation(inventoryServices.aiDescription);
  const user = useUserAuthStore((state) => state.user);

  const modalRef = useRef<HTMLDivElement | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Focus on the modal when it opens

  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
     
  }, [isOpen]);
   

   


  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    if (name === "productName") {
      const sku = generateSku(value);
      setProductDetails((prevDetails) => ({
        ...prevDetails,
        productName: value,
        SKUNumber: sku,
      }));
    } else if (name === "countryCode") {
      setProductDetails((prevDetails) => {
        const currentPhoneWithoutCode = prevDetails.businessPhone.replace(
          prevDetails.countryCode,
          ""
        );
        return {
          ...prevDetails,
          countryCode: value,
          businessPhone: value + currentPhoneWithoutCode,
        };
      });
    } else if (name === "businessPhone") {
      setProductDetails((prevDetails) => ({
        ...prevDetails,
        businessPhone: prevDetails.countryCode + value,
      }));
    } else {
      setProductDetails((prevDetails) => ({
        ...prevDetails,
        [name]: value,
      }));
    }
    if (name === "purchasePrice" || name === "sellingPrice") {
      const updatedPurchasePrice =
        name === "purchasePrice" ? +value : +productDetails.purchasePrice;
      const updatedSellingPrice =
        name === "sellingPrice" ? +value : +productDetails.sellingPrice;

      if (updatedSellingPrice < updatedPurchasePrice) {
        setErrorMessage("Selling price cannot be less than purchase price");
      } else {
        setErrorMessage(null);
      }
    }
  };

  // const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const files = e.target.files;
  //   const maxImages = 4;

  //   if (files && files.length > 0) {
  //     const fileArray = Array.from(files);

  //     // Separate images and videos
  //     const imageFiles = fileArray.filter((file) =>
  //       file.type.startsWith("image/")
  //     );
  //     const videoFiles = fileArray.filter((file) =>
  //       file.type.startsWith("video/")
  //     );

  //     // Handle video upload
  //     if (videoFiles.length > 0) {
  //       if (videoFiles.length > 1) {
  //         toast.error("You can only upload one video.");
  //         return;
  //       }

  //       const videoFile = videoFiles[0];
  //       const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
  //       if (videoFile.size > maxSizeInBytes) {
  //         toast.error("Video size exceeds 10MB.");
  //         return;
  //       }

  //       const videoUrl = URL.createObjectURL(videoFile);

  //       setProductDetails((prevDetails) => {
  //         // Remove the last image if productImages is full
  //         const updatedImages =
  //           prevDetails.productImages.length >= maxImages
  //             ? [...prevDetails.productImages.slice(0, -1)]
  //             : [...prevDetails.productImages];

  //         return {
  //           ...prevDetails,
  //           productImages: [
  //             ...updatedImages,
  //             { file: videoFile, previewUrl: videoUrl, type: "video" },
  //           ],
  //         };
  //       });
  //     }

  //     // Handle image uploads
  //     if (imageFiles.length > 0) {
  //       // let availableSlots = maxImages;

  //       // Check if a video was uploaded, reduce available slots
  //       const newImages = imageFiles.map((file) => {
  //         const imageUrl = URL.createObjectURL(file);
  //         return {
  //           file,
  //           previewUrl: imageUrl,
  //         };
  //       });

  //       setProductDetails((prevDetails) => {
  //         const totalMediaCount =
  //           prevDetails.productImages.length + newImages.length;
  //         if (totalMediaCount > maxImages) {
  //           toast.error(
  //             `You can only upload a maximum of ${maxImages} images/videos.`
  //           );
  //           return prevDetails;
  //         }
  //         return {
  //           ...prevDetails,
  //           productImages: [...prevDetails.productImages, ...newImages],
  //         };
  //       });
  //     }
  //   }
  // };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    const maxImages = 4;
    const maxImageSize = 10 * 1024 * 1024;
    const maxVideoSize = 10 * 1024 * 1024;

    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);

    const validImages = fileArray.filter((file) => {
      if (file.type.startsWith("image/")) {
        if (file.size > maxImageSize) {
          toast.error(`Image "${file.name}" exceeds 3MB limit`);
          return false;
        }
        return true;
      }
      return false;
    });

    const validVideos = fileArray.filter((file) => {
      if (file.type.startsWith("video/")) {
        if (file.size > maxVideoSize) {
          toast.error(`Video "${file.name}" exceeds 10MB limit`);
          return false;
        }
        return true;
      }
      return false;
    });

    if (validVideos.length > 0) {
      if (validVideos.length > 1) {
        toast.error("You can only upload one video");
        return;
      }

      const videoFile = validVideos[0];
      const videoUrl = URL.createObjectURL(videoFile);

      setProductDetails((prevDetails) => {
        const hasExistingVideo = prevDetails.productImages.some(
          (img) => img.type === "video"
        );

        if (hasExistingVideo) {
          toast.error("Only one video is allowed");
          return prevDetails;
        }

        const updatedImages =
          prevDetails.productImages.length >= maxImages
            ? [...prevDetails.productImages.slice(0, -1)]
            : [...prevDetails.productImages];

        return {
          ...prevDetails,
          productImages: [
            ...updatedImages,
            { file: videoFile, previewUrl: videoUrl, type: "video" },
          ],
        };
      });
    }

    if (validImages.length > 0) {
      const newImages = validImages.map((file) => ({
        file,
        previewUrl: URL.createObjectURL(file),
        type: "image",
      }));

      setProductDetails((prevDetails) => {
        const totalMediaCount =
          prevDetails.productImages.length + newImages.length;

        if (totalMediaCount > maxImages) {
          const availableSlots = maxImages - prevDetails.productImages.length;
          toast.error(
            `You can only upload ${availableSlots} more file(s) (max ${maxImages} total)`
          );
          return prevDetails;
        }

        return {
          ...prevDetails,
          productImages: [...prevDetails.productImages, ...newImages],
        };
      });
    }
  };
  const areRequiredFieldsFilled = () => {
    const {
      SKUNumber,
      productName,
      category,
      description,
      initialStockLevel,
      // reorderLevel,
      // purchasePrice,
      sellingPrice,
    } = productDetails;
    return (
      SKUNumber &&
      productName &&
      category &&
      description &&
      initialStockLevel &&
      // reorderLevel &&
      // purchasePrice &&
      sellingPrice > 0
    );
  };
  const handlePreview = () => {
    setPreviewData(productDetails);
    setIsPreview(true);
  };

  const handleBack = () => {
    setIsPreview(false);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (areRequiredFieldsFilled() && !errorMessage) {
      handlePreview();
    }
  };

  const handleSetCover = (index: number) => {
    // Get the current product images
    const updatedImages = [...productDetails.productImages];

    // Move the selected image to the first position
    const selectedImage = updatedImages.splice(index, 1)[0];
    updatedImages.unshift(selectedImage);

    // Update the product details state with the new image order
    setProductDetails((prevDetails) => ({
      ...prevDetails,
      productImages: updatedImages,
    }));

    // Set the cover image index to the first element of the updated array
    setCoverImageId(0); // Always set the cover image to the first image
  };

  const handleAIDESC = async () => {
    const payload = {
      productName: productDetails.productName,
      category: productDetails.category,
      description: productDetails.description,
      vendorId: user.vendorId,
    };
    writeWithAIMutation.mutate(payload, {
      onSuccess: (response) => {
        const { enhancedDescription } = response.data;
        toast("Description Optimized");
        setProductDetails((prev) => ({
          ...prev,
          description: enhancedDescription,
        }));
      },
      onError: (error) => {
        console.error("Error optimizing description:", error);
        toast.error("Failed to optimize description.");
      },
    });
  };

  const handleRemoveImage = (index: number) => {
    setProductDetails((prevDetails) => {
      const updatedImages = [...prevDetails.productImages];
      updatedImages.splice(index, 1); // Remove the image at the specified index
      return {
        ...prevDetails,
        productImages: updatedImages,
      };
    });
  };
   const [optional, setOptional] = useState(false);
    const handleOptionalField = () => {
      setOptional((prev) => !prev);
    };

  const discountValue = productDetails.discount ?? 0;
  const discountedPrice =
    productDetails.sellingPrice -
    productDetails.sellingPrice * (discountValue / 100);
  productDetails.discountedPrice = discountedPrice;
   const originalThemeColor = useRef<string | null>(null);
 useEffect(()=>{
const meta = document.querySelector<HTMLMetaElement>(
      'meta[name="theme-color"]'
    );
    if (!meta) return;

    // on first ever run, capture the original content
    if (originalThemeColor.current === null) {
      originalThemeColor.current = meta.content;
    }

    // toggle between white (modal open) and the saved original
    meta.content = isPreview ? "#FFFFFF" : originalThemeColor.current;
 },[isPreview])
  return (
    <div
      ref={modalRef}
      className="fixed font-sora  top-0 left-0 w-full h-full  bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out"
      tabIndex={-1}
      onKeyDown={(e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
        }
      }}>
      <div
        className={`fixed top-0 right-0 h-full md:w-[580px] w-full bg-white backdrop-blur-[10px] z-30 p-1 md:p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-0 pr-4 flex justify-end z-[999999]">
            &times;
          </span>

          <form
            onSubmit={handleSubmit}
            encType="multipart/form-data"
            className=" h-screen scrollbar-none hidescroll ">
            {!isPreview ? (
              <div className="overflow-y-auto  h-[calc(100vh-60px)] scrollbar-none hidescroll">
                <div className="md:pl-6 px-2 ">
                  <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                    Add Product
                  </h2>
                  <p className=" text-sm text-[#919191] font-sora ">
                    Start selling by adding at least one product. It’s quick
                  </p>
                </div>
                <div className="p-4 md:p-6 rounded-2xl flex flex-col gap-6 last:mb-[200px]">
                  <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                    Product Information
                  </h4>
                  <div className="rounded-2xl flex flex-col  last:mb-[200px]">
                    <p className="text-[12px] font-sora mb-2 text-[#5B5B5B]">
                      Add nice photos of your product
                    </p>
                    <div className="image-upload-container">
                      <div id="imageList" className="">
                        {productDetails?.productImages?.map((image, index) => (
                          <ProductImageCard
                            key={index}
                            imageUrl={image.previewUrl}
                            imageName={image.file.name}
                            imageSize={`${(image.file.size / 1024).toFixed(
                              2
                            )} KB`}
                            imageNumber={index + 1}
                            isCover={index === coverImageId}
                            onSetCover={() => handleSetCover(index)}
                            onRemove={() => handleRemoveImage(index)}
                            isVideo={image.type === 'video'}
                            videoThumbnail={
                              image.type === 'video' ? thumbnail : undefined
                            }
                          />
                        ))}
                      </div>
                      {productDetails.productImages.length < 4 && (
                        <label
                          htmlFor="fileInput"
                          className="relative border border-dashed h-[200px] border-[#6200ea]
                          rounded-2xl p-4 text-center cursor-pointer
                          bg-[#F2F2FC] transition-colors duration-300
                          hover:bg-[#dedcff] flex flex-col justify-center items-center">
                          {/* transparent overlay input catches every click */}
                          <input
                            id="fileInput"
                            type="file"
                            accept="image/*,video/*"
                            multiple
                            ref={fileInputRef}
                            onChange={handleFileChange}
                            name="productImages"
                            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                            aria-label="Upload product images"
                          />

                          <DocumentUpload
                            size="32"
                            color="#4F4CD8"
                            className="mb-2.5"
                          />
                          <p className="text-base text-[#5b5b5b]">
                            Click here to upload or Drag & Drop
                          </p>
                          <p className="text-xs text-[#9b9b9b] mt-1">
                            Maximum file size is 10MB each
                          </p>
                        </label>
                      )}
                    </div>
                  </div>
                  <InputField
                    label="Product Name"
                    name="productName"
                    placeholder="Enter your product name"
                    value={productDetails.productName}
                    onChange={handleChange}
                  />
                  {/* <InputField
                                 name="SKUNumber"
                                 label="SKU Number"
                                 placeholder="SKU is auto generated"
                                 readOnly={true} className="hidden"
                                 value={productDetails.SKUNumber}
                               />
                               <div className=" hidden text-[#5b5b5b] text-xs items-center gap-1.5 -mt-4">
                                 <Warning2 size="16" color="#5B5B5B" />{" "}
                                 <p>This will be auto generated</p>
                               </div> */}
                  <InputField
                    name="category"
                    label="Category"
                    placeholder="Enter category for this product"
                    value={productDetails.category}
                    onChange={handleChange}
                  />

                  <div className="flex flex-col md:flex-row gap-4  items-center w-full">
                    {/* <div className="flex w-full flex-col gap-2">
                                   <p className="font-sora text-[14px] text-[#5B5B5B]">How much did you buy it? </p>
                                    <div className="flex items-center h-[48px] gap-4 border-[1px] border-[#dcdcdc] rounded-xl">
                                                     <p className="px-4 h-full flex items-center border-r-[1px] border-[#cccccc] bg-[#E9ECEF] rounded-l-xl">₦</p>
             
                                   <input
                                 placeholder="Enter buying price"
                                 name="purchasePrice"
                                 // type="number"
                                 value={productDetails.purchasePrice}
                                 onChange={handleChange}
                                                   className="w-full outline-none border-none"
             
                                 onKeyDown={(e) => {
                                   const allowedKeys = ["Backspace", "Tab", "ArrowLeft", "ArrowRight"];
                                   if (
                                     !/[0-9]/.test(e.key) &&
                                     !allowedKeys.includes(e.key)
                                   ) {
                                     e.preventDefault();
                                   }
                                 }}
                                 // price={true}
                               />
                                 </div>
                                 </div> */}

                    <div className="flex w-full flex-1 flex-col gap-2">
                      <p className="font-sora text-sm text-[#5B5B5B]">Price </p>

                      <div className="flex items-center h-[48px] gap-4 border-[1px] border-[#dcdcdc] rounded-xl">
                        <p className="px-4 h-full flex items-center border-r-[1px] border-[#cccccc] bg-[#E9ECEF] rounded-l-xl">
                          ₦
                        </p>

                        {/* <input
                                       // label="Selling Price"
                                       placeholder="How much is it?"
                                       name="sellingPrice"
                                       // type="number"
                                       className="w-full text-[14px] outline-none border-none"
                                       value={productDetails.sellingPrice.toString()}
                                       onChange={handleChange}
                                       onKeyDown={(e) => {
                                         const allowedKeys = [
                                           "Backspace",
                                           "Tab",
                                           "ArrowLeft",
                                           "ArrowRight",
                                         ];
                                         if (
                                           !/[0-9]/.test(e.key) &&
                                           !allowedKeys.includes(e.key)
                                         ) {
                                           e.preventDefault();
                                         }
                                       }}
                                       // price={true}
                                     /> */}
                        <input
                          // label="Selling Price"
                          placeholder="How much is it?"
                          name="sellingPrice"
                          inputMode="numeric"
                          pattern="[0-9]*"
                          className="w-full text-[14px] outline-none border-none"
                          value={productDetails.sellingPrice.toString()}
                          onChange={(e) => {
                            const numericValue = e.target.value.replace(
                              /[^0-9]/g,
                              ''
                            );
                            const event = {
                              ...e,
                              target: {
                                ...e.target,
                                name: e.target.name,
                                value: numericValue,
                              },
                            };

                            handleChange(event);
                          }}
                          onKeyDown={(e) => {
                            const allowedKeys = [
                              'Backspace',
                              'Delete',
                              'Tab',
                              'ArrowLeft',
                              'ArrowRight',
                              'ArrowUp',
                              'ArrowDown',
                              'Home',
                              'End',
                            ];
                            if (
                              !/^[0-9]$/.test(e.key) &&
                              !allowedKeys.includes(e.key)
                            ) {
                              e.preventDefault();
                            }
                          }}
                          onPaste={(e) => {
                            const paste = e.clipboardData.getData('text');
                            if (!/^\d*$/.test(paste)) {
                              e.preventDefault();
                            }
                          }}
                          // price={true}
                        />
                      </div>
                    </div>

                    <div className="w-full flex-1">
                      <QuantityInput
                        label="Quantity Available"
                        name="initialStockLevel"
                        placeholder=""
                        value={productDetails.initialStockLevel}
                        onChange={handleChange}
                      />
                    </div>
                  </div>
                  <div className="p-0 flex flex-col gap-2 relative">
                    <p className="text-sm font-sans font-normal text-[#5b5b5b]">
                      Description
                    </p>
                    <textarea
                      rows={12}
                      className=" text-[#7b7b7b] text-xs p-4 border border-[#cccccc] rounded-2xl outline-none"
                      placeholder="Enter description of the product "
                      name="description"
                      value={productDetails.description}
                      onChange={handleChange}></textarea>
                    {/* Loading overlay */}
                    {writeWithAIMutation.isLoading && (
                      <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 rounded-2xl">
                        <div className="flex flex-col items-center ">
                          <div className="  animate-spin">
                            <img src={aiStar} />
                          </div>
                          <p className="text-xs text-gray-500 mt-2">
                            Loading optimized description...
                          </p>
                        </div>
                      </div>
                    )}
                    <div className="absolute bottom-2 right-2 mt-5">
                      <button
                        type="button"
                        onClick={handleAIDESC}
                        disabled={writeWithAIMutation.isLoading}
                        className={`flex items-center bg-[#e6e5f9] p-2.5 gap-2.5 text-[#4f4cd8] rounded-2xl font-semibold cursor-pointer border border-[#4f4cd8]`}>
                        <img
                          className={`${
                            writeWithAIMutation.isLoading ? 'animate-spin' : ''
                          }`}
                          src={aiStar}
                          alt="ai"
                        />
                        <span className="text-xs sm:text-sm">
                          {writeWithAIMutation.isLoading
                            ? 'Optimizing with AI'
                            : productDetails.description
                            ? 'Optimize with AI'
                            : 'Write with AI'}
                        </span>
                      </button>
                    </div>
                  </div>
                </div>

                <div
                  onClick={handleOptionalField}
                  className={`w-full cursor-pointer ${
                    optional ? 'mb-[10px]' : 'mb-[300px]'
                  } px-4 flex items-center justify-between`}>
                  <p className="text-[16px] font-sora text-[#2A2A2A] font-semibold">
                    Add extra Information to this product? (optional)
                  </p>
                  <div className="flex items-center gap-2">
                    {optional ? (
                      <ArrowUp2 color="#4f4cd8" size={32} />
                    ) : (
                      <ArrowDown2 color="#4f4cd8" size={32} />
                    )}
                  </div>
                </div>

                {optional ? (
                  <div
                    className={`${
                      optional
                        ? 'translate-y-0 duration-500'
                        : 'translate-y-100 duration-500'
                    }`}>
                    <div className="p-6 rounded-2xl my-0 flex flex-col gap-6 last:mb-[200px]">
                      <div className="flex items-center gap-4 justify-between flex-col w-full md:flex-row">
                        <div className="flex w-full flex-col gap-2">
                          <p className="font-sora text-[14px] text-[#5B5B5B]">
                            Buying Price
                          </p>
                          <div className="flex items-center h-[48px] gap-4 border-[1px] border-[#dcdcdc] rounded-xl">
                            <p className="px-4 h-full flex items-center border-r-[1px] border-[#cccccc] bg-[#E9ECEF] rounded-l-xl">
                              ₦
                            </p>

                            <input
                              placeholder="How much does it cost you to buy it?"
                              name="purchasePrice"
                              // type="number"
                              value={productDetails.purchasePrice}
                              onChange={handleChange}
                              className="w-full text-[12px] outline-none border-none"
                              onKeyDown={(e) => {
                                const allowedKeys = [
                                  'Backspace',
                                  'Tab',
                                  'ArrowLeft',
                                  'ArrowRight',
                                ];
                                if (
                                  !/[0-9]/.test(e.key) &&
                                  !allowedKeys.includes(e.key)
                                ) {
                                  e.preventDefault();
                                }
                              }}
                              // price={true}
                            />
                          </div>
                        </div>

                        <div className="flex w-full md:w-fit flex-col gap-2">
                          <p className="font-sora text-[14px] text-[#5B5B5B]">
                            Discount
                          </p>
                          <div className="flex items-center h-[48px] gap-4 border-[1px] border-[#dcdcdc] rounded-xl">
                            <p className="px-4 h-full flex items-center border-r-[1px] border-[#cccccc] bg-[#E9ECEF] rounded-l-xl">
                              %
                            </p>

                            <input
                              //  label="  Apply Discount % (Optional)"
                              placeholder="E.g 10"
                              name="discount"
                              max={100}
                              //  min={0}
                              className="border-none outline-none text-[12px] w-full"
                              //  type="number"
                              onKeyDown={(e) => {
                                const allowedKeys = [
                                  'Backspace',
                                  'Tab',
                                  'ArrowLeft',
                                  'ArrowRight',
                                ];
                                if (
                                  !/[0-9]/.test(e.key) &&
                                  !allowedKeys.includes(e.key)
                                ) {
                                  e.preventDefault();
                                }
                              }}
                              value={(productDetails.discount ?? '').toString()}
                              onInput={(e) => {
                                const target = e.target as HTMLInputElement;
                                // Convert the value to a number before comparing
                                const inputValue = Number(target.value);

                                // If the value exceeds 100, set it back to 100
                                if (inputValue > 100) {
                                  target.value = '100';
                                }
                                if (inputValue < 0) {
                                  target.value = '0';
                                }

                                // Call the handleChange function to update the state
                                handleChange(
                                  e as React.ChangeEvent<HTMLInputElement>
                                );
                              }}
                            />
                          </div>
                        </div>
                      </div>
                      {/* <InputField
                                 label=" Quantity Available"
                                 name="initialStockLevel"
                                 placeholder="Enter how much you have in stock currently"
                                 type="number"
                                 value={productDetails.initialStockLevel}
                                 onKeyDown={(e) => {
                                   const allowedKeys = ["Backspace", "Tab", "ArrowLeft", "ArrowRight"];
                                   if (
                                     !/[0-9]/.test(e.key) &&
                                     !allowedKeys.includes(e.key)
                                   ) {
                                     e.preventDefault();
                                   }
                                 }}
                                 onChange={handleChange}
                               /> */}
                      <InputField
                        label="Discounted Price"
                        placeholder="Price will be deteremined by discount %"
                        value={productDetails.discountedPrice.toFixed(2)}
                        readOnly={true}
                        name="discountedPrice"
                        price={true}
                      />
                      <div className="flex flex-col gap-2">
                        <QuantityInput
                          label="Restock Level"
                          name="reorderLevel"
                          placeholder="Enter Number"
                          value={productDetails.reorderLevel}
                          onChange={handleChange}
                        />
                        <p className="text-[12px] font-sora items-center flex gap-2 text-[#5b5b5b]">
                          <Warning2 size={16} /> When should we remind you to
                          items are low?
                        </p>
                      </div>

                      {/* <InputField
                                     label="Reorder Stock Level"
                                     placeholder="How much stock should be left before reordering"
                                     name="reorderLevel"
                                     type="number"
                                     value={productDetails.reorderLevel}
                                     onKeyDown={(e) => {
                                       const allowedKeys = [
                                         "Backspace",
                                         "Tab",
                                         "ArrowLeft",
                                         "ArrowRight",
                                       ];
                                       if (
                                         !/[0-9]/.test(e.key) &&
                                         !allowedKeys.includes(e.key)
                                       ) {
                                         e.preventDefault();
                                       }
                                     }}
                                     onChange={handleChange}
                                   /> */}
                      <InputField
                        label="Supplier Name "
                        placeholder="Enter Supplier Name"
                        name="supplierName"
                        value={productDetails.supplierName}
                        onChange={handleChange}
                      />
                      <div className="p-0 flex flex-col gap-2 relative">
                        <p className="text-sm font-sans font-normal text-[#5b5b5b]">
                          Supplier Phone Number (Optional)
                        </p>
                        <div className="flex items-center">
                          <select
                            name="countryCode"
                            className="mr-2.5 flex-shrink-0 flex-[0.3] text-xs text-[#7b7b7b] h-[48px] px-3.5 border border-[#cccccc] rounded-2xl outline-none"
                            value={productDetails.countryCode}
                            onChange={handleChange}>
                            <option value="+234">🇳🇬 +234</option>
                            <option value="+1">🇺🇸 +1</option>
                          </select>
                          <input
                            className="text-xs flex-1 hover:border-primary-purple500 duration-300 h-[48px] text-[#7b7b7b] px-3.5 border border-[#cccccc] rounded-2xl outline-none "
                            type="number"
                            name="businessPhone"
                            placeholder="************"
                            onChange={handleChange}
                            value={productDetails.businessPhone.replace(
                              productDetails.countryCode,
                              ''
                            )}
                          />
                        </div>
                      </div>
                      <InputField
                        label="Supplier Email (Optional)"
                        placeholder="Enter Supplier Email"
                        name="supplierEmail"
                        value={productDetails.supplierEmail}
                        onChange={handleChange}
                      />
                      <InputField
                        label="Storage Location (Optional)"
                        placeholder="Enter where this product is stored (Shelf Number etc)"
                        name="storageLocation"
                        value={productDetails.storageLocation}
                        onChange={handleChange}
                      />
                    </div>
                    {/* <div >
                                   <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                                     Pricing Information
                                   </h4>
                                   <InputField
                                     label=" Purchase Price"
                                     placeholder="Enter the amount you purchased this product"
                                     name="purchasePrice"
                                     type="number"
                                     value={productDetails.purchasePrice}
                                     onChange={handleChange}
                                     onKeyDown={(e) => {
                                       const allowedKeys = [
                                         "Backspace",
                                         "Tab",
                                         "ArrowLeft",
                                         "ArrowRight",
                                       ];
                                       if (
                                         !/[0-9]/.test(e.key) &&
                                         !allowedKeys.includes(e.key)
                                       ) {
                                         e.preventDefault();
                                       }
                                     }}
                                     price={true}
                                   />
                                   <InputField
                                     label="Selling Price"
                                     placeholder="Enter the amount you want to sell this product"
                                     name="sellingPrice"
                                     type="number"
                                     value={productDetails.sellingPrice.toString()}
                                     onChange={handleChange}
                                     onKeyDown={(e) => {
                                       const allowedKeys = [
                                         "Backspace",
                                         "Tab",
                                         "ArrowLeft",
                                         "ArrowRight",
                                       ];
                                       if (
                                         !/[0-9]/.test(e.key) &&
                                         !allowedKeys.includes(e.key)
                                       ) {
                                         e.preventDefault();
                                       }
                                     }}
                                     price={true}
                                   />
                                   
                                   {errorMessage && (
                                     <p className="text-[#DC3545] border border-[#DC3545] bg-[#FAF2F2] py-1 px-2.5 rounded-full text-xs font-sora">
                                       {errorMessage}
                                     </p>
                                   )}
                                   <InputField
                                     label="  Apply Discount % (Optional)"
                                     placeholder="Enter Discount percentage"
                                     name="discount"
                                     max={100}
                                     min={0}
                                     type="number"
                                     value={productDetails.discount.toString()}
                                     onInput={(e) => {
                                       // Convert the value to a number before comparing
                                       const inputValue = Number(e.target.value);
             
                                       // If the value exceeds 100, set it back to 100
                                       if (inputValue > 100) {
                                         e.target.value = "100";
                                       }
                                       if (inputValue < 0) {
                                         e.target.value = "0";
                                       }
             
                                       // Call the handleChange function to update the state
                                       handleChange(e);
                                     }}
                                   />
                                 </div> */}
                    <div className="p-6 rounded-2xl my-0 flex flex-col gap-6 last:mb-[200px] mb-[100px]  bottom-add-product">
                      {/* <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                                     Additional Details
                                   </h4> */}
                      {/* <InputField
                                     label="Batch Number (Optional)"
                                     placeholder="Enter the batch number"
                                     name="batchNumber"
                                     type="number"
                                     value={productDetails.batchNumber}
                                     onChange={handleChange}
                                   /> */}
                      <div className="p-0 flex flex-col gap-2 relative pb-40">
                        <p className="text-sm font-sans font-normal text-[#5b5b5b]">
                          Notes
                        </p>
                        <textarea
                          rows={10}
                          className="text-[#7b7b7b] text-xs p-4 border border-[#cccccc] rounded-2xl outline-none"
                          placeholder="Provide additional info (E.g colors and sizes available)"
                          name="notes"
                          value={productDetails.notes}
                          onChange={handleChange}></textarea>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="mb-[200px]"></div>
                )}

                <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2 md:p-10">
                  <div className="flex flex-col md:flex-row w-full px-7.5 gap-2.5">
                    <button
                      onClick={closeModal}
                      className="bg-transparent order-2 md:order-1  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2">
                      Back
                    </button>
                    <button
                      type="submit"
                      disabled={!areRequiredFieldsFilled() || !!errorMessage}
                      className={` w-full text-[#fcfcfc] order-1 md:order-2 bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 h-[49px] rounded-2xl cursor-pointer flex-2 ${
                        !areRequiredFieldsFilled() || !!errorMessage
                          ? ' opacity-70 cursor-default'
                          : ''
                      }`}>
                      Save & Preview
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="overflow-y-auto h-[calc(100vh-60px)] scrollbar-none hidescroll">
                {isPreview && previewData && (
                  <PreviewAddProduct
                    productDetails={previewData}
                    coverImageId={coverImageId}
                    onBack={handleBack}
                    onSetCover={handleSetCover}
                    closeModal={closeModal}
                    refetch={refetch}
                  />
                )}
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
};
export default AddProduct;
