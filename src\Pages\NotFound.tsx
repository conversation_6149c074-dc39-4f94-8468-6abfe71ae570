import ellipse from '../assets/Ellipse.png';
import Logo from '../assets/Logo.svg';

const NotFound = () => {
  return (
    <div className="bg-[#FCFCFC] ">
      <div className="p-[14.3px] fixed w-full  border-b border-[#DCDCDC]">
        <img className="object-contain" src={Logo} alt="Logo" />
      </div>
      <div className="flex justify-center items-center h-screen pt-16 font-sora px-5 sm:px-0 ">
        <div className="text-center">
          <h4 className="text-[#1A1A1A] font-bold text-8xl font-lora">404</h4>
          <p className="text-lg font-semibold mt-6 mb-8">Page not found</p>
          <div className="flex justify-center">
            <img src={ellipse} alt="img" />
          </div>
          <div className="max-w-[676px] mt-10">
            <p className="mb-4 text-[#4A4A4A] font-semibold text-base">
              Oops! Looks like your inventory’s playing hide and seek
            </p>
            <p className="text-base text-[#6B6B6B] font-[300]">
              We couldn’t find the page you were looking for. Maybe it’s stuck
              behind a stack of orders . No worries—we’ll sort it out in no time
            </p>
          </div>
          <button
            type="button"
            className="bg-[#4F4CD8] text-[#FCFCFC] max-w-[189px] w-full py-4 rounded-2xl mt-8">
            Go to dashboard
          </button>
        </div>
      </div>
    </div>
  );
};
export default NotFound;
