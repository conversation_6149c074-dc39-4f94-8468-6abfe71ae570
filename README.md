# Huxxle WebApp - React + TypeScript + Vite

## Overview

The Huxxle WebApp is a comprehensive business management platform designed to streamline operations, enhance productivity, and drive growth. Built with React, TypeScript, and Vite, this application offers a user-friendly interface and a range of powerful features to help businesses manage their inventory, orders, analytics, and more.

## Features

*   **Progressive Web App (PWA) Support:** Installable on compatible browsers, providing a native app-like experience.
*   **iOS Installation:** Clear instructions for adding the app to the home screen on iOS devices.
*   **Automatic Redirection:** Checks for existing authentication tokens and redirects to the overview page.
*   **Loading Indicator:** Displays a loading animation while checking for authentication status.
*   **Dashboard Overview:** Provides a snapshot of key business metrics, including total sales, available products, and recent orders.
*   **Inventory Management:**
    *   Add, edit, and delete products.
    *   Track stock levels and movements.
    *   Restock and adjust inventory with detailed history.
    *   Categorize products and manage product information.
*   **Order Management:**
    *   Create, edit, and manage orders.
    *   Generate invoices.
    *   Track order status and customer information.
*   **Promotions and Sales:**
    *   Create and manage promotional offers and discount coupons.
    *   Set usage limits and track redemption rates.
*   **Analytics and Reporting:**
    *   Visualize sales data and product performance.
    *   Track key metrics such as churn rate and sales over time.
*   **AI-Powered Assistance:**
    *   Virtual business assistants (Burtons and Ven) to guide users and provide support.
    *   AI-optimized product descriptions.
*   **Account Settings:**
    *   Manage business and personal profiles.
    *   Configure business settings and preferences.
*   **User Authentication:** Secure login and logout functionality.
*   **Real-time Notifications:** Stay informed about important updates and events.

## Technologies Used

*   **React:** A JavaScript library for building user interfaces.
*   **TypeScript:** A superset of JavaScript that adds static typing.
*   **Vite:** A fast build tool and development server for modern web projects.
*   **React Router:** For navigation and routing within the application.
*   **React Query:** For data fetching, caching, and state management.
*   **Zustand:** For simple and scalable state management.
*   **iconsax-react:** For using consistent icons.
*   **Tailwind CSS:** A utility-first CSS framework for styling the application.
*   **react-toastify:** For displaying toast notifications.

## Setup Instructions

1.  **Clone the repository:**

    ```bash
    git clone <repository-url>
    cd <repository-directory>
    ```
2.  **Install dependencies:**

    ```bash
    npm install
    ```

3.  **Configure environment variables:**

    *   Create a `.env` file in the root directory.
    *   Add the necessary environment variables, such as API endpoints and authentication keys.

4.  **Start the development server:**

    ```bash
    npm run dev
    ```

5.  **Open the application in your browser:**

    ```
    http://localhost:<port>
    ```

    *Replace `<port>` with the port number specified by Vite.*

## Environment Variables

The application relies on several environment variables for configuration. Ensure these are properly set in your `.env` file:

*   `VITE_API_BASE_URL`: Base URL for the backend API.
*   `VITE_AUTH_API_URL`: URL for authentication-related API calls.
*   `VITE_VENDOR_ID`: Unique identifier for the vendor.

## Directory Structure

```
webapp/
├── src/
│   ├── App.tsx               # Main application component
│   ├── main.tsx              # Entry point for the React application
│   ├── components/           # Reusable React components
│   ├── pages/                # Application pages
│   ├── utils/                # Utility functions and helpers
│   ├── assets/               # Static assets (images, logos, etc.)
│   ├── styles/               # Global styles and CSS files
│   ├── Constants/            # Constant data
│   ├── store/                # Zustand store
│   ├── serviceWorkerRegistration.ts # Service worker
│   └── ...
├── public/               # Public assets
├── .env                  # Environment variables
├── README.md             # Project documentation
├── package.json          # Project dependencies and scripts
├── tsconfig.json         # TypeScript configuration
└── vite.config.ts        # Vite configuration
```

## Contributing

Contributions are welcome! Please follow these steps:

1.  Fork the repository.
2.  Create a new branch for your feature or bug fix.
3.  Make your changes and commit them with descriptive messages.
4.  Submit a pull request.

## License

[MIT](LICENSE)

## Contact

HUXXLE


## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default tseslint.config({
  languageOptions: {
    // other options...
    parserOptions: {
      project: ['./tsconfig.node.json', './tsconfig.app.json'],
      tsconfigRootDir: import.meta.dirname,
    },
  },
})
```

- Replace `tseslint.configs.recommended` to `tseslint.configs.recommendedTypeChecked` or `tseslint.configs.strictTypeChecked`
- Optionally add `...tseslint.configs.stylisticTypeChecked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and update the config:

```js
// eslint.config.js
import react from 'eslint-plugin-react'

export default tseslint.config({
  // Set the react version
  settings: { react: { version: '18.3' } },
  plugins: {
    // Add the react plugin
    react,
  },
  rules: {
    // other rules...
    // Enable its recommended rules
    ...react.configs.recommended.rules,
    ...react.configs['jsx-runtime'].rules,
  },
})
```
