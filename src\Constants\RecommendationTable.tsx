import React from "react";

// Define the types for table data
interface Table {
  headers: string[];
  rows: (string | number)[][];
}

interface Content {
  description: string;
  table: Table;
}

interface TableData {
  tableId: number;
  content: Content;
}

interface TablesProps {
  tables: TableData[];
}

const Tables: React.FC<TablesProps> = ({ tables }) => {
  return (
    <div className="overflow-x-auto w-full flex">
      <div className="flex w-full">
        {tables.map((tableData) => (
          <div
            key={tableData.tableId}
            className="min-w-full flex-shrink-0 w-full"
          >
            <h3 className="text-[16px] font-sora font-semibold mb-4">
              {tableData.content.description}
            </h3>
            <div className="border-[1px] border-primary-neutral300 rounded-[24px] w-full">
              <table className="w-full text-center border border-primary-neutral100 rounded-2xl border-collapse">
                <thead className="bg-primary-neutral100">
                  <tr>
                    {tableData.content.table.headers.map((header, index) => (
                      <th
                        key={index}
                        className="px-[12px] text-[12px] py-[16px] font-sora text-[#919191]"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {tableData.content.table.rows.map((row, rowIndex) => (
                    <tr
                      key={rowIndex}
                      className="border-b-[2px] border-primary-neutral300"
                    >
                      {row.map((cell, cellIndex) => (
                        <td
                          key={cellIndex}
                          className="px-[16px] py-[12px] font-sora text-[14px] text-primary-neutralt2"
                        >
                          {/* Render image if the cell contains a URL */}
                          {typeof cell === "string" && cell.includes("http") ? (
                            <img
                              src={cell}
                              alt={`Product ${cellIndex}`}
                              className="h-[30px] w-[30px] object-contain"
                            />
                          ) : (
                            cell
                          )}
                        </td>
                      ))}
                      {/* Conditionally render actions or extra content based on tableId */}
                      {tableData.tableId === 1 || tableData.tableId === 2 ? (
                        <td>
                          <button className="border-[1px] font-sora text-primary-neutralt1 border-primary-purple rounded-3xl px-[16px] py-[8px]">
                            Restock
                          </button>
                        </td>
                      ) : tableData.tableId === 3 ? (
                        <td>{row[row.length - 1]} Orders</td>
                      ) : null}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Tables;
