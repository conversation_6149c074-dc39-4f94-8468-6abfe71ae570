import { useState } from 'react';
import Vector from '../assets/Vector.svg';
import Comb1 from '../assets/Comb1.svg';
import Comb2 from '../assets/Comb2.svg';
import { Link } from 'react-router-dom';

const SideHoneyComb = () => {
  const quotes = [
    'The difference between a successful person and others is not a lack of strength, not a lack of knowledge, but rather a lack in will. – <PERSON> Lombardi',
    'Success is not the key to happiness. Happiness is the key to success. If you love what you are doing, you will be successful. – <PERSON>',
    'The only limit to our realization of tomorrow is our doubts of today. – <PERSON>',
  ];

  const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0);

  const nextQuote = () => {
    setCurrentQuoteIndex((prevIndex) =>
      prevIndex === quotes.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevQuote = () => {
    setCurrentQuoteIndex((prevIndex) =>
      prevIndex === 0 ? quotes.length - 1 : prevIndex - 1
    );
  };

  return (
    <div
      className="absolute flex flex-col items-center justify-center h-full w-full  top-0 bg-no-repeat bg-contain left-[80px]"
      style={{ backgroundImage: `url(${Vector})` }}>
      <div className="flex absolute top-7 lg:right-[190px] space-x-4 mb-8">
        <Link to='https://huxxle.com/terms-of-use' target='blank' className="text-primary-purple500 font-sora border bg-opacity-15 border-primary-purple500 p-2 bg-primary-purple200 rounded-[16px] font-medium">
          Terms of Use
        </Link>
        <button className="text-primary-purple500 font-sora border bg-opacity-15  border-primary-purple500 p-2 bg-primary-purple200 rounded-[16px] font-medium">
          Privacy Policy
        </button>
      </div>
      <h2 className="text-center absolute top-[45%] right-[230px] text-[40px] font-sora text-primary-baseWhite mb-8">
        Welcome Back,
        <br />
        We Have Missed You
      </h2>
      <div className="quote-container bg-primary-purple200 bg-opacity-30 p-4 rounded-[24px] absolute right-[190px]  bottom-[90px] mb-8">
        <p className="quote text-left font-sora text-[14px] italic text-primary-purple500 max-w-md">
          {quotes[currentQuoteIndex]}
        </p>
      </div>
      <div className="flex absolute bottom-[10px] w-[500px] right-[180px]  justify-between items-center space-x-4">
        <button
          onClick={prevQuote}
          className="cursor-pointer border-[2px] border-primary-purple200 rounded-full flex justify-center h-[64px] w-[64px] text-primary-purple200 text-5xl">
          ←
        </button>
        <div className="flex space-x-2">
          {quotes.map((_, index) => (
            <span
              key={index}
              className={`block  h-2 rounded-full ${
                index === currentQuoteIndex
                  ? 'bg-primary-purple500 w-[30px]'
                  : 'bg-primary-purple200 w-[8px]'
              }`}></span>
          ))}
        </div>
        <button
          onClick={nextQuote}
          className="cursor-pointer border-[2px] border-primary-purple200 rounded-full flex justify-center h-[64px] w-[64px] text-primary-purple200 text-5xl">
          →
        </button>
      </div>
      <div className="absolute top-0 right-[149px]">
        <img src={Comb1} className="w-[320px] h-[220px]" alt="honecomb" />
      </div>
      <div className="absolute top-[20%] right-[400px]">
        <img src={Comb2} className="w-[320px] h-[300px]" alt="honecomb" />
      </div>
    </div>
  );
};

export default SideHoneyComb;
