import React from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler, // Import Filler plugin
  ChartOptions,
} from "chart.js";

// Register the necessary components in Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler // Register Filler plugin
);

// Define the data type for sales
interface SalesData {
  dateTime: string;
  totalSales: number;
}

// Component props type
interface LineChartProps {
  salesOverTime: SalesData[];
}

const Chart: React.FC<LineChartProps> = ({ salesOverTime }) => {
  // Prepare data for the chart with just the month name
  const labels = salesOverTime?.map((sale) =>
    new Date(sale.dateTime).toLocaleString("default", { month: "short" })
  ); // X-axis (formatted month names like "Jan", "Feb", etc.)

  const salesData = salesOverTime?.map((sale) => sale.totalSales); // Y-axis (sales numbers)

  // Define the chart data
  const data = {
    labels,
    datasets: [
      {
        label: "Total Sales Over Time",
        data: salesData,
        borderColor: "#28A745", // Similar green color
       // Fill under the line
        fill: false,
        tension: 0.4, // Higher tension for a smoother curve
        pointRadius: 6, // Larger data points
        pointHoverRadius: 8, // Larger hover effect on points
        borderWidth: 2, // Thicker line
      },
    ],
  };

  // Define chart options to match the format
  const options: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio:true,
    plugins: {
      legend: {
        display: false, // Hides the legend to simplify the chart
      },
      title: {
        display: false, // Hide the title for a cleaner look
      },
      tooltip: {
        callbacks: {
          label: function (tooltipItem) {
            const rawValue = tooltipItem.raw as number; // Cast `raw` to number
            const formattedValue = (rawValue / 1000).toFixed(0) + "k"; // Format in 'k'
            return `${formattedValue} Sales`; // Display formatted sales in hundred thousands
          },
        },
      },
    },
    scales: {
      x: {
        type: "category", // ensures x-axis is treated as category labels
        grid: {
          display: false, // Remove gridlines on the x-axis for a cleaner look
        },
        ticks: {
          color: "#999", // Light color for axis labels
          font: {
            size: 12, // Slightly larger font size
            weight: "bold",
          },
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(200, 200, 200, 0.3)", // Light grid lines on y-axis
        },
        ticks: {
          callback: function (value) {
            const numericValue = Number(value); // Ensure value is treated as a number
            return (numericValue / 1000).toFixed(0) + "k"; // Format y-axis in hundred thousands with 'k'
          },
          color: "#999", // Light color for axis labels
          font: {
            size: 12, // Slightly larger font size
            weight: "bold",
          },
        },
      },
    },
  };

  return <Line data={data} options={options} />;
};

export default Chart;
