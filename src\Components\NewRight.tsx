import { Message2 } from "iconsax-react";
import background from "../assets/backgroundLogin.svg";
import { useEffect, useState } from "react";

function NewRight() {
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState<number>(0);
  const quotes = [
    "The difference between a successful person and others is not a lack of strength, not a lack of knowledge, but rather a lack in will. – <PERSON>",
    "Success is not the key to happiness. Happiness is the key to success. If you love what you are doing, you will be successful. – <PERSON>",
    "The only limit to our realization of tomorrow is our doubts of today. – <PERSON>",
  ];

  const nextQuote = () => {
    setCurrentQuoteIndex((prevIndex) =>
      prevIndex === quotes.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevQuote = () => {
    setCurrentQuoteIndex((prevIndex) =>
      prevIndex === 0 ? quotes.length - 1 : prevIndex - 1
    );
  };

  // Automatically change quotes every 2 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      nextQuote();
    }, 4000); // 4000ms = 4 seconds

    // Cleanup the interval on component unmount
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="w-full p-10 flex flex-col relative rounded-2xl h-full bg-gradient-to-b from-[#4F4CD8] to-[#2A2872]">
      <div className="z-10 flex justify-center md:justify-end">
        <p className="flex font-sora gap-1 text-[#F2F2FC] text-[12px] md:text-[14px]">
          <Message2 />
          Do you need help? Reach out to{" "}
          <a href="mailto:<EMAIL>" className="text-[#F2F2FC] cursor-pointer  underline">
            <EMAIL>
          </a>
        </p>
      </div>

      <div className="mt-auto flex flex-col gap-10 items-center">
        <h1 className="max-w-[636px] font-sora text-[#FCFCFC] text-[40px] px-4 text-center md:text-[32px] lg:text-[40px]">
          Managing Your Business Made Easy
        </h1>
        <div>
          <div className="rounded-[24px] h-[135px] border-[0.5px] border-[#817fe3]   bg-[#e6e5f91a] flex  gap-[10px]">
            <p className="p-[20px] text-[18px] leading-[40px] text-[#cdccf4] w-[604px] md:w-auto">
              {quotes[currentQuoteIndex]}
            </p>
          </div>
        </div>
        <div className="flex  items-center gap-5">
             <div
          onClick={prevQuote}
          className="cursor-pointer z-10 border-[0.5px] bg-[#e6e5f91a] border-[#e6e5f91a] rounded-full flex justify-center h-[28px] w-[28px] text-primary-purple200 text-[18px]"
        >
          ←
        </div>
             <div className="flex bg-[#e6e5f91a] border-[#e6e5f91a] border-[0.5px] p-3 rounded-3xl space-x-2">
          {quotes.map((_, index) => (
            <span
              key={index}
              className={`block h-2 rounded-full ${
                index === currentQuoteIndex
                  ? "bg-primary-purple500 w-[30px]"
                  : "bg-primary-purple200 w-[8px]"
              }`}
            ></span>
          ))}
        </div>
          <div
          onClick={nextQuote}
          className="cursor-pointer z-10 border-[0.5px] bg-[#e6e5f91a] border-[#e6e5f91a] rounded-full flex justify-center h-[28px] w-[28px] text-primary-purple200 text-[18px]"
        >
          →
        </div>
        </div>
       
      </div>

      <img
        src={background}
        alt="background "
        className="absolute bottom-0 opacity-10"
      />
      <img
        src={background}
        alt="background "
        className="absolute bottom-50% opacity-10"
      />
    </div>
  );
}

export default NewRight;
