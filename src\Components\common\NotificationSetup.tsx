import React, { useEffect, useState } from 'react';
import { Notification, NotificationBing } from 'iconsax-react';
import { toast } from 'react-toastify';
import {
  isPushNotificationSupported,
  subscribeToPushNotifications,
  unsubscribeFromPushNotifications,
  isPushNotificationSubscribed,
} from '../../utils/pushNotificationService';
import { useUserAuthStore } from '../../store/auth';

interface NotificationSetupProps {
  compact?: boolean;  // If true, shows a more compact version
}

const NotificationSetup: React.FC<NotificationSetupProps> = ({ compact = false }) => {
  const [isSubscribed, setIsSubscribed] = useState<boolean | null>(null); // Use null for "loading" state
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSupported, setIsSupported] = useState<boolean>(false);
  
  const vendorId = useUserAuthStore((state) => state.user.vendorId);
  
  // Check if push notifications are supported and if the user is subscribed
  useEffect(() => {
    const checkSupport = async () => {
      const supported = isPushNotificationSupported();
      setIsSupported(supported);
      
      if (supported) {
        try {
          const subscribed = await isPushNotificationSubscribed();
          setIsSubscribed(subscribed);
        } catch (error) {
          console.error('Error checking subscription status:', error);
          setIsSubscribed(false);
        }
      }
      
      setIsLoading(false);
    };
    
    checkSupport();
  }, []);
  
  const handleToggleSubscription = async () => {
    if (!vendorId) {
      toast.error('User ID not found. Please log in again.');
      return;
    }
    
    setIsLoading(true);
    
    try {
      if (isSubscribed) {
        // Unsubscribe
        const success = await unsubscribeFromPushNotifications(vendorId);
        if (success) {
          setIsSubscribed(false);
          toast.success('Push notifications have been turned off.');
        } else {
          toast.error('Failed to turn off push notifications.');
        }
      } else {
        // Subscribe
        const success = await subscribeToPushNotifications(vendorId);
        if (success) {
          setIsSubscribed(true);
          toast.success('Push notifications have been turned on!');
        } else {
          toast.error('Failed to enable push notifications. Please check your browser permissions.');
        }
      }
    } catch (error) {
      console.error('Error toggling subscription:', error);
      toast.error('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!isSupported) {
    if (compact) {
      return null; // Don't show anything in compact mode if not supported
    }
    
    return (
      <div className="p-4 bg-white rounded">
        <p className="text-sm text-gray-500">
          Push notifications are not supported in this browser.
        </p>
      </div>
    );
  }
  
  if (isLoading) {
    return compact ? (
      <div className="flex items-center justify-center w-6 h-6">
        <div className="w-5 h-5 border-2 border-t-primary-purple rounded-full animate-spin"></div>
      </div>
    ) : (
      <div className="flex justify-center p-4">
        <div className="w-8 h-8 border-2 border-t-primary-purple rounded-full animate-spin"></div>
      </div>
    );
  }
  
  // Compact version (just the toggle switch with icon)
  if (compact) {
    return (
      <div className="relative inline-block" title={isSubscribed ? "Turn off notifications" : "Turn on notifications"}>
        <button
          onClick={handleToggleSubscription}
          disabled={isLoading}
          className="focus:outline-none"
        >
          {isSubscribed ? (
            <NotificationBing size={24} color="#4F4CD8" variant="Bold" />
          ) : (
            <Notification size={24} color="#5B5B5B" />
          )}
        </button>
      </div>
    );
  }
  
  // Full version with explanation
  return (
    <div className="p-6 bg-white rounded-2xl shadow">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-sora font-semibold">Push Notifications</h2>
        <div className="relative">
          <label className="flex items-center cursor-pointer">
            <div className="relative">
              <input
                type="checkbox"
                checked={!!isSubscribed}
                onChange={handleToggleSubscription}
                disabled={isLoading}
                className="sr-only"
              />
              <div className={`block w-10 h-5 rounded-full transition ${isSubscribed ? 'bg-primary-purple' : 'bg-gray-300'}`}></div>
              <div className={`absolute left-1 top-1 w-3 h-3 bg-white rounded-full transition transform ${isSubscribed ? 'translate-x-5' : 'translate-x-0'}`}></div>
            </div>
          </label>
        </div>
      </div>
      
      <p className="text-sm text-[#5B5B5B] mb-4">
        {isSubscribed 
          ? 'You will receive notifications for new orders, inventory alerts, and other important updates.'
          : 'Enable notifications to stay updated on new orders, inventory alerts, and other important events.'}
      </p>
      
      <button
        onClick={handleToggleSubscription}
        disabled={isLoading}
        className={`w-full py-3 px-4 rounded-xl font-sora font-semibold flex items-center justify-center gap-2 transition ${
          isSubscribed 
            ? "border border-[#DC3545] text-[#DC3545]"
            : "bg-primary-purple text-white hover:bg-[#6866DE]"
        }`}
      >
        {isSubscribed ? (
          <Notification size={20} />
        ) : (
          <NotificationBing size={20} />
        )}
        {isSubscribed ? 'Turn Off Notifications' : 'Turn On Notifications'}
      </button>
    </div>
  );
};

export default NotificationSetup;
