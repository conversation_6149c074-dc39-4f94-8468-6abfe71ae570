/* eslint-disable @typescript-eslint/no-explicit-any */
import { Warning2 } from 'iconsax-react';
import InputField from '../../Constants/InputField';
import { useEffect, useState } from 'react';
import PreviewProduct from './PreviewAdjustProduct';

interface Product {
  productId: string;
  itemID: string;
  userEmail: string;
  quantityChange: string;
  movementType: string;
  stockLevel: string;
  movementReason: string;
  editAction: string;
  productName: string;
  SKU: string;
}
interface Preview {
  quantityChange: string;
  movementType: string;
  stockLevel: string;
  movementReason: string;
  newStockLevel: string;
}
const AdjustProduct = ({
  onClose,
  isOpen,
  viewingId,
  refetch,
  productDetails,
  isError,
  productRefetch,
  stockRefetch
  
}: any) => {
  const [isPreview, setIsPreview] = useState(false);
  const [product, setProduct] = useState<Product>({
    productId: '',
    itemID: '',
    userEmail: '',
    quantityChange: '',
    movementType: '',
    stockLevel: '',
    movementReason: '',
    editAction: '',
    productName: '',
    SKU: '',
  });
  const [previewData, setPreviewData] = useState<Preview>({
    quantityChange: '',
    movementType: '',
    stockLevel: '',
    movementReason: '',
    newStockLevel: '',
  });
  

  useEffect(() => {
    if (productDetails) {
      setProduct({
        ...productDetails,
      });
    }
  }, [productDetails]);
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setPreviewData((prevDetails) => {
      const newPreviewData = {
        ...prevDetails,
        [name]: value,
      };

      if (name === 'quantityChange' || name === 'movementType') {
        const quantityChange = Number(newPreviewData.quantityChange) || 0;
        const currentStockLevel =
          Number(product.stockLevel || productDetails.totalUnitsInStock) || 0;

        if (newPreviewData.movementType === 'out') {
          const newLevel = currentStockLevel - quantityChange;
          newPreviewData.newStockLevel =
            newLevel > 0 ? newLevel.toString() : '0';
        } else if (newPreviewData.movementType === 'in') {
          newPreviewData.newStockLevel = (
            currentStockLevel + quantityChange
          ).toString();
        }
       
      }

      return newPreviewData;
    });
  };
  const handleBack = () => {
    setIsPreview(false);
  };
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setPreviewData(previewData);
    setIsPreview(true);
  };

    const isSaveDisabled =
      !previewData.quantityChange.trim() || !previewData.movementReason.trim();

  return (
    <div className="fixed font-sora  top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-2 md:p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={onClose}
            className="text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]"
          >
            &times;
          </span>
          {isError ? (
            <div>
              <h2>An error occured, please refresh the page</h2>
            </div>
          ) : (
            <div className="overflow-y-auto h-screen scrollbar-none hidescroll ">
              {/* <div className="spinner" id="adjust-loading"></div> */}
              {!isPreview ? (
                <form onSubmit={handleSubmit}>
                  <div className="pl-3">
                    {" "}
                    <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                      Adjust Stock Units
                    </h2>
                    <p className=" text-sm text-[#919191] font-sora ">
                      Provide the Information below to adjust stock units
                    </p>
                  </div>

                  <div className="normal-box ">
                    <div className="p-6 ml-0 md:ml-2 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6 last:mb-[200px]">
                      <h4 className="text-lg font-sora font-semibold text-[#2a2a2a]">
                        Adjustment Details
                      </h4>
                      <div className="p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                        <p className="text-[12px] font-sora font-normal text-[#5b5b5b]">
                          Product Name
                        </p>
                        <p className="text-sm font-normal text-[#5b5b5b] font-sora">
                          {product.productName}
                        </p>
                      </div>
                      <div className="p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                        <p className="text-[12px] font-sora font-normal text-[#5b5b5b]">
                          SKU Number
                        </p>
                        <p className="text-sm font-normal text-[#5b5b5b] font-sora">
                          {product.SKU || productDetails.sku}
                        </p>
                      </div>
                      <div className="p-0 pb-4  flex flex-col gap-2">
                        <p className="text-[12px] font-sora font-normal text-[#5b5b5b]">
                          Current Stock Level
                        </p>
                        <p className="text-sm font-normal text-[#5b5b5b] font-sora">
                          {" "}
                          {product?.stockLevel?.toLocaleString() ||
                            productDetails.totalUnitsInStock}
                        </p>
                      </div>
                    </div>

                    <div className="p-6 rounded-2xl my-5 flex flex-col gap-6 ">
                      <h4>Adjustment Details</h4>
                      <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0">
                        <p className="text-[12px] font-sora font-normal text-[#5b5b5b] ">
                          Adjustment Type
                        </p>
                        <select
                          name="movementType"
                          value={previewData.movementType}
                          onChange={handleChange}
                          className="text-[#5b5b5b] text-sm font-sora font-normal h-[48px] px-4 border border-[#cccccc] rounded-2xl outline-none"
                        >
                          <option>Choose adjustment type</option>
                          <option value="out">Remove Stock</option>
                          <option value="in">Add Stock</option>
                        </select>
                      </div>
                      <InputField
                        placeholder="Enter the amount of stock you want to adjust"
                        label="  Quantity to Adjust"
                        name="quantityChange"
                        type="number"
                        value={previewData.quantityChange.toString()}
                        onChange={handleChange}
                        onKeyDown={(e) => {
                          const allowedKeys = ["Backspace", "Tab", "ArrowLeft", "ArrowRight"];
                          if (
                            !/[0-9]/.test(e.key) &&
                            !allowedKeys.includes(e.key)
                          ) {
                            e.preventDefault();
                          }
                        }}
                      />
                      <InputField
                        placeholder="This will be determined by quantity adjusted"
                        readOnly
                        label=" New Stock Level"
                        name="newStockLevel"
                        value={previewData.newStockLevel}
                      />
                      <div className="flex text-[#5b5b5b] text-xs items-center gap-1.5 -mt-4">
                        <Warning2 size="16" color="#5B5B5B" />{" "}
                        <p>
                          {" "}
                          Current stock level is{" "}
                          {product?.stockLevel?.toLocaleString() ||
                            productDetails.totalUnitsInStock}{" "}
                          units
                        </p>
                      </div>

                      <div className="p-0 flex flex-col gap-2 mb-44">
                        <p className="text-[12px] font-sora font-normal text-[#5b5b5b]">
                          Reason for Adjustment
                        </p>
                        <textarea
                          rows={12}
                          className="text-[#7b7b7b] text-[12px] p-4 border border-[#cccccc] rounded-[16px] outline-none"
                          placeholder="What is your reason for adjustment, Damaged goods,  or New Shipment "
                          name="movementReason"
                          value={previewData.movementReason}
                          onChange={handleChange}
                        ></textarea>
                      </div>
                    </div>
                  </div>
                  <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2 md:p-10">
                    <div className="flex w-full px-7.5 gap-2.5">
                      <button
                        onClick={onClose}
                        className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2"
                      >
                        Back
                      </button>
                      <button
                        type="submit"
                        disabled={isSaveDisabled}
                        className={` w-full text-[#fcfcfc] bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 h-[49px] rounded-2xl cursor-pointer flex-2 ${
                          isSaveDisabled 
                            ? " opacity-70 cursor-default"
                            : ""
                        }`}
                      >
                        Save & Preview
                      </button>
                    </div>
                  </div>
                </form>
              ) : (
                <PreviewProduct
                  onBack={handleBack}
                  productDetails={previewData}
                  oldStockData={product}
                  tempData={productDetails}
                  productId={viewingId}
                  itemID={product.itemID}
                  onClose={onClose}
                  refetch={refetch}
                      productRefetch={productRefetch}
                      stockRefetch={stockRefetch}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
export default AdjustProduct;
