import { useState, useRef, useEffect } from "react";
import { DateRange, RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { subDays } from "date-fns";
import { Calendar } from "iconsax-react";
import "../calender.css";
import { useDateStore } from "../store/date";

// ✅ Custom hook to detect screen width
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  return isMobile;
};

const DatePicker = () => {
  const [showPicker, setShowPicker] = useState(false);
  const { setDateRange, startDates, endDates } = useDateStore();
  const isMobile = useIsMobile();

  const [tempRange, setTempRange] = useState({
    startDate: subDays(new Date(), 30),
    endDate: new Date(),
    key: "selection",
  });

  const togglePicker = () => {
    setShowPicker(!showPicker);
  };

  const handleSelect = (ranges: RangeKeyDict) => {
    const { startDate, endDate } = ranges.selection;
    setTempRange({
      ...tempRange,
      startDate: startDate || new Date(),
      endDate: endDate || new Date(),
    });
  };

  const applyDateRange = () => {
    setDateRange(tempRange.startDate, tempRange.endDate);
    setShowPicker(false);
  };

  const ref = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setShowPicker(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [ref]);

  // ✅ Format dates based on screen size
  const formattedRange = isMobile
    ? `${startDates.toLocaleDateString("en-US", {
        day: "2-digit",
        month: "short",
      })} - ${endDates.toLocaleDateString("en-US", {
        day: "2-digit",
        month: "short",
      })}`
    : `${startDates.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      })} - ${endDates.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      })}`;

  return (
    <div className="relative" ref={ref}>
      <div
        onClick={togglePicker}
        className="flex items-center h-[49px] justify-between gap-2 px-4 py-2 border border-primary-neutral200 rounded-[16px] cursor-pointer hover:border-primary-purple focus:border-primary-purple"
      >
        <div className="flex text-[12px] md:text-[14px] font-bold justify-center w-full text-[#2A2A2A]">
          {formattedRange}
        </div>
        <Calendar size="20" className="text-gray-500" />
      </div>

      {showPicker && (
        <div className="absolute z-50 mt-2 bg-white border border-gray-300 rounded-lg shadow-lg p-4">
          <DateRange
            editableDateInputs={true}
            onChange={handleSelect}
            moveRangeOnFirstSelection={false}
            ranges={[tempRange]}
            rangeColors={["#4f4cd8"]}
            maxDate={new Date()}
            className="custom-date-range-picker"
          />
          <button
            className="bg-[#4f4cd8] text-white w-full mt-4 py-2 rounded-lg"
            onClick={applyDateRange}
          >
            Apply Date
          </button>
        </div>
      )}
    </div>
  );
};

export default DatePicker;
