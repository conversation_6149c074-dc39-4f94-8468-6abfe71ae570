import { Messages3, TickCircle } from "iconsax-react";
import image from "../assets/Group 1000011163.svg";
import { Link } from "react-router-dom";
import { RouteNames } from "../utils/RouteNames";
import Logo from "../assets/Logo.svg";
// import <PERSON><PERSON>omb from "./HoneyComb";
import { useEffect } from "react";
import { isPushNotificationSupported, subscribeToPushNotifications } from "../utils/pushNotificationService";
import { useUserAuthStore } from "../store/auth";
import NewRight from "./NewRight";

const WelcomeVerify = () => {
  // Try to subscribe to push notifications on component mount
  useEffect(() => {
    // Use sessionStorage to track if we've attempted subscription during this session
    if (sessionStorage.getItem('pushAttempted') === 'true') {
      console.log("Push subscription already attempted in this session");
      return;
    }

    const enablePushNotifications = async () => {
      try {
        sessionStorage.setItem('pushAttempted', 'true');
        const vendorId = useUserAuthStore.getState().user.vendorId;
        
        if (!vendorId || !isPushNotificationSupported()) {
          return;
        }

        // Check if notifications are already permitted
        if (Notification.permission === 'granted') {
          console.log("WelcomeVerify: Permission already granted, checking subscription");
          const swRegistration = await navigator.serviceWorker.ready;
          const existingSubscription = await swRegistration.pushManager.getSubscription();
          
          if (!existingSubscription) {
            await subscribeToPushNotifications(vendorId);
          } else {
            console.log("Existing subscription found in WelcomeVerify");
          }
        } else if (Notification.permission !== 'denied') {
          // If not explicitly denied, request permission
          console.log("WelcomeVerify: Requesting notification permission");
          const permission = await Notification.requestPermission();
          if (permission === 'granted') {
            console.log("WelcomeVerify: Permission granted, subscribing...");
            await subscribeToPushNotifications(vendorId);
          }
        }
      } catch (error) {
        console.error("Error setting up push notifications in WelcomeVerify:", error);
      }
    };

    enablePushNotifications();
  }, []);

  return (
    <div className="fixed inset-0 flex flex-col h-full md:flex-row  p-5 mt-0 lg:mt-0 md:justify-center gap-2 w-full  overflow-auto">
      <div className="flex-1 h-full">
        <div className="flex flex-col h-full">
          {/* Left */}
          <div className="">
            <div className="flex p-2 justify-center md:justify-start">
              <img src={Logo} alt="Katajere Logo" className="mb-[40px]" />
            </div>
            <div className=" flex-col px-1 md:px-6 space-y-[20px]">
              <div>
                <div className="flex justify-center">
                  <img src={image} alt="" />
                </div>
                <h2 className="text-[28px] text-center mt-[10px] text-[#1A1A1A] font-normal">
                  Welcome Aboard
                </h2>
              </div>

              <div className="flex justify-center mt-4 text-left gap-[10px]">
                <div>
                  <TickCircle size="22" color="#28A745" variant="Bold" />
                </div>
                <p className="text-sm leading-[22.4px] font-normal font-sora text-[#7B7B7B]">
                  We’re excited to have you on board! Your email has been
                  successfully verified and your account is now active.
                </p>
              </div>
              <div className="flex justify-center my-6 text-left gap-[10px]">
                <div>
                  <TickCircle size="22" color="#28A745" variant="Bold" />
                </div>
                <p className="text-sm leading-[22.4px] font-normal font-sora text-[#7B7B7B] ">
                  As a new member of the Huxxle community, you now have access
                  to our comprehensive digital solution designed to enhance your
                  business operations.
                </p>
              </div>
              <div className="flex justify-center text-left gap-[10px] ">
                <div>
                  <TickCircle size="22" color="#28A745" variant="Bold" />
                </div>
                <p className="text-sm leading-[22.4px] font-normal font-sora text-[#7B7B7B]">
                  With Huxxle, you can easily manage your inventory,
                  streamline your orders, and leverage AI-powered analytics to
                  make data-driven decisions that propel your business forward.
                </p>
              </div>
              <Link
                to={RouteNames.business}
                className="px-10 w-full font-semibold font-sora right-0 flex border-none mt-3 rounded-[12px] bg-[#4f4cd8] text-white text-sm h-[49px] cursor-pointer transition duration-300 items-center justify-center"
              >
                Go To Dashboard
              </Link>
            </div>

            {/* <div className="mt-10 px-6">
              <div className="flex  justify-between md:hidden gap-5">
                <button className="border-[1px] font-sora text-[#5B5B5B] rounded-lg py-3 border-primary-neutral300 flex-1">
                  Terms of Use
                </button>
                <button className="border-[1px] font-sora text-[#5B5B5B] rounded-lg py-3 border-primary-neutral300 flex-1">
                  Privacy Policy
                </button>
              </div>
            </div> */}

            <div className="flex px-4 mt-4 items-center">
              <Messages3  size="24" color="#5B5B5B" />
              <p className="text-xs pl-2 text-[#5b5b5b]">
                Do you need help? Reach out to{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="no-underline text-[#4f4cd8]"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
            
          </div>
          {/* right */}
          {/* <HoneyComb /> */}
        </div>
      </div>
      <div className="w-full h-full lg:block flex-1 hidden">
    <NewRight/>
      </div>
    </div>
  );
};
export default WelcomeVerify;
