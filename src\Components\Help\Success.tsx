import image from "../../assets/Group 1000011163.svg";

interface actions {
  isOpen: boolean;
  closeModal: () => void;
}
const Success: React.FC<actions> = ({ isOpen, closeModal }) => {
  return (
    <div className="fixed font-sora  top-0 left-0 w-full h-full  bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-screen md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="h-[calc(100vh-80px)] overflow-y-auto scrollbar-none hidescroll">
            <div className="pl-6 items-center flex flex-col gap-10">
              <div>
                <img src={image} alt="" />
              </div>
              <p className="font-sora text-[#1A1A1A] text-[28px]">
                Your Message Was Received
              </p>
              <p className="text-[14px] text-center text-[#7B7B7B] font-sora">
                We are currently reviewing your message and someone from our
                team will respond to you promptly, Our response usually takes
                less than 24 hours
              </p>

              <button onClick={closeModal} className="bg-[#4f4cd8] w-full text-[#fcfcfc] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl cursor-pointer flex-2">
                Thank You
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Success;
