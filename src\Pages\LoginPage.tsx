import { useEffect, useState } from 'react';
import LoginBox from '../Components/LoginBox';
import { useNavigate } from 'react-router-dom';
import { RouteNames } from '../utils/RouteNames';
import logo from '../assets/Logo.svg'

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: "accepted" | "dismissed" }>;
}

function LoginPage() {
  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallButton, setShowInstallButton] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [showInstallInstructions, setShowInstallInstructions] = useState(false);
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true); // Add a loading state

  useEffect(() => {
    // Check for token in localStorage on component mount
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      try {
        const parsedAuthStore = JSON.parse(userAuthStore);
        const token = parsedAuthStore?.state?.token;
        if (token) {
          navigate(RouteNames.overview); // Redirect to overview if token exists
        }
      } catch (error) {
        console.error("Error parsing user-auth-store from localStorage:", error);
        // Handle parsing errors, maybe clear the localStorage or show an error message
      }
    }
    setIsLoading(false); // Set loading to false after token check

    // Detect iOS (iPhone, iPad, iPod)
    const userAgent = window.navigator.userAgent.toLowerCase();
    setIsIOS(/iphone|ipad|ipod/.test(userAgent));

    // Listen for the 'beforeinstallprompt' event
    const handleBeforeInstallPrompt = (e: Event) => {
      const installPromptEvent = e as BeforeInstallPromptEvent; // Explicitly cast the event
      installPromptEvent.preventDefault(); // Prevent the default mini-infobar
      setDeferredPrompt(installPromptEvent); // Save the event for later use
      setShowInstallButton(true); // Show your custom install button
    };

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener(
        "beforeinstallprompt",
        handleBeforeInstallPrompt
      );
    };
  }, [navigate]);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      try {
        await deferredPrompt.prompt(); // Show the install prompt
        const choiceResult = await deferredPrompt.userChoice;
        if (choiceResult.outcome === "accepted") {
          console.log("PWA installation accepted");
        } else {
          console.log("PWA installation dismissed");
        }
      } catch (error) {
        console.error("Error during PWA installation prompt:", error);
      } finally {
        // Clear the deferred prompt and hide the button
        setDeferredPrompt(null);
        setShowInstallButton(false);
      }
    }
  };

  const handleShowInstallInstructions = () => {
    setShowInstallInstructions(true);
    setShowInstallButton(false); // Hide button after instructions are shown
  };

  const handleHideInstructions = () => {
    setShowInstallInstructions(false);
  };

  if (isLoading) {
    // Render a loading indicator while checking the token
    return (
      <div className="flex justify-center items-center h-screen">
        <img className='animate-pulse' src={logo}/>        
      </div>
    );
  }

  return (
    <>
      <div className="flex justify-center items-center ">
        <LoginBox />
      </div>
      {showInstallButton && !isIOS && (
        <div style={{ position: "fixed", bottom: "20px", right: "20px" }}>
          <button
            onClick={handleInstallClick}
            style={{
              padding: "10px 20px",
              backgroundColor: "#4CAF50",
              color: "#fff",
              border: "none",
              borderRadius: "5px",
              cursor: "pointer",
            }}
          >
            Install App
          </button>
        </div>
      )}
      {isIOS && !showInstallInstructions && (
        <div style={{ position: "fixed", bottom: "20px", left: "20px" }}>
          <button
            onClick={handleShowInstallInstructions}
            style={{
              padding: "10px 20px",
              backgroundColor: "#007BFF",
              color: "#fff",
              border: "none",
              borderRadius: "5px",
              cursor: "pointer",
            }}
          >
            How to Install
          </button>
        </div>
      )}
      {isIOS && showInstallInstructions && (
        <div
          style={{
            position: "fixed",
            bottom: "20px",
            left: "20px",
            padding: "10px",
            backgroundColor: "#f9f9f9",
            border: "1px solid #ddd",
            borderRadius: "5px",
            maxWidth: "300px",
          }}
        >
          <p>
            To install this app on your iOS device, tap the
            <strong>Share</strong> icon in Safari, then select
            <strong>Add to Home Screen</strong>.
          </p>
          <button
            onClick={handleHideInstructions}
            style={{
              padding: "5px 10px",
              backgroundColor: "#FF0000",
              color: "#fff",
              border: "none",
              borderRadius: "5px",
              marginTop: "10px",
              cursor: "pointer",
            }}
          >
            Close
          </button>
        </div>
      )}
    </>
  );
}

export default LoginPage;
