/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from "react";
import InputField from "../Constants/InputField";
import { orderServices } from "../utils/Services/Order";
import { useMutation } from "react-query";
import { useUserAuthStore } from "../store/auth";
import { toast } from "react-toastify";
import { InfoCircle, Warning2 } from "iconsax-react";
import progress from "../assets/deliveryStep.svg";
import AddFirstProduct from "./AddFirstProduct";
import { FaSpinner } from "react-icons/fa";

interface OrderProps {
  closeModal: () => void;
  isOpen: boolean;
  closeSetup: any;
}
interface AddDelivery {
  vendorId: string;
  vendorEmail: string;
  deliveryFeeValue: string;
  deliveryTitle: string;
  isPublic: string;
}

const AddDelivery: React.FC<OrderProps> = ({
  closeModal,
  closeSetup,
  isOpen,
}) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    return () => {
      document.body.classList.remove("no-scroll");
    };
  }, [isOpen]);

  // const [isToggled, setIsToggled] = useState<boolean>(false);
  const user = useUserAuthStore((state) => state.user);
  const [addProduct, setAddProduct] = useState(false);
  const [addDelivery, setAddDelivery] = useState<AddDelivery>({
    vendorEmail: user.userEmail,
    vendorId: user.vendorId,
    deliveryFeeValue: "",
    deliveryTitle: "",
    isPublic: "",
  });

  const deliveryFee = useMutation(orderServices.deliveryFee);
  const isDisabled =
    !addDelivery.deliveryTitle || !addDelivery.deliveryFeeValue;

  // Function to handle the toggle
  // const handleToggle = () => {
  //   setIsToggled(!isToggled);
  // };
  const handleProduct = () => {
    setAddProduct((prev) => !prev);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
  
    if (name === "deliveryFeeValue") {
      // Allow only numbers (0-9), removing any other characters like hyphens, dots, spaces, etc.
      const numericValue = value.replace(/[^0-9]/g, ""); // Removes everything except digits
  
      setAddDelivery((prevState) => ({
        ...prevState,
        [name]: numericValue,
      }));
    } else {
      setAddDelivery((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    }
  };
  
  

  const handleSubmit = async () => {
    const payload = {
      vendorEmail: addDelivery.vendorEmail,
      vendorId: addDelivery.vendorId,
      deliveryFeeValue: addDelivery.deliveryFeeValue,
      deliveryTitle: addDelivery.deliveryTitle,
      isPublic: 'yes',
    };
    try {
      const res: any = await deliveryFee.mutateAsync(payload);
      console.log(res);
      toast.success(res.message);
      handleProduct();
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="fixed font-sora  bg-black/10 top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-1 md:p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? "translate-x-0 " : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeModal}
            className="close-drawer pt-4 text-4xl font-bold cursor-pointer my-0 pr-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="my-[20px] grid justify-center">
            <img src={progress} alt="progress bar" />
          </div>
          <div className="overflow-y-auto h-screen scrollbar-none hidescroll ">
            <div className="md:pl-6 px-2 md:px-0 ">
              <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
               Set Delivery Prices
              </h2>
              <p className=" text-sm text-[#919191] font-sora ">
                You can set different prices for different delivery locations.
              </p>
              <div className="md:py-4 py-3 px-4 grid gap-2 mt-6 bg-[#FFF2DF] rounded-2xl md:px-6">
                
                <p className="text-[#5B5B5B] flex gap-2 items-start text-[14px] leading-[22.4px] font-sora">
                  <InfoCircle  color="#FF9900" size={48} /> Your customers will select from this delivery options when they want to buy from your website.
                </p>
              </div>
            </div>
            <div className="p-6 rounded-2xl flex flex-col gap-6 last:mb-[200px]">
              <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                  Delivery Location
                </p>
                <InputField
                  id="title"
                  placeholder="E.g Anywhere on Lagos Island"
                  onChange={handleChange}
                  value={addDelivery.deliveryTitle}
                  name="deliveryTitle"
                />
                <p className="flex items-center gap-2 font-sora text-[12px] text-[#5B5B5B]"> <Warning2 size={14}/> E.g Anywhere on Lagos Island</p>
              </div>
              <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                  Amount
                </p>
                <div className="flex items-center h-[48px] gap-4 border-[1px] border-[#dcdcdc] rounded-xl">
                  <p className="px-4 h-full flex items-center border-r-[1px] border-[#cccccc] bg-[#E9ECEF] rounded-l-xl">₦</p>
                    <input
                  id="amount"
                  placeholder="Enter Amount"
                  onChange={handleChange}
                  className="w-full outline-none border-none"
                  // type="number"
                  onKeyDown={(e) => {
                    const allowedKeys = ["Backspace", "Tab", "ArrowLeft", "ArrowRight"];
                    if (
                      !/[0-9]/.test(e.key) &&
                      !allowedKeys.includes(e.key)
                    ) {
                      e.preventDefault();
                    }
                  }}
                  value={addDelivery.deliveryFeeValue}
                  
                  name="deliveryFeeValue"
                />
                </div>
                
              </div>
              {/* <div className="p-6 flex bg-[#F5F5F5] md:mb-0 mb-[300px] rounded-2xl flex-col gap-3">
                <h2 className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                  Do you want customer’s to see this on your website ?
                </h2>
                <div className="flex justify-between">
                  <p>Yes</p>
                  <div
                    className={`w-10 h-6 flex items-center bg-gray-300  rounded-full p-1 cursor-pointer ${
                      isToggled ? "bg-purple-600" : "bg-gray-300"
                    }`}
                    onClick={handleToggle}
                  >
                    <div
                      className={`bg-white w-4 h-4 rounded-full shadow-md transform ${
                        isToggled ? "translate-x-5" : "translate-x-0"
                      } transition`}
                    ></div>
                  </div>
                </div>
              </div> */}
              <div className="mb-[300px]"></div>
            </div>

            <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 px-3  md:p-10">
              <div className="flex flex-col md:flex-row w-full px-7.5 gap-2.5">
                <button
                  onClick={closeModal}
                  className="bg-transparent order-2 md:order-1  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2"
                >
                  Back
                </button>
                <button
                  className={`${
                    isDisabled ? "opacity-50 cursor-not-allowed" : ""
                  } ${
                    deliveryFee.isLoading ? "opacity-50 cursor-auto" : ""
                  } w-full text-[#fcfcfc] order-1 md:order-2  bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 h-[49px] rounded-2xl cursor-pointer flex-2 `}
                  onClick={handleSubmit}
                  //   onClick={handleProduct}
                  disabled={deliveryFee.isLoading || isDisabled}
                >
                  {deliveryFee.isLoading ? (<p className="animate-spin flex justify-center items-center "> <FaSpinner size={20}/></p>) : " Continue to Add Product"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      {addProduct && (
        <AddFirstProduct
          closeSetup={closeSetup}
          isOpen={addProduct}
          closeModal={handleProduct}
        />
      )}
    </div>
  );
};

export default AddDelivery;
