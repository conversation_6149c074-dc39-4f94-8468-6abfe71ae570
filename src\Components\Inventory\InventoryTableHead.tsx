/* eslint-disable @typescript-eslint/no-explicit-any */
import { SearchNormal1 } from "iconsax-react";
import React, { useState } from "react";
import Filter from "../../Constants/Filter";

// Define the props type for the component
interface InventoryTableHeadProps {
  data: string[]; // Array of category strings
  searchTerm: string;
  selectedCategory: string;
  selectedStatus: string;
  onSearchChange: (value: string) => void;
  onCategoryChange: (value: string) => void;
  onStatusChange: (value: string) => void;
}

const InventoryTableHead: React.FC<InventoryTableHeadProps> = ({
  data,
  searchTerm,
  selectedCategory,
  selectedStatus,
  onSearchChange,
  onCategoryChange,
  onStatusChange,
}) => {
    const [dropDown, setDropDown] = useState<any>(false);
 
  return (
    <div className="flex w-full flex-col gap-2 md:flex-row justify-between items-start p-5">
      {/* Title and Description */}
      <div className="table-title">
        <h1 className="text-lg text-[#393939] mb-2 font-semibold">
          All Products
        </h1>
        <p className="text-sm text-[#919191] font-normal font-sora mb-2.5">
          See information of all your products here
        </p>
      </div>

      {/* Filters Section */}
      <div className="w-full md:w-[671px] flex flex-col md:flex-row gap-2.5">
        <div className="flex w-full gap-2 items-center">
          <div className="flex items-center h-[42px] gap-[10px] py-2 px-2 border border-gray-300 text-[#ABABAB] text-xs rounded-2xl bg-[#fcfcfc] w-full">
            <SearchNormal1 size="20" color="#ABABAB" />
            <input
              type="text"
              id="searchInput"
              placeholder="Search"
              className="bg-[#fcfcfc] w-full outline-none"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>
          <div className="md:hidden">
            <Filter
              statuses="status"
              onStatusChange={onStatusChange}
              selectedStatus={selectedStatus}
              stat1="Low Stock"
              stat2="Out of Stock"
              stat3="In Stock"
              category="category"
              onCategoryChange={onCategoryChange}
              data={data}
              selectedCategory={selectedCategory}
              setDropDown={setDropDown}
              dropDown={dropDown}
            />
          </div>
        </div>

        <select
          id="categoryFilter"
          className="py-2 hidden md:inline px-4 border h-[42px] text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none w-[250px]"
          value={selectedCategory}
          onChange={(e) => onCategoryChange(e.target.value)}>
          <option value="">Filter by Category</option>
          {data?.map((category) => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>

        <select
          id="statusFilter"
          className="py-2 hidden md:inline px-4 border h-[42px] text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none w-full"
          value={selectedStatus}
          onChange={(e) => onStatusChange(e.target.value)}>
          <option value="">Filter by Status</option>
          <option value="In Stock">In Stock</option>
          <option value="Low Stock">Low Stock</option>
          <option value="Out of Stock">Out of Stock</option>
        </select>
      </div>
    </div>
  );
};

export default InventoryTableHead;
