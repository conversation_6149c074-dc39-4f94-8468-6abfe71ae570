import KatajereAPI from "../KatajereAPI";

export const TrackStockServices = {
  GetStockTracker: async (payload: {
    vendorId: string;
    startDate?: string;
    endDate?: string;
  }) => {
    const queryParams = new URLSearchParams({
      vendorId: payload.vendorId,
      ...(payload.startDate && { startDate: payload.startDate }),
      ...(payload.endDate && { endDate: payload.endDate }),
    });
    return await KatajereAPI().get(`stock/stock-tracker?${queryParams}`);
  },
  GetStockTrackerDetails: async (payload: {
    vendorId: string;
    productId: string;
  }) => {
    const queryParams = new URLSearchParams(payload).toString(); // Serializing the payload object to query parameters
    return await KatajereAPI().get(
      `/stock/stock-tracker-details?${queryParams}`
    );
  },
};
