// src/stores/dateStore.ts
import { create } from "zustand";
import { subDays } from "date-fns";

interface DateState {
  startDates: Date;
  endDates: Date;
  setStartDate: (date: Date) => void;
  setEndDate: (date: Date) => void;
  setDateRange: (startDates: Date, endDates: Date) => void;
  resetDate: () => void; // Add resetDate function
}

export const useDateStore = create<DateState>((set) => ({
  startDates: subDays(new Date(), 30), // Default startDate is 30 days ago
  endDates: new Date(), // Default endDate is today
  setStartDate: (startDates) => set({ startDates }),
  setEndDate: (endDates) => set({ endDates }),
  setDateRange: (startDates, endDates) => set({ startDates, endDates }), // Function to update both dates
  resetDate: () =>
    set({
      startDates: subDays(new Date(), 30), // Reset to 30 days ago
      endDates: new Date(), // Reset to today
    }), // Implement resetDate function
}));
