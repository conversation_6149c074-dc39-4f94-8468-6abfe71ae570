/* eslint-disable @typescript-eslint/no-explicit-any */

import { More, SearchNormal1 } from "iconsax-react";
import Table from "../../utils/Table";
import { useState, useMemo } from "react";
import OrderDetails from "./OrderDetails";
import { orderServices } from "./../../utils/Services/Order";
import { useMutation, useQuery } from "react-query";
import usePagination from "../../Constants/usePagination";
import ProductCard from "../../utils/ProductCard";
import DropDown from "./DropDown";
import EditOrder from "./EditOrder";
import ConfirmationModal from "../../Constants/ConfirmationModal";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../store/auth";
import Filter from "../../Constants/Filter";
import logo from "../../../public/Favicon.png";
import OrderEmpyIcon from "../../assets/orderEmptyIcon.svg";
import leftArrowIcon from "../../assets/leftArrow.svg";
import rightArrowIcon from "../../assets/rightArrow.svg";

const TableOrder = ({ data, isLoading, isError, refetch }: any) => {
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [modalPosition, setModalPosition] = useState<{
    top: number;
  } | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedChannel, setSelectedChannel] = useState<any>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [viewOrderModal, setViewOrderModal] = useState<boolean>(false);
  const [editOrderModal, setEditOrderModal] = useState<boolean>(false);
  const [deleteOrderModal, setDeleteOrderModal] = useState<boolean>(false);
  const [orderNum, setOrderNum] = useState<string | null>(null);
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
  const [deleteSingleOrderModal, setDeleteSingleOrderModal] =
    useState<boolean>(false);
  const [dropDown, setDropDown] = useState<any>(false);

  const user = useUserAuthStore((state) => state.user);

  const mutation = useMutation((data: any) =>
    orderServices.deleteEntireOrder(data)
  );

  const {
    data: order,
    // isError: orderDetailsError,
    isLoading: orderDetailsIsLoading,
    refetch: orderDetailsRefetch,
  } = useQuery(
    ["orderDetail", selectedRow],
    () => {
      return orderServices.getOrderDetails(selectedRow, user.vendorId);
    },
    {
      enabled: !!selectedRow,
      onSuccess: (data) => {
        setOrderNum(data?.orderId || null);
      },
      onError: (error) => {
        console.error("Error fetching order details:", error);
      },
    }
  );
  console.log("order data", data?.ordersTable);
  // console.log('order', order);

  const deleteOrder = (productIds?: string[]) => {
    if (!orderNum) {
      console.error("No order number available to delete.");
      return;
    }

    const deleteData = {
      vendorId: user.vendorId,
      orderId: orderNum,
      vendorEmail: user.userEmail,
      ...(productIds ? { productIds } : { deleteEntireOrder: true }),
    };

    mutation.mutate(deleteData, {
      onSuccess: async (data) => {
        if (order?.orderSummary.length === 1) {
          setViewOrderModal(false);
        }
        toast.success(data?.data?.message || "Deleted successfully");
        setDeleteOrderModal(false);
        setDeleteSingleOrderModal(false);
        orderDetailsRefetch();
        refetch();
      },
      onError: (error: any) => {
        if (error?.response?.status === 429) {
          toast.error(error?.response?.data?.message || "Too many requests");
        } else {
          toast.error(
            error?.response?.data?.error ||
              error?.message ||
              "An error occurred"
          );
        }
      },
    });
  };
  const handleDownloadInvoice = () => {
    if (!order?.invoiceDownload) {
      toast.error("Invoice download URL is not available.");
      return;
    }

    const link = document.createElement("a");
    link.href = order.invoiceDownload;
    link.download = `Invoice_${order.orderNumber}.pdf`;
    link.target = "_blank";
    link.click();
    document.body.removeChild(link);
    toast.success("Invoice downloaded successfully.");
  };

  const handleDeleteSingleProduct = (productId: string) => {
    deleteOrder([productId]);
  };

  const handleDeleteEntireOrder = () => {
    deleteOrder();
  };
  const handleActionClick = (event: React.MouseEvent, orderId: string) => {
    event.stopPropagation();
    setSelectedRow(orderId);
    const target = event.currentTarget.getBoundingClientRect();
    setModalPosition({
      top: target.top + window.scrollY,
    });
    setDropdownVisible(true);
  };
  const closeDropDown = () => setDropdownVisible(false);

  const handleRowMobile = (orderId: any) => {
    setViewOrderModal(orderId);
    setSelectedRow(orderId);
  };

  const handleRowClick = (orderId: any) => {
    setViewOrderModal(orderId);
    setSelectedRow(orderId);
  };

  const handleViewOrder = () => {
    setViewOrderModal(true);
    setDropdownVisible(false);
  };
  const handleEditOrder = () => {
    setEditOrderModal(true);
    setDropdownVisible(false);
  };

  const openDeleteOrderModal = () => {
    setDeleteOrderModal(true);
    setDropdownVisible(false);
  };

  const headers = [
    {
      title: "Product Name",
      key: "productName",
      render: (row: any) => (
        <div className="flex items-center gap-2">
          <p className="truncate font-bold">{row.productName}</p>
        </div>
      ),
    },
    {
      title: "Order Id",
      key: "orderId",
      render: (row: any) => <div>{row.orderId}</div>,
    },
    {
      title: "Order Date",
      key: "orderDate",
      render: (row: any) => <div>{row.orderDate}</div>,
    },
    {
      title: "Price",
      key: "totalAmount",
      render: (row: any) => <div>{row.totalAmount}</div>,
    },
    {
      title: "Channel",
      key: "channel",
      render: (row: any) => <div>{row.channel}</div>,
    },
    {
      title: "Order Status",
      key: "orderStatus",
      render: (row: any) => (
        <div
          className={`p-3 truncate  align-middle  text-xs  ${
            row.orderStatus === "Delivered"
              ? "text-[#28a745]"
              : row.orderStatus === "Pending"
              ? "text-[#FFC107]"
              : row.orderStatus === "Shipped"
              ? "text-primary-purple"
              : row.orderStatus === "Returned"
              ? "text-primary-neutralt1"
              : "text-[#DC3545]"
          }`}
        >
          <span
            className={`px-4 py-1  rounded-xl ${
              row.orderStatus === "Delivered"
                ? "bg-[#EAF7ED]"
                : row.orderStatus === "Pending"
                ? "bg-[#FFF9E6]"
                : row.orderStatus === "Shipped"
                ? "bg-[#E6E5F9]"
                : row.orderStatus === "Returned"
                ? "bg-[#F5F5F5]"
                : "bg-[#FAF2F2]"
            }`}
          >
            {row.orderStatus}
          </span>
        </div>
      ),
    },
    {
      title: "Customer",
      key: "customerName",
      render: (row: any) => <p>{row.customerName}</p>,
    },
    {
      title: "Payment Status",
      key: "paymentStatus",
      render: (row: any) => (
        <div className="flex items-center gap-4">
          <span
            className={`w-[8px] h-[8px]  rounded-full ${
              row.paymentStatus === 'Paid'
                ? 'bg-green-800'
                : row.paymentStatus === 'Not Yet Paid'
                ? 'bg-[#FFC107]': row.paymentStatus === 'Declined'? 'bg-red-600'
                : 'bg-primary-purple'
            } `}></span>
          {row.paymentStatus}
        </div>
      ),
    },

    {
      title: "Action",
      render: (row: any) => (
        <div className="relative">
          <More
            size="16"
            color="#1A1A1A"
            className="cursor-pointer"
            onClick={(event) => handleActionClick(event, row.orderId)}
          />
        </div>
      ),
    },
  ];

  const channels: string[] = Array.from(
    new Set(data?.ordersTable?.map((order: any) => order?.channel))
  );
  const handleChannelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedChannel(e.target.value);
  };
  const handleSearchChange = (event: any) => {
    setSearchTerm(event.target.value);
  };

  const handleStatusChange = (event: any) => {
    setSelectedStatus(event.target.value);
  };
  const handleStatusFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    handleStatusChange(e);
    setDropDown(false);
  };
  const handleStatusChannelChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    handleChannelChange(e);
    setDropDown(false);
  };
  const { page, limit, Pagination } = usePagination({
    page: 1,
    limit: 8,
    total: data?.ordersTable?.length,
  });

  const filteredOrders = useMemo(() => {
    return data?.ordersTable?.filter((order: any) => {
      const matchesSearch =
        order.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.orderId.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCustomer =
        selectedChannel === "" || order.channel === selectedChannel;
      const matchesStatus =
        selectedStatus === "" || order.orderStatus === selectedStatus;

      return matchesSearch && matchesCustomer && matchesStatus;
    });
  }, [data?.ordersTable, searchTerm, selectedChannel, selectedStatus]);

  const paginatedRows = filteredOrders?.slice((page - 1) * limit, page * limit);

  return (
    <div className="my-15 bg-white w-full  border border-[#dedede] font-sora rounded-3xl mb-8 ">
      <div className="flex w-full flex-col gap-2 md:flex-row justify-between items-start p-5">
        <div className="table-title">
          <h1 className="text-lg text-[#393939] mb-2 font-semibold">
            All Orders
          </h1>
          <p className="text-sm text-[#919191] font-normal font-sora mb-2.5">
            Manage all your orders here{" "}
          </p>
        </div>
        {/* className="w-full md:w-[671px] flex flex-col md:flex-row gap-2.5" */}
        <div className="w-full md:w-[671px] flex flex-col md:flex-row gap-2.5">
          <div className="flex w-full gap-2 items-center">
            <div className="flex items-center h-[42px] gap-[10px] py-2 px-2 border border-gray-300 text-[#ABABAB] text-xs rounded-2xl bg-[#fcfcfc] w-full">
              <SearchNormal1 size="20" color="#ABABAB" />
              <input
                type="text"
                id="searchInput"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearchChange}
                className="bg-[#fcfcfc] w-full outline-none"
              />
            </div>
            <div className="md:hidden">
              <Filter
                statuses="Status"
                category="Channel"
                stat1="Delivered"
                stat2="Pending"
                stat3="Returned"
                stat4="Shipped"
                handleOrderStatusChange={handleStatusFilterChange}
                data={channels}
                handleOrderChannel={handleStatusChannelChange}
                setDropDown={setDropDown}
                dropDown={dropDown}
                selectedStatus={selectedStatus}
                selectedChannel={selectedChannel}
              />
            </div>
          </div>

          <select
            id="customerFilter"
            value={selectedChannel}
            onChange={handleChannelChange}
            className="py-2 px-4 border text-sm hidden md:inline text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none w-full"
          >
            <option value="">Filter by Channel</option>
            {channels?.map((channel: string) => (
              <option key={channel} value={channel}>
                {channel}
              </option>
            ))}
          </select>
          <select
            id="statusFilter"
            value={selectedStatus}
            onChange={handleStatusChange}
            className="py-2 px-4 border hidden md:inline text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none w-full"
          >
            <option value="">Filter by Status</option>
            <option value="Delivered">Delivered</option>
            <option value="Pending">Pending</option>
            <option value="Returned">Returned</option>
            <option value="Shipped">Shipped</option>
          </select>
        </div>
      </div>
      {isError ? (
        <div className="text-center py-10 mb-5">
          Error loading data, refresh
        </div>
      ) : (
        <div className="relative">
          {isLoading ? (
            <div className="text-center py-10 flex justify-center">
              <img
                className="animate-spin w-[50px] h-[50px]"
                src={logo}
                alt="logo"
              />
            </div>
          ) : (
            <>
              {filteredOrders && filteredOrders.length > 0 ? (
                <>
                  <div className="hidden md:block">
                    <Table
                      rows={paginatedRows}
                      headers={headers}
                      showHead={true}
                      allowRowClick
                      onRowClick={(row) => handleRowClick(row.orderId)}
                    />
                  </div>
                  {paginatedRows.map((product: any, index: any) => (
                    <div key={index} className="md:hidden mt-4 px-5">
                      <ProductCard
                        productName={product.productName}
                        productImage={product.productImage}
                        productSKU={product.orderId}
                        paymentStatus={product.paymentStatus}
                        itemTotal={product.totalItems}
                        totalAmount={product.totalAmount}
                        onClick={() => {
                          handleRowMobile(product.orderId);
                        }}
                      />
                    </div>
                  ))}

                  <div className="px-5 w-full">
                    <Pagination />
                  </div>
                </>
              ) : (
                <div className="flex px-5 flex-col items-center justify-center text-center my-[70px]">
                  {filteredOrders ? (
                    <div className="flex flex-col items-center justify-center text-center mt-8 space-y-4">
                      <img
                        src={OrderEmpyIcon}
                        alt="No Data"
                        className="w-16 h-16"
                      />
                      <p className="text-sm font-sora text-[#5B5B5B]">
                        You currently have no stock movement. When You begin to
                        add and sell product, it will appear here
                      </p>
                    </div>
                  ) : (
                    <p className="mt-6 font-sora text-base font-medium">
                      Order list is currently empty, proceed to add orders
                    </p>
                  )}
                </div>
              )}

              <div className="hidden">
                <div className="p-4 items-center gap-4 justify-end text-[#5A5A5A] text-[14px] flex">
                  <p>0 items</p>

                  <button className="py-3 px-3.5 border rounded-2xl">
                    Previous
                  </button>
                  <button className="py-3 px-3.5 border rounded-2xl">
                    Next
                  </button>
                </div>
              </div>
            </>
          )}

          <div className="mt-6 hidden mb-4 items-center text-[#5A5A5A]  p-4 justify-between">
            <p>0 items</p>
            <div className="flex gap-4">
              <div className="py-3 border border-[#DEDEDE] rounded-2xl px-3">
                <img src={rightArrowIcon} alt="" />
              </div>
              <div className="py-3 border border-[#DEDEDE] rounded-2xl px-3">
                <img src={leftArrowIcon} alt="" />
              </div>
            </div>
          </div>
        </div>
      )}
      {dropdownVisible && modalPosition && (
        <DropDown
          orderId={selectedRow}
          position={modalPosition}
          onViewOrder={handleViewOrder}
          onEditOrder={handleEditOrder}
          onDeleteOrder={openDeleteOrderModal}
          onDownloadInvoice={handleDownloadInvoice}
          onClose={closeDropDown}
        />
      )}
      {viewOrderModal && selectedRow && (
        <OrderDetails
          isOpen={viewOrderModal}
          closeModal={() => setViewOrderModal(false)}
          order={order}
          isLoading={orderDetailsIsLoading}
          openEditOrder={handleEditOrder}
          viewingId={viewOrderModal}
          refetch={refetch}
          deleteSingleOrder={handleDeleteSingleProduct}
          deleteSingleOrderModal={deleteSingleOrderModal}
          setDeleteSingleOrderModal={setDeleteSingleOrderModal}
          orderDetailsRefetch={orderDetailsRefetch}
          deleteLoading={mutation.isLoading}
          handleDownloadInvoice={handleDownloadInvoice}
        />
      )}
      {editOrderModal && selectedRow && (
        <EditOrder
          isOpen={editOrderModal}
          closeModal={() => setEditOrderModal(false)}
          order={order}
          orderDetailsRefetch={orderDetailsRefetch}
          refetch={refetch}
        />
      )}
      {deleteOrderModal && (
        <ConfirmationModal
          isOpen={deleteOrderModal}
          title="Are you sure you want to delete order?"
          text="This action will delete this order, It implies this order would not be part of your orders. "
          actionButtonText={
            mutation.isLoading ? "deleting . . ." : "Delete Order"
          }
          cancelButtonText="No, Cancel"
          onActionClick={handleDeleteEntireOrder}
          onCancelClick={() => setDeleteOrderModal(false)}
          actionButtonStyle="bg-[#DC3545]"
          actionButtonDisabled={mutation.isLoading}
        />
      )}
    </div>
  );
};
export default TableOrder;
