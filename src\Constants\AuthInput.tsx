  interface InputFieldProps {
    label: string;
    type: string;
    name?: string;
    placeholder: string;
    value?: string;
    errorMessage?: string;

    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  }
const AuthInput: React.FC<InputFieldProps> = ({
  label,
  type,
  placeholder,
  value,
  name,
  onChange,
  errorMessage
}) => {
  return (
    <div className="mb-4 w-full">
      <label className="text-[#5B5B5B] font-sora text-sm">{label}</label>
      <input
        type={type}
        className={`${
          errorMessage && 'border-[#E87883] hover:border-[#E87883]'
        } bg-[#FCFCFC] appearance-none  hover:border-primary-purple500 transition-[500] border border-[#cccccc] font-sora text-[14px] mt-2 rounded-[16px] w-full h-[48px] px-3 placeholder:text-[#ABABAB] focus:outline-none focus:shadow-outline`}
        placeholder={placeholder}
        value={value}
        name={name}
        onChange={onChange}
      />
      <p className="text-[#A21C29] text-[10px] text-left mt-0">
        {errorMessage}
      </p>
    </div>
  );
};


  export default AuthInput;
