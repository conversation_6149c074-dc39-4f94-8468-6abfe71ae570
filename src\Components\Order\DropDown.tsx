/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useRef } from 'react';

interface ModalProps {
  orderId: any;
  position: { top: number };
  onClose: () => void;
  onViewOrder: (row: any) => void;
  onEditOrder: (row: any) => void;
  onDeleteOrder: (row: any) => void;
  onDownloadInvoice: (row: any) => void;
}

const DropDown: React.FC<ModalProps> = ({
  orderId,
  position,
  onClose,
  onViewOrder,
  onEditOrder,
  onDeleteOrder,
  onDownloadInvoice,
}) => {
  const modalRef = useRef<HTMLDivElement | null>(null);

  const handleOverlayClick = (event: any) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      onClose();
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleOverlayClick);
    return () => {
      document.removeEventListener('mousedown', handleOverlayClick);
    };
  }, []);

  return (
    <div
      ref={modalRef}
      className="absolute bg-white rounded-2xl font-sora border border-gray-300 shadow-lg  w-[190px]   lg:left-[80%] "
      style={{ top: `${position.top}px` }}>
      <div className="cursor-pointer">
        <p
          onClick={() => onViewOrder(orderId)}
          className="py-2 px-4 text-[#5B5B5B]  rounded-t-2xl font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          View Details
        </p>
        <p
          onClick={() => onEditOrder(orderId)}
          className="py-2 px-4 text-[#5B5B5B] font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          Edit
        </p>
        <p
          onClick={onDownloadInvoice}
          className="py-2 px-4 text-[#5B5B5B] font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          Download Invoice
        </p>
        <p
          onClick={() => onDeleteOrder(orderId)}
          className="py-2 px-4  text-[#DC3545] font-normal">
          Delete/Cancel Order
        </p>
      </div>
    </div>
  );
};

export default DropDown;
