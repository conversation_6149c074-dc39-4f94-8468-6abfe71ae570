/* eslint-disable @typescript-eslint/no-explicit-any */

import { ArrowDown, ArrowRight, ArrowUp, Box, InfoCircle } from 'iconsax-react';
import AnalyticButton from '../../Constants/AnalyticButton';
import InventorydataCard from './InventorydataCard';
import DateHead from './DateHead';
import { useEffect, useState } from 'react';
import LowStockModal from './LowStockModal';
import AgingStockModal from './AgingStockModal';

const InventoryData = ({ data, isError, isLoading }: any) => {
  const [lowStockModal, setLowStockModal] = useState<boolean>(false);
  const [agingStockModal, setAgingStockModal] = useState<boolean>(false);

  const handleAgingStockModal = () => {
    setAgingStockModal((prev) => !prev);
  };
  const handleLowStockModal = () => {
    setLowStockModal((prev) => !prev);
  };
  // const tshirt = 30;
  // const hoodie = 20;
  // const dress = 10;
  useEffect(() => {
    document.title = 'Inventory Data - Analytics';
  });
  return (
    <div className="font-sora">
      <DateHead />
      {isError ? (
        <h2 className="flex justify-center items-center py-16">
          An Error Occurred, Please Refresh
        </h2>
      ) : (
        <div className="flex flex-col lg:flex-row gap-6">
          <div className="w-full max-w-[448px]">
            <div className="border rounded-xl py-4 px-6  ">
              <div className="flex gap-4 items-center">
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                ) : (
                  <div className="bg-[#E6E5F9] p-1.5 rounded-full">
                    <Box size="16" color="#4F4CD8" variant="Bold" />
                  </div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                ) : (
                  <p className="font-semibold text-lg text-[#2A2A2A]">
                    Total Value Of Current Stock
                  </p>
                )}
              </div>
              <div className="flex my-6 justify-between">
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                ) : (
                  <p className="font-semibold text-[#1E1B39] text-2xl">
                    ₦{' '}
                    {parseFloat(
                      data?.totalvalueofcurrentstock?.StockValue
                    ).toLocaleString()}
                  </p>
                )}
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 rounded-3xl w-[40px] "></div>
                ) : (
                  <p
                    className={`flex items-center rounded-3xl  text-[10px] p-1 ${
                      data?.totalvalueofcurrentstock?.percentageChange < 1
                        ? 'border-[#DC3545] border'
                        : 'border-[#AEEBBC] border'
                    } `}>
                    <span
                      className={`${
                        data?.totalvalueofcurrentstock?.percentageChange < 1
                          ? 'text-[#DC3545]'
                          : 'text-[#28A745]'
                      }`}>
                      {data?.totalvalueofcurrentstock?.percentageChange} %
                    </span>
                    {data?.totalvalueofcurrentstock?.percentageChange < 1 ? (
                      <ArrowDown className="text-[#DC3545]" size="12" />
                    ) : (
                      <ArrowUp className="text-[#28A745]" size="12" />
                    )}
                  </p>
                )}
              </div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] max-w-[320px]"></div>
              ) : (
                <div className="text-[#9B9B9B] text-sm font-normal flex gap-2 items-center">
                  <InfoCircle color="#9A99E9" />
                  <span>
                    This shows the total value of all the products you currently
                    have in stock based on their cost
                  </span>
                </div>
              )}
            </div>
            <div className="border rounded-xl py-4 px-6  mt-6">
              <div className="flex gap-4 items-center">
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                ) : (
                  <div className="bg-[#E6E5F9] p-1.5 rounded-full">
                    <Box size="16" color="#4F4CD8" variant="Bold" />
                  </div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                ) : (
                  <p className="font-semibold text-lg text-[#2A2A2A]">
                    Low Stock Alert{' '}
                  </p>
                )}
              </div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[180px] my-6"></div>
              ) : (
                <p className="font-normal text-sm text-[#7B7B7B] my-6">
                  See your low stock alerts{' '}
                </p>
              )}

              <div>
                {data?.LowStockAlert?.slice(0, 3).map(
                  (item: any, index: any) => (
                    <div key={index} className="mb-4">
                      {isLoading ? (
                        <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[180px]"></div>
                      ) : (
                        <div className="flex gap-4  items-center">
                          <img
                            src={item.productImage || ''}
                            alt={item.productName || 'product'}
                            className="w-[32px]  h-[32px] object-cover"
                          />
                          <span className="font-bold text-xs text-[#5A5A5A]">
                            {item.productName || ''}
                          </span>
                        </div>
                      )}
                      {isLoading ? (
                        <div className="rounded-2xl animate-pulse bg-slate-200 h-[28px] my-5"></div>
                      ) : (
                        <div className="relative w-full h-8 bg-[#FFF9E6] mt-2">
                          <div
                            className="absolute top-0 left-0 h-full bg-[#FFBF60]"
                            style={{
                              width: `${Math.min(
                                (item.currentStock / 100) * 100,
                                100
                              )}%`,
                            }}></div>
                        </div>
                      )}
                      {isLoading ? (
                        <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[130px]"></div>
                      ) : (
                        <p className="text-[#6B6B6B] text-xs mt-[10px] font-semibold leading-4">
                          {item.currentStock || 0} pcs left
                        </p>
                      )}
                    </div>
                  )
                )}
              </div>

              {/* <div>
              <div className="flex gap-4 items-center ">
                <img
                  src={
                    (data?.lowStockAlert &&
                      data?.lowStockAlert[0]?.productImage) ||
                    ''
                  }
                  alt="product"
                  className="w-[32px] h-[32px]"
                />
                <span className="font-bold text-xs text-[#5A5A5A]">
                  {(data?.lowStockAlert &&
                    data?.lowStockAlert[0]?.productName) ||
                    ''}
                </span>
              </div>
              <div className="relative w-full h-8 bg-[#FFF9E6] mt-2">
                <div
                  className="absolute top-0 left-0 h-full bg-[#FFBF60]"
                  style={{ width: `${tshirt || 0}%` }}></div>
              </div>
              <p className="text-[#6B6B6B] text-xs font-semibold leading-4">
                {(data?.lowStockAlert && data?.lowStockAlert[0]?.stockLevel) ||
                  ''}
              </p>
            </div>
            <div className="my-6">
              <div className="flex gap-4 items-center ">
                <img
                  src={
                    (data?.lowStockAlert &&
                      data?.lowStockAlert[1]?.productImage) ||
                    ''
                  }
                  alt="product"
                  className="w-[32px] h-[32px]"
                />
                <span className="font-bold text-xs text-[#5A5A5A]">
                  {(data?.lowStockAlert &&
                    data?.lowStockAlert[1]?.productName) ||
                    ''}
                </span>
              </div>
              <div className="relative w-full h-8 bg-[#FFF9E6] mt-2">
                <div
                  className="absolute top-0 left-0 h-full bg-[#FFBF60]"
                  style={{ width: `${dress || 0}%` }}></div>
              </div>
              <p className="text-[#6B6B6B] text-xs font-semibold leading-4">
                {(data?.lowStockAlert && data?.lowStockAlert[1]?.stockLevel) ||
                  ''}
              </p>
            </div>
            <div>
              <div className="flex gap-4 items-center ">
                <img
                  src={
                    data?.lowStockAlert && data?.lowStockAlert[2]?.productImage
                  }
                  alt="product"
                  className="w-[32px] h-[32px]"
                />
                <span className="font-bold text-xs text-[#5A5A5A]">
                  {data?.lowStockAlert && data?.lowStockAlert[2]?.productName}
                </span>
              </div>
              <div className="relative w-full h-8 bg-[#FFF9E6] mt-2">
                <div
                  className="absolute top-0 left-0 h-full bg-[#FFBF60]"
                  style={{ width: `${hoodie || 0}%` }}></div>
              </div>
              <p className="text-[#6B6B6B] text-xs font-semibold leading-4">
                {data?.lowStockAlert &&  data?.lowStockAlert[2]?.stockLevel}
              </p>
            </div> */}
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[36px] max-w-[100px]"></div>
              ) : (
                <AnalyticButton
                  title="View More"
                  image={ArrowRight}
                  onClick={handleLowStockModal}
                />
              )}
            </div>
            <div className="border rounded-xl py-4 px-6  mt-6">
              <div className="flex gap-4 items-center">
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                ) : (
                  <div className="bg-[#E6E5F9] p-1.5 rounded-full">
                    <Box size="16" color="#4F4CD8" variant="Bold" />
                  </div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                ) : (
                  <p className="font-semibold text-lg text-[#2A2A2A]">
                    Aging Stock{' '}
                  </p>
                )}
              </div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] max-w-[320px] my-6"></div>
              ) : (
                <p className="font-normal text-sm text-[#7B7B7B] my-6">
                  {data?.AgingStockDescription}
                </p>
              )}
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[36px] max-w-[100px]"></div>
              ) : (
                <AnalyticButton
                  title="View More"
                  image={ArrowRight}
                  onClick={handleAgingStockModal}
                />
              )}
            </div>
            <div className="border rounded-xl py-4 px-6  mt-6">
              <div className="flex gap-4 items-center">
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                ) : (
                  <div className="bg-[#E6E5F9] p-1.5 rounded-full">
                    <Box size="16" color="#4F4CD8" variant="Bold" />
                  </div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                ) : (
                  <p className="font-semibold text-lg text-[#2A2A2A]">
                    Products Performance{' '}
                  </p>
                )}
              </div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] max-w-[320px] my-6"></div>
              ) : (
                <p className="font-normal text-sm text-[#7B7B7B] my-6">
                  {data?.productsPerformance}
                </p>
              )}
            </div>
          </div>
            <InventorydataCard data={data} isLoading={isLoading} />
        </div>
      )}

      {lowStockModal && (
        <LowStockModal
          isOpen={lowStockModal}
          onClose={handleLowStockModal}
          data={data}
        />
      )}
      {agingStockModal && (
        <AgingStockModal
          isOpen={agingStockModal}
          onClose={handleAgingStockModal}
          data={data}
        />
      )}
    </div>
  );
};
export default InventoryData;
