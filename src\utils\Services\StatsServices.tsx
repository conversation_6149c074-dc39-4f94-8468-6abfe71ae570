import KatajereAPI from "../KatajereAPI";

export const StatsService = {
  OverviewStats: async (payload: {
    vendorId: string;
    startDate?: string;
    endDate?: string;
  }) => {
    const queryParams = new URLSearchParams({
      vendorId: payload.vendorId,
      ...(payload.startDate && { startDate: payload.startDate }),
      ...(payload.endDate && { endDate: payload.endDate }),
    });
    return await KatajereAPI().get(`/stats?${queryParams}`);
  },
  AiRecommendations: async (payload: { vendorId: string }) => {
    const queryParams = new URLSearchParams({ vendorId: payload.vendorId });
    return await KatajereAPI().get(`/ai-recommendation?${queryParams}`);
  },
};
