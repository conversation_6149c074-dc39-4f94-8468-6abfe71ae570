import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import dotenv from 'dotenv';
import { visualizer } from 'rollup-plugin-visualizer';
import { VitePWA } from 'vite-plugin-pwa';

dotenv.config();

export default defineConfig(({ mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [
      react(),
      VitePWA({
        registerType: 'autoUpdate',
        manifest: {
          short_name: '<PERSON>x<PERSON><PERSON>',
          name: '<PERSON>x<PERSON><PERSON>',
          icons: [
            {
              src: '/icons/Purple192.png',
              sizes: '192x192',
              type: 'image/png',
            },
            {
              src: '/icons/Purple512.png',
              sizes: '512x512',
              type: 'image/png',
            },
            {
              src: '/icons/Purple180.png',
              sizes: '180x180',
              type: 'image/png',
            },
          ],
          start_url: env.VITE_BASE_URL || '/', 
          display: 'standalone',
          background_color: '#ffffff',
          theme_color: '#4F4CD8',
          lang: 'en',
          scope: env.VITE_BASE_URL || '/', 
          dir: 'ltr',
          orientation: 'portrait',
          categories: ['finance'],
          description: 'Huxxle',
        },
        workbox: {
          runtimeCaching: [
            {
              urlPattern: /\/api\/.*/,
              handler: 'StaleWhileRevalidate',
              options: {
                cacheName: 'api-cache',
                expiration: {
                  maxEntries: 50,
                  maxAgeSeconds: 24 * 60 * 60, 
                },
              },
            },
          ],
        },
        injectManifest: {
          swSrc: 'src/service-worker.js', 
          swDest: 'service-worker.js', 
        },
      }),
      visualizer({
        filename: './dist/bundle-stats.html',
        open: true,
      }),
    ],
    server: {
      host: '0.0.0.0',
      port: 3001,
      proxy: {
        '/pg/alert': {
          target: 'https://api.huxxle.com', // Your backend server URL
          changeOrigin: true,
          secure: false,
        },
      },
    },
    build: {
      sourcemap: false,
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return id
                .toString()
                .split('node_modules/')[1]
                .split('/')[0]
                .toString();
            }
          },
        },
      },
      chunkSizeWarningLimit: 500, 
    },
  };
});
