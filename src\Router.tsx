import { createBrowserRouter } from 'react-router-dom';
import LoginPage from './Pages/LoginPage';
import Inventory from './Pages/Inventory';
import Dashboard from './Pages/Dashboard';
import Overview from './Pages/Overview';
import Verify from './Pages/Verify';
import Order from './Pages/Order';
import TrackStock from './Components/Inventory/TrackStock/TrackStock';
import PromoSales from './Components/Order/PromoSales';
import Analytics from './Pages/Analytics';
import ManageCustomer from './Components/Order/ManageCustomer';
import AddBussiness from './Components/Overview/AddBussiness';
import Alert from './Components/Inventory/ManageAlert/Alert';
import AccountSettings from './Pages/AccountSettings';
import WelcomeVerify from './Components/WelcomeVerify';
import InventoryData from './Components/Analytics/InventoryData';
import NotFound from './Pages/NotFound';
import WebsiteSettings from './Pages/WebsiteSettings';

const router = createBrowserRouter([
  {
    path: '/',
    element: <LoginPage />,
  },

  {
    path: '/verify-signup',
    element: <Verify isSignup={true} />,
  },
  {
    path: '/verify-login',
    element: <Verify isSignup={false} />,
  },
  {
    path: '/welcome',
    element: <WelcomeVerify />,
  },
  {
    path:'/website-settings',
    element: <WebsiteSettings/>
  },
  {
    errorElement: (
      <div className="flex justify-center items-center h-screen">
        Sorry, an error occured. Kindly refresh the page.
      </div>
    ),
    element: <Dashboard />,
    children: [
      {
        path: '/overview',
        children: [
          {
            index: true,
            element: <Overview />,
          },
          {
            path: 'add-business',
            element: <AddBussiness />,
          },
        ],
      },
      {
        path: '/inventory',
        children: [
          {
            index: true,
            element: <Inventory />,
          },
          {
            path: 'trackstock',
            element: <TrackStock />,
          },
          {
            path: 'manage-alerts',
            element: <Alert />,
          },
        ],
      },

      {
        path: '/order',
        children: [
          {
            index: true,
            element: <Order />,
          },
          {
            path: 'promo',
            element: <PromoSales />,
          },
          {
            path: 'customer',
            element: <ManageCustomer />,
          },
        ],
      },
      {
        path: '/analytics',
        children: [
          {
            index: true,
            element: <Analytics />,
          },
          {
            path: 'inventory-data',
            element: <InventoryData />,
          },
        ],
      },
      {
        path: '/account-settings',
        element: <AccountSettings />,
      },
    ],
  },
  {
    path: '*',
    element: <NotFound />,
    errorElement: (
      <div className="flex justify-center items-center h-screen">
        Sorry, an error occured. Kindly refresh the page.
      </div>
    ),
  },
]);

export default router;
