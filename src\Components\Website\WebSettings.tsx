import { Link } from 'react-router-dom';
import { RouteNames } from '../../utils/RouteNames';
import { ArrowDown2, <PERSON><PERSON><PERSON><PERSON>, ArrowUp2, Copy } from 'iconsax-react';
import { useState } from 'react';
import { useUserAuthStore } from '../../store/auth';
import bg from '../../assets/defaultBg.png';
import template1 from '../../assets/template1.png';
import template2 from '../../assets/template2.png';
import DefaultTemplate from './DefaultTemplate';
import SecoundTemplate from './SecoundTemplate';

const WebSettings = () => {
  const [webTemplate, setWebTemplate] = useState<boolean>(false);
  const [webAddress, setWebAddress] = useState<boolean>(false);
  const [webContent, setWebContent] = useState<boolean>(false);
  const [webLinks, setWebLinks] = useState<boolean>(false);
  const [primaryColor, setPrimaryColor] = useState<string>('#4f4cd8');
  const [bgImage, setBgImage] = useState<string>(bg);
  const business = useUserAuthStore((state) => state.business);
  const user = useUserAuthStore((state) => state.user);
  const [selectedColor, setSelectedColor] = useState('#4f4cd8');
  const [selectedTemplate, setSelectedTemplate] = useState(0);
  const [customDomain, setCustomDomain] = useState('www.dysonclothing');
  const [headline, setHeadline] = useState(
    'Yaay! You have reached Dyson Clothing'
  );
  const [subHeadline, setSubHeadline] = useState(
    'Find everything you need, from the latest trends to everyday essentials. What would you like to buy today?'
  );
  const [useDefaultBanner, setUseDefaultBanner] = useState(true);

  const colors = [
    '#4F4CD8',
    '#FF9900',
    '#28A745',
    '#DC3545',
    '#E042F5',
    '#4CC5D8',
    '#69443F',
    '#1A1A1A',
  ];
  const templates = [
    {
      id: 0,
      name: 'Default Template',
      preview: template1,
    },
    {
      id: 1,
      name: 'Modern Template',
      preview: template2,
    },
  ];
  // control setting views
  const showTemplates = () => {
    setWebTemplate((prev) => !prev);
  };

  const showAddress = () => {
    setWebAddress((prev) => !prev);
  };
  const showContent = () => {
    setWebContent((prev) => !prev);
  };
  const showLinks = () => {
    setWebLinks((prev) => !prev);
  };
  const copyToClipboard = () => {
    navigator.clipboard.writeText(`${customDomain}.huxxle.shop`);
  };
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Handle file upload logic here
      console.log('File selected:', file);
    }
  };
  return (
    <div className="w-full h-screen  bg-[#F5F5F5] flex gap-[80px]">
      {/* LEFT SIDE  */}
      <div className="md:w-[450px] h-full flex flex-col gap-[64px] w-full bg-white border-r-[2px] border-[#DCDCDC] md:p-10">
        <div className="w-full flex justify-center">
          <Link
            className=" bg-[#E6E5F9] text-[14px] hover:bg-[#4f4cd8] hover:text-white duration-500 flex items-center gap-4 justify-center py-4 rounded-2xl w-full text-primary-purple  font-sora font-semibold"
            to={RouteNames.overview}>
            <ArrowLeft /> Return Home
          </Link>
        </div>

        {/* Settings */}
        <div className=" flex flex-col gap-6">
          <div className="space-y-[4px]">
            <h3 className="font-sora text-[24px] text-[#181818] font-semibold">
              Manage Your Website
            </h3>
            <p className="font-sora text-[14px] text-[#919191]">
              {' '}
              You can always update your website settings
            </p>
          </div>
          {/* Website Template Section */}
          <div className="border-y border-[#DEE2E6] py-6">
            <div
              className="flex items-center justify-between cursor-pointer"
              onClick={showTemplates}>
              <h3 className="text-[16px] font-semibold text-[#2a2a2a]">
                Website Template
              </h3>
              <div>
                {!webTemplate ? (
                  <ArrowDown2 size={20} />
                ) : (
                  <ArrowUp2 size={20} />
                )}
              </div>
            </div>
            {/* Dropdown Content */}
            {webTemplate && (
              <div className="mt-6 space-y-6">
                <div className="space-y-4">
                  <h4 className="text-[14px] font-medium text-[#2a2a2a]">
                    Style
                  </h4>
                  <div className="flex gap-4">
                    {templates.map((template) => (
                      <div
                        key={template.id}
                        className={`relative cursor-pointer rounded-lg border-2 overflow-hidden ${
                          selectedTemplate === template.id
                            ? 'border-green-500'
                            : 'border-gray-200'
                        }`}
                        onClick={() => setSelectedTemplate(template.id)}>
                        <div className="h-[64px] w-[64px] bg-gray-100 p-1 flex items-center justify-center">
                          {template.id === 0 ? (
                            <img
                              src={template.preview}
                              alt="template preview"
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <img
                              src={template.preview}
                              alt="template preview"
                              className="w-full h-full object-cover"
                            />
                          )}
                        </div>
                        {selectedTemplate === template.id && (
                          <div className="absolute bottom-1 right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                            <svg
                              width="12"
                              height="12"
                              viewBox="0 0 12 12"
                              fill="none">
                              <path
                                d="M2 6L5 9L10 3"
                                stroke="white"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Color Section */}
                <div className="space-y-4">
                  <h4 className="text-[14px] font-medium text-[#2a2a2a]">
                    Color
                  </h4>
                  <div className="flex gap-3">
                    {colors.map((color, index) => (
                      <div
                        key={index}
                        className={`w-8 h-8 rounded-full cursor-pointer border-2 ${
                          selectedColor === color
                            ? 'border-[2px]'
                            : 'border-gray-300'
                        }`}
                        style={{
                          backgroundColor: color,
                          borderColor: selectedColor === color ? color : '',
                        }}
                        onClick={() => {
                          setSelectedColor(color);
                          setPrimaryColor(color);
                        }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="py-6 flex flex-col gap-6 border-y-[1px] border-[#DEE2E6]">
            <div className=" flex items-center justify-between">
              <h3 className="font-sora text-[16px] font-semibold text-[#2a2a2a]">
                Website Address
              </h3>
              <div onClick={showAddress} className="cursor-pointer">
                {!webAddress ? <ArrowDown2 /> : <ArrowUp2 />}
              </div>
            </div>
            {/* Website Address Dropdown Content */}
            {webAddress && (
              <div className="mt-6">
                <div className="flex items-center gap-2 border border-[#DEE2E6] rounded-2xl ">
                  <input
                    type="text"
                    value={customDomain}
                    onChange={(e) => setCustomDomain(e.target.value)}
                    className="flex-1 px-4 py-3  rounded-2xl text-[14px] font-medium text-[#2a2a2a] focus:outline-none focus:border-[#4f4cd8]"
                    placeholder="Enter your domain"
                  />
                  <div className="flex items-center h-[38px]  px-3  border border-[#DEE2E6] rounded-xl text-[14px] font-medium text-[#2a2a2a]">
                    .huxxle.shop
                  </div>
                  <button
                    onClick={copyToClipboard}
                    className="flex items-center justify-center w-12 h-12  bg-gray-50 border border-[#DEE2E6] rounded-2xl hover:bg-gray-100 transition-colors">
                    <Copy size={20} color="#6B7280" />
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="py-6 flex flex-col gap-6 border-y-[1px] border-[#DEE2E6]">
            <div className=" flex items-center justify-between">
              <h3 className="font-sora text-[16px] font-semibold text-[#2a2a2a]">
                Website Content
              </h3>
              <div onClick={showContent} className="cursor-pointer">
                {!webContent ? <ArrowDown2 /> : <ArrowUp2 />}
              </div>
            </div>
            {/* Website Content Dropdown */}
            {webContent && (
              <div className="mt-6 space-y-6">
                {/* Headline */}
                <div className="space-y-2">
                  <label className="text-[14px] font-medium text-[#2a2a2a]">
                    Headline
                  </label>
                  <input
                    type="text"
                    value={headline}
                    onChange={(e) => setHeadline(e.target.value)}
                    className="w-full px-4 py-3 border border-[#DEE2E6] rounded-2xl text-[14px] text-[#2a2a2a] focus:outline-none focus:border-[#4f4cd8]"
                    placeholder="Enter your headline"
                  />
                </div>

                {/* Sub headline */}
                <div className="space-y-2">
                  <label className="text-[14px] font-medium text-[#2a2a2a]">
                    Sub headline
                  </label>
                  <textarea
                    value={subHeadline}
                    onChange={(e) => setSubHeadline(e.target.value)}
                    rows={4}
                    className="w-full px-4 py-3 border border-[#DEE2E6] rounded-2xl text-[14px] text-[#2a2a2a] focus:outline-none focus:border-[#4f4cd8] resize-none"
                    placeholder="Enter your sub headline"
                  />
                </div>

                {/* Use Default Banner */}
                <div
                  className={`p-4 border-2 rounded-2xl cursor-pointer transition-colors ${
                    useDefaultBanner
                      ? 'border-green-500 bg-green-50'
                      : 'border-[#DEE2E6] bg-white'
                  }`}
                  onClick={() => setUseDefaultBanner(true)}>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-100 rounded border flex items-center justify-center">
                      {useDefaultBanner && (
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none">
                          <path
                            d="M3 8L7 12L13 4"
                            stroke="#10B981"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      )}
                    </div>
                    <span className="text-[14px] font-medium text-[#2a2a2a]">
                      Use Default Banner
                    </span>
                  </div>
                </div>

                {/* Upload Banner */}
                <div className="space-y-2">
                  <label className="text-[14px] font-medium text-[#2a2a2a]">
                    Upload a banner photo
                  </label>
                  <div className="border-2 border-dashed border-[#4f4cd8] rounded-2xl p-8 text-center">
                    <div className="flex flex-col items-center space-y-3">
                      <svg
                        width="48"
                        height="48"
                        viewBox="0 0 48 48"
                        fill="none">
                        <path
                          d="M24 4V44M44 24H4"
                          stroke="#4f4cd8"
                          strokeWidth="2"
                          strokeLinecap="round"
                        />
                        <path
                          d="M34 14L24 4L14 14"
                          stroke="#4f4cd8"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="space-y-1">
                        <p className="text-[14px] font-medium text-[#2a2a2a]">
                          Click here to upload or Drag & Drop
                        </p>
                        <p className="text-[12px] text-[#919191]">
                          Maximum file size is 10mb
                        </p>
                      </div>
                    </div>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="py-6 flex flex-col gap-6 border-y-[1px] border-[#DEE2E6]">
            <div className=" flex items-center justify-between">
              <h3 className="font-sora text-[16px] font-semibold text-[#2a2a2a]">
                Social Media Links
              </h3>
              <div onClick={showLinks} className="cursor-pointer">
                {!webLinks ? <ArrowDown2 /> : <ArrowUp2 />}
              </div>
            </div>
          </div>
        </div>
        <div>
          <div className="flex flex-col md:flex-row w-full  gap-2.5">
            <button className="bg-transparent order-2 md:order-1 text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 py-4 rounded-2xl cursor-pointer flex-1/2">
              Back
            </button>
            <button
              type="button"
              className={`w-full text-[#fcfcfc] order-1 md:order-2 bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl cursor-pointer flex-2 `}>
              Save
            </button>
          </div>
        </div>
      </div>

      {/* PREVIEW PAGE */}
      <div className="md:w-[830px] py-5">
        {selectedTemplate === 0 ? (
          <DefaultTemplate
            user={user}
            business={business}
            primaryColor={primaryColor}
            bg={bgImage}
          />
        ) : (
          <SecoundTemplate
            user={user}
            business={business}
            primaryColor={primaryColor}
          />
        )}
      </div>
    </div>
  );
};

export default WebSettings;
