/* eslint-disable @typescript-eslint/no-explicit-any */
import { Link } from 'react-router-dom';
import { RouteNames } from '../../utils/RouteNames';
import {
  ArrowDown2,
  <PERSON>Left,
  <PERSON>Up2,
  <PERSON><PERSON>,
  Tick<PERSON>ircle,
} from 'iconsax-react';
import { useState } from 'react';
import { useUserAuthStore } from '../../store/auth';
import { toast } from 'react-toastify';
import bg from '../../assets/defaultBg.png';
import template1 from '../../assets/template1.png';
import template2 from '../../assets/template2.png';
import DefaultTemplate from './DefaultTemplate';
import SecoundTemplate from './SecoundTemplate';

interface SocialLinks {
  facebook: string;
  instagram: string;
  twitter: string;
  tiktok: string;
}

type SocialPlatform = keyof SocialLinks;
const WebSettings = () => {
  const [webTemplate, setWebTemplate] = useState(false);
  const [webAddress, setWebAddress] = useState(false);
  const [webContent, setWebContent] = useState(true); 
  const [webLinks, setWebLinks] = useState(false);
  const [primaryColor, setPrimaryColor] = useState('#4f4cd8');
  const business = useUserAuthStore((state) => state.business);
  const user = useUserAuthStore((state) => state.user);
  const [selectedColor, setSelectedColor] = useState('#4f4cd8');
  const [selectedTemplate, setSelectedTemplate] = useState(0);
  const [customDomain, setCustomDomain] = useState('www.dysonclothing');
  const [headline, setHeadline] = useState(
    'Yaay! You have reached Dyson Clothing'
  );
  const [subHeadline, setSubHeadline] = useState(
    'Find everything you need, from the latest trends to everyday essentials. What would you like to buy today?'
  );
  const [useDefaultBanner, setUseDefaultBanner] = useState(true);
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [socialLinks, setSocialLinks] = useState({
    facebook: '',
    instagram: '',
    twitter: '',
    tiktok: '',
  });

  const colors = [
    '#4F4CD8',
    '#FF9900',
    '#28A745',
    '#DC3545',
    '#E042F5',
    '#4CC5D8',
    '#69443F',
    '#1A1A1A',
  ];

  const templates = [
    {
      id: 0,
      name: 'Default Template',
      preview: template1,
    },
    {
      id: 1,
      name: 'Modern Template',
      preview: template2,
    },
  ];

  const bannerOptions = [
    {
      id: 'fashion',
      title: 'Fashion & Clothing',
      image:
        'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop',
    },
    {
      id: 'groceries',
      title: 'Groceries',
      image:
        'https://images.unsplash.com/photo-1542838132-92c53300491e?w=400&h=300&fit=crop',
    },
    {
      id: 'kitchen',
      title: 'Kitchen Items',
      image:
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',
    },
    {
      id: 'gadgets',
      title: 'Gadgets',
      image:
        'https://images.unsplash.com/photo-1468495244123-6c6c332eeece?w=400&h=300&fit=crop',
    },
    {
      id: 'interior',
      title: 'Interior Decor',
      image:
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
    },
    {
      id: 'beauty',
      title: 'Beauty & Scents',
      image:
        'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=300&fit=crop',
    },
    {
      id: 'garden',
      title: 'Gadgets',
      image:
        'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=300&fit=crop',
    },
    {
      id: 'kids',
      title: "Baby's & Kids",
      image:
        'https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?w=400&h=300&fit=crop',
    },
    {
      id: 'food',
      title: 'Food',
      image:
        'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
    },
    {
      id: 'automobiles',
      title: 'Automobiles',
      image:
        'https://images.unsplash.com/photo-1549317661-bd32c8ce0db2?w=400&h=300&fit=crop',
    },
  ];

  const handleSocialLinkChange = (platform: SocialPlatform, value: string) => {
    setSocialLinks((prev: SocialLinks) => ({
      ...prev,
      [platform]: value,
    }));
  };

  const showTemplates = () => {
    setWebTemplate((prev) => !prev);
  };

  const showAddress = () => {
    setWebAddress((prev) => !prev);
  };

  const showContent = () => {
    setWebContent((prev) => !prev);
  };

  const showLinks = () => {
    setWebLinks((prev) => !prev);
  };

  const handleBannerSelect = (bannerId: any) => {
    setSelectedBanner(bannerId);
    setUseDefaultBanner(false);
  };

  const handleDefaultBanner = () => {
    setUseDefaultBanner(true);
    setSelectedBanner(null);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(`${customDomain}.huxxle.shop`);
    toast.success('Domain copied to clipboard!');
  };

  return (
    <div className="w-full h-screen bg-[#F5F5F5] flex gap-[80px] font-sora">
      {/* LEFT SIDE */}
      <div className="md:w-[450px] h-full flex flex-col gap-[64px] w-full bg-white border-r-[2px] border-[#DCDCDC] md:p-10">
        <div className="w-full flex justify-center">
          <Link
            className="bg-[#E6E5F9] text-sm hover:bg-[#4f4cd8] hover:text-white duration-500 flex items-center gap-4 justify-center py-4 rounded-2xl w-full text-primary-purple font-sora font-semibold"
            to={RouteNames.overview}>
            <ArrowLeft /> Return to Home
          </Link>
        </div>
        <div className="flex flex-col gap-6">
          <div className="space-y-[4px]">
            <h3 className="font-sora text-[24px] text-[#181818] font-semibold">
              Manage Your Website
            </h3>
            <p className="font-sora text-sm text-[#919191]">
              You can always update your website settings
            </p>
          </div>
          <div className="border-y border-[#DEE2E6] py-6">
            <div
              className="flex items-center justify-between cursor-pointer"
              onClick={showTemplates}>
              <h3 className="text-[16px] font-semibold text-[#2a2a2a]">
                Website Template
              </h3>
              <div>
                {!webTemplate ? (
                  <ArrowDown2 size={20} />
                ) : (
                  <ArrowUp2 size={20} />
                )}
              </div>
            </div>
            {webTemplate && (
              <div className="mt-6 space-y-6">
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-[#2a2a2a]">Style</h4>
                  <div className="flex gap-4">
                    {templates.map((template) => (
                      <div
                        key={template.id}
                        className={`relative cursor-pointer rounded-lg border-2 overflow-hidden ${
                          selectedTemplate === template.id
                            ? 'border-green-500'
                            : 'border-gray-200'
                        }`}
                        onClick={() => setSelectedTemplate(template.id)}>
                        <div className="h-[64px] w-[64px] bg-gray-100 p-1 flex items-center justify-center">
                          <img
                            src={template.preview}
                            alt="template preview"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        {selectedTemplate === template.id && (
                          <div className="absolute bottom-1 right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                            <TickCircle size={12} color="white" />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-[#2a2a2a]">Color</h4>
                  <div className="flex gap-3">
                    {colors.map((color, index) => (
                      <div
                        key={index}
                        className={`w-8 h-8 rounded-full cursor-pointer border-2 ${
                          selectedColor === color
                            ? 'border-[2px]'
                            : 'border-gray-300'
                        }`}
                        style={{
                          backgroundColor: color,
                          borderColor: selectedColor === color ? color : '',
                        }}
                        onClick={() => {
                          setSelectedColor(color);
                          setPrimaryColor(color);
                        }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="py-6 flex flex-col gap-6 border-y-[1px] border-[#DEE2E6]">
            <div className="flex items-center justify-between">
              <h3 className="font-sora text-[16px] font-semibold text-[#2a2a2a]">
                Website Address
              </h3>
              <div onClick={showAddress} className="cursor-pointer">
                {!webAddress ? <ArrowDown2 /> : <ArrowUp2 />}
              </div>
            </div>
            {webAddress && (
              <div className="mt-6">
                <div className="flex items-center gap-2 border border-[#DEE2E6] rounded-2xl">
                  <input
                    type="text"
                    value={customDomain}
                    onChange={(e) => setCustomDomain(e.target.value)}
                    className="flex-1 pl-2 py-3 rounded-2xl text-sm font-medium text-[#2a2a2a] focus:outline-none "
                    placeholder="Enter your domain"
                  />
                  <div className="flex items-center h-[38px] px-3 border border-[#DEE2E6] rounded-xl text-[14px] font-medium text-[#2a2a2a]">
                    .huxxle.shop
                  </div>
                  <button
                    onClick={copyToClipboard}
                    className="flex items-center justify-center w-12 h-12 bg-gray-50 border border-[#DEE2E6] rounded-2xl hover:bg-gray-100 transition-colors">
                    <Copy size={20} color="#6B7280" />
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="py-6 flex flex-col gap-6 border-y-[1px] border-[#DEE2E6]">
            <div className="flex items-center justify-between">
              <h3 className="font-sora text-[16px] font-semibold text-[#2a2a2a]">
                Website Content
              </h3>
              <div onClick={showContent} className="cursor-pointer">
                {!webContent ? <ArrowDown2 /> : <ArrowUp2 />}
              </div>
            </div>
            {webContent && (
              <div className=" space-y-6">
                <div className="space-y-2">
                  <label className="text-sm text-[#5B5B5B]">Headline</label>
                  <input
                    type="text"
                    value={headline}
                    onChange={(e) => setHeadline(e.target.value)}
                    className="w-full px-4 py-3 border border-[#DEE2E6] rounded-2xl text-sm text-[#2a2a2a] focus:outline-none "
                    placeholder="Enter your headline"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm text-[#5B5B5B]">Sub headline</label>
                  <textarea
                    value={subHeadline}
                    onChange={(e) => setSubHeadline(e.target.value)}
                    rows={4}
                    className="w-full px-4 py-3 border border-[#DEE2E6] rounded-2xl text-sm text-[#2a2a2a] focus:outline-none  resize-none"
                    placeholder="Enter your sub headline"
                  />
                </div>
                <div
                  className={` cursor-pointer rounded-2xl border overflow-hidden flex gap-2.5 p-2 items-center border-[#CCCCCC]`}
                  onClick={handleDefaultBanner}>
                  <div className="h-[64px] w-[64px] relative  flex items-center justify-center">
                    <img
                      src={bg}
                      alt="default banner"
                      className={`w-full h-full object-cover border rounded-2xl ${
                        useDefaultBanner && 'border-[#28A745]'
                      }`}
                    />
                    {useDefaultBanner && (
                      <div className="absolute bottom-3 right-2 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                        <TickCircle size={12} color="white" />
                      </div>
                    )}
                  </div>

                  <span className="text-base text-[#2a2a2a]">
                    Use Default Banner
                  </span>
                </div>
                <div className="flex items-center justify-center">
                  <span className="text-[14px] text-[#919191] font-medium">
                    OR
                  </span>
                </div>

                <div className="grid grid-cols-3 gap-3 justify-center">
                  {bannerOptions.map((banner) => (
                    <div
                      key={banner.id}
                      className={`cursor-pointer flex flex-col items-center  ${
                        selectedBanner === banner.id
                          ? 'border-green-500'
                          : 'border-gray-200'
                      }`}
                      onClick={() => handleBannerSelect(banner.id)}>
                      <div className="relative">
                        <img
                          src={banner.image}
                          alt={banner.title}
                          className="w-[64px] h-[64px] rounded-2xl object-cover"
                        />
                         {selectedBanner === banner.id && (
                        <div className="absolute bottom-1 right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                          <TickCircle size={12} color="white" />
                        </div>
                      )}
                      </div>
                     
                      <div className="text-[10px] mt-3">{banner.title}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          <div className="py-6 flex flex-col gap-6 border-y-[1px] border-[#DEE2E6]">
            <div className="flex items-center justify-between">
              <h3 className="font-sora text-[16px] font-semibold text-[#2a2a2a]">
                Social Media Links
              </h3>
              <div onClick={showLinks} className="cursor-pointer">
                {!webLinks ? <ArrowDown2 /> : <ArrowUp2 />}
              </div>
            </div>
            {webLinks && (
              <div className=" space-y-6">
                <div className="space-y-2">
                  <label className="text-sm  text-[#5B5B5B]">Facebook</label>
                  <div className="flex gap-3">
                    <div className="w-12 h-12 bg-white border border-[#CCCCCC] rounded-xl flex items-center justify-center">
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none">
                        <path
                          d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                          fill="#1877F2"
                        />
                      </svg>
                    </div>
                    <input
                      type="text"
                      value={socialLinks.facebook}
                      onChange={(e) =>
                        handleSocialLinkChange('facebook', e.target.value)
                      }
                      className="flex-1 px-4 py-3 border border-[#CCCCCC] rounded-xl text-sm text-[#919191] focus:outline-none "
                      placeholder="Link to facebook profile"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm  text-[#5B5B5B]">Instagram</label>
                  <div className="flex gap-3">
                    <div className="w-12 h-12 bg-white border border-[#CCCCCC] rounded-xl flex items-center justify-center">
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none">
                        <path
                          d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"
                          fill="url(#instagram-gradient)"
                        />
                        <defs>
                          <linearGradient
                            id="instagram-gradient"
                            x1="0%"
                            y1="0%"
                            x2="100%"
                            y2="100%">
                            <stop offset="0%" stopColor="#833ab4" />
                            <stop offset="50%" stopColor="#fd1d1d" />
                            <stop offset="100%" stopColor="#fcb045" />
                          </linearGradient>
                        </defs>
                      </svg>
                    </div>
                    <input
                      type="text"
                      value={socialLinks.instagram}
                      onChange={(e) =>
                        handleSocialLinkChange('instagram', e.target.value)
                      }
                      className="flex-1 px-4 py-3 border border-[#CCCCCC] rounded-xl text-sm text-[#919191] focus:outline-none "
                      placeholder="Link to Instagram profile"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm  text-[#5B5B5B]">X (Twitter)</label>
                  <div className="flex gap-3">
                    <div className="w-12 h-12 bg-white border border-[#CCCCCC] rounded-xl flex items-center justify-center">
                      <svg
                        width="18"
                        height="18"
                        viewBox="0 0 18 18"
                        fill="none">
                        <path
                          d="M14.095 1.5H16.764L11.072 8.088L17.685 16.5H12.455L8.447 11.431L3.846 16.5H1.176L7.291 9.416L1 1.5H6.362L9.948 6.069L14.095 1.5ZM13.174 14.94H14.606L5.585 2.973H4.064L13.174 14.94Z"
                          fill="#000000"
                        />
                      </svg>
                    </div>
                    <input
                      type="text"
                      value={socialLinks.twitter}
                      onChange={(e) =>
                        handleSocialLinkChange('twitter', e.target.value)
                      }
                      className="flex-1 px-4 py-3 border border-[#CCCCCC] rounded-xl text-sm text-[#919191] focus:outline-none "
                      placeholder="Link to X profile"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm  text-[#5B5B5B]">TikTok</label>
                  <div className="flex gap-3">
                    <div className="w-12 h-12 bg-white border border-[#CCCCCC] rounded-xl flex items-center justify-center">
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none">
                        <path
                          d="M16.8 5.7c-1.2-.8-2-2.1-2.1-3.6H12v11.4c0 1.7-1.4 3.1-3.1 3.1s-3.1-1.4-3.1-3.1 1.4-3.1 3.1-3.1c.3 0 .6.1.9.2V7.9c-.3 0-.6-.1-.9-.1-2.8 0-5.1 2.3-5.1 5.1s2.3 5.1 5.1 5.1 5.1-2.3 5.1-5.1V7.5c1.1.8 2.4 1.3 3.8 1.3V6.1c-.7 0-1.3-.2-1.9-.4z"
                          fill="#000000"
                        />
                      </svg>
                    </div>
                    <input
                      type="text"
                      value={socialLinks.tiktok}
                      onChange={(e) =>
                        handleSocialLinkChange('tiktok', e.target.value)
                      }
                      className="flex-1 px-4 py-3 border border-[#CCCCCC] rounded-xl text-sm text-[#919191] focus:outline-none "
                      placeholder="Link to TikTok profile"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Bottom Buttons */}
        <div className="flex flex-col md:flex-row w-full gap-2.5">
          <button className="bg-transparent order-2 md:order-1 text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 py-4 rounded-2xl cursor-pointer flex-1/2">
            Back
          </button>
          <button
            type="button"
            className="w-full text-[#fcfcfc] order-1 md:order-2 bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl cursor-pointer flex-2">
            Save
          </button>
        </div>
      </div>

      {/* PREVIEW PAGE */}
      <div className="md:w-[830px] py-5">
        {selectedTemplate === 0 ? (
          <DefaultTemplate
            user={user}
            business={business}
            primaryColor={primaryColor}
            bg={
              selectedBanner
                ? bannerOptions.find((b) => b.id === selectedBanner)?.image
                : bg
            }
            headline={headline}
            subHeadline={subHeadline}
          />
        ) : (
          <SecoundTemplate
            user={user}
            business={business}
            primaryColor={primaryColor}
            headline={headline}
            subHeadline={subHeadline}
          />
        )}
      </div>
    </div>
  );
};

export default WebSettings;
