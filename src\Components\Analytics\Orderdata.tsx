/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ArrowD<PERSON>,
  ArrowRight,
  ArrowUp,
  BagCross,
  Box,
  Flash,
  Profile2User,
} from 'iconsax-react';
import DateHead from './DateHead';
import Progressbar from '../../Constants/Progressbar';
import AnalyticButton from '../../Constants/AnalyticButton';
import AnalyticCard from '../../Constants/AnalyticCard';
import { useEffect, useState } from 'react';
import RecurringOrderModal from './RecurringOrderModal';

const Orderdata = ({
  data,
  isError,
  isLoading,
  formattedEndDate,
  formattedStartDate,
}: any) => {
  const [reocurringModal, setReocurringModal] = useState<boolean>(false);
  const handleReocurringModal = () => {
    setReocurringModal((prev) => !prev);
  };
  useEffect(() => {
    document.title = 'Order Data - Analytics';
  });
  return (
    <div>
      <DateHead />
      {isError ? (
        <h2 className="flex justify-center items-center py-16">
          An Error Occurred, Please Refresh
        </h2>
      ) : (
        <>
          {' '}
          <div className="flex flex-col lg:flex-row gap-6">
            <div className="w-full max-w-[660px]">
              <div className="border rounded-xl py-4 px-6">
                <div className="flex gap-4 items-center">
                  {isLoading ? (
                    <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                  ) : (
                    <div className="bg-[#E8F2FB] p-1.5 rounded-full">
                      <Box size="16" color="#1976D2" variant="Bold" />
                    </div>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                  ) : (
                    <p className="font-semibold text-lg text-[#2A2A2A]">
                      Total Orders{' '}
                    </p>
                  )}
                </div>
                <div className="flex my-6 justify-between items-center">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[120px]"></div>
                  ) : (
                    <p className="font-semibold text-[#1E1B39] text-2xl">
                      {data?.totalOrders}{' '}
                    </p>
                  )}
                  {isLoading ? (
                    <div className="animate-pulse bg-slate-200 rounded-3xl w-[40px] "></div>
                  ) : (
                    <p
                      className={`flex items-center rounded-3xl  text-[10px] p-1 ${
                        data?.ordersPercentageChange <= 0
                          ? 'border-[#DC3545] border'
                          : 'border-[#AEEBBC] border'
                      } `}>
                      <span
                        className={`${
                          data?.ordersPercentageChange <= 0
                            ? 'text-[#DC3545]'
                            : 'text-[#28A745]'
                        }`}>
                        {data?.ordersPercentageChange || 0} %
                      </span>
                      {data?.ordersPercentageChange <= 0 ? (
                        <ArrowDown className="text-[#DC3545]" size="12" />
                      ) : (
                        <ArrowUp className="text-[#28A745]" size="12" />
                      )}
                    </p>
                  )}
                </div>
                <div>
                  {data?.ordersByStatusData?.map((item: any) => (
                    <Progressbar
                      key={item.label} // Use a unique property like 'label' as the key
                      itemName={item.label}
                      level={item.value}
                      units="orders"
                      progressPercentage={item.percentage}
                      barColor={
                        item.label === 'Returned'
                          ? '#FFBF60'
                          : item.label === 'Shipped'
                          ? '#9A99E9'
                          : '#CDCCF4'
                      }
                      isLoading={isLoading}
                    />
                  ))}
                </div>

                {/* <Progressbar
              itemName="Completed Orders"
              level={3000}
              units="orders"
              progressPercentage={100}
              barColor="#AEEBBC"
            /> */}
                {/* <Progressbar
              itemName={data?.ordersByStatusData[0]?.label}
              level={2}
              units="orders"
              progressPercentage={data?.ordersByStatusData[0]?.value}
              barColor="#FFBF60"
            /> */}
                {/* <Progressbar
              itemName="Cancelled Orders "
              level={1000}
              units="orders"
              progressPercentage={50}
              barColor="#E87883"
            /> */}
              </div>
            </div>
            <div className="w-full">
              <div className="border rounded-xl py-4 px-6">
                <div className="flex gap-4 items-center">
                  {isLoading ? (
                    <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                  ) : (
                    <div className="bg-[#E8F2FB] p-1.5 rounded-full">
                      <Profile2User size="16" color="#1976D2" variant="Bold" />
                    </div>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                  ) : (
                    <p className="font-semibold text-lg text-[#2A2A2A]">
                      Recurring Order{' '}
                    </p>
                  )}
                </div>
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] mt-3"></div>
                ) : (
                  <p className="font-normal text-sm text-[#7B7B7B] mt-6">
                    {data?.recurringOrder}
                  </p>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[36px] max-w-[100px] mt-4"></div>
                ) : (
                  <AnalyticButton
                    title="View More"
                    image={ArrowRight}
                    onClick={handleReocurringModal}
                  />
                )}
              </div>
              <AnalyticCard
                title="Return Rate"
                churnRate={data?.returnedRate?.returnedRate}
                percentageChange={data?.returnedRate?.percentageChange || 0}
                description={`Your percentage of orders that were returned from  ${formattedStartDate} to ${formattedEndDate}`}
                hasMaxWidth={false}
                icon={<BagCross size="16" color="#DC3545" variant="Bold" />}
                backgroundColor="#FAF2F2"
                isLoading={isLoading}
                isPositive={data?.returnedRate?.percentageChange < 0}
              />
            </div>
          </div>
          <div className="flex flex-col lg:flex-row mt-6 lg:mt-0 justify-center items-center gap-6">
            <div className="border rounded-xl py-4 px-6 max-w-[448px] w-full ">
              <div className="flex gap-4 items-center">
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                ) : (
                  <div className="bg-[#E8F2FB] p-1.5 rounded-full">
                    <Profile2User size="16" color="#1976D2" variant="Bold" />
                  </div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                ) : (
                  <p className="font-semibold text-lg text-[#2A2A2A]">
                    Order Source
                  </p>
                )}
              </div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-full mt-6"></div>
              ) : (
                <p className="font-normal text-sm text-[#7B7B7B] mt-6">
                  {data?.orderSource}
                </p>
              )}
            </div>
            <>
              <AnalyticCard
                title="Average Fulfillment Time"
                churnRate={data?.averageFulfillmentTime?.averageTime || 0}
                percentageChange={
                  data?.averageFulfillmentTime?.percentageChange || 0
                }
                description={`Your average fulfilment time of your customers orders from ${formattedStartDate} to ${formattedEndDate}`}
                hasMaxWidth={true}
                icon={<Flash size="16" color="#1976D2" variant="Bold" />}
                backgroundColor="#E8F2FB"
                isLoading={isLoading}
                isPositive={data?.averageFulfillmentTime?.percentageChange < 0}
              />
            </>
          </div>
        </>
      )}

      {reocurringModal && (
        <RecurringOrderModal
          isOpen={reocurringModal}
          data={data}
          onClose={handleReocurringModal}
        />
      )}
    </div>
  );
};
export default Orderdata;
