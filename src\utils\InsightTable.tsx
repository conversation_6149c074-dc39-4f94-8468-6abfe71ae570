/* eslint-disable @typescript-eslint/no-explicit-any */
interface HeaderType {
  title: string;
  key?: string;
  align?: string;
  render?: (row: any) => React.ReactNode;
}

interface Props {
  rows?: any[];
  headers?: HeaderType[];
  showHead?: boolean;
}

export const InsightTable: React.FC<Props> = ({ headers, rows = [] }) => {
  return (
    <div className="overflow-x-auto rounded-3xl border border-[#CCCCCC]">
      <table className="w-full ">
        <thead>
          <tr className=" ">
            {headers?.map((header, index) => (
              <th
                key={index}
                className={`bg-[#FAFAFA] font-inter p-4 truncate text-xs font-normal text-[#919191] text-left border-b border-[#DEDEDE] ${
                  header.key ? 'cursor-pointer' : ''
                }`}>
                <div className="flex  items-center">
                  <span>{header.title}</span>
                </div>
              </th>
            ))}
          </tr>
        </thead>

        <tbody>
          {rows.map((row: any, rowIndex: number) => (
            <tr key={rowIndex} className=" bg-white cursor-pointer ">
              {headers?.map((header, headerIndex) => {
                const cellContent = header?.render
                  ? header.render(row)
                  : row[header.key || ''];
                const cellClassNames = `p-4 border-b  border-[#DEDEDE] ${
                  header?.align === 'center' ? 'text-center' : ''
                }`;

                return (
                  <td key={headerIndex} className={cellClassNames}>
                    {cellContent}
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
