/* eslint-disable @typescript-eslint/no-explicit-any */
import { Edit, Save2 } from "iconsax-react";
import SettingsInput from "../../Constants/SettingsInput";
import { useUserAuthStore } from "../../store/auth";
import { useState } from "react";
import { useMutation } from "react-query";
import { orderServices } from "../../utils/Services/Settings";
import { toast } from "react-toastify";

interface profileSettings {
  business_name: string;
  business_image: string | File;
  business_phone: string;
  business_type: string;
  business_address: string;
  business_state: string;
  business_zip_code: string;
  business_website: string;
  business_city: string;
  social_facebook:string;
  social_instagram:string;
  social_tiktok:string;
  social_twitter:string;
  vendorId: string;
  countryCode: string;
}
const BusinessProfile = () => {
  const business = useUserAuthStore((state) => state.business);
  const user = useUserAuthStore((state) => state.user);
  const updateBusiness = useUserAuthStore((state) => state.updateBusiness);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  const extractPhoneNumber = (phone: string) => {
    const countryCode = phone.slice(0, phone.length - 10);
    const localNumber = phone.slice(-10);
    return { countryCode, localNumber };
  };

  const { countryCode, localNumber } = extractPhoneNumber(
    business.business_phone
  );
  const [profile, setProfile] = useState<profileSettings>({
    vendorId: user.vendorId,
    business_name: business.business_name,
    business_image: business.business_image,
    business_type: business.business_type,
    business_phone: localNumber,
    business_website: business.business_website,
    business_address: business.business_address,
    business_city: business.business_city,
    business_state: business.business_state,
    business_zip_code: business.business_zip_code,
    countryCode: countryCode,
    social_facebook:business.social_facebook,
    social_instagram:business.social_instagram,
    social_tiktok:business.social_tiktok,
    social_twitter:business.social_twitter
  });
  const mutation = useMutation((formData: FormData) =>
    orderServices.editUserProfile(formData)
  );

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setProfile((prevDetails) => ({
        ...prevDetails,
        business_image: file,
      }));

      const imageUrl = URL.createObjectURL(file);
      setPreviewImage(imageUrl);
    }
  };
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setProfile((prevDetails) => ({
      ...prevDetails,
      [name]: value,
    }));
  };
  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;

    setProfile((prevDetails) => ({
      ...prevDetails,
      [name]: value,
    }));
  };
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const fullPhoneNumber = `${profile.countryCode}${profile.business_phone}`;

    const formData = new FormData();
    formData.append("vendorId", profile.vendorId);
    const businessData = {
      business_name: profile.business_name,
      business_type: profile.business_type,
      business_phone: fullPhoneNumber,
      business_website: profile.business_website,
      business_address: profile.business_address,
      business_city: profile.business_city,
      business_state: profile.business_state,
      business_zip_code: profile.business_zip_code,
      social_facebook:profile.social_facebook,
      social_instagram:profile.social_instagram,
      social_twitter:profile.social_twitter,
      social_tiktok:profile.social_tiktok
    };
    formData.append("businessData", JSON.stringify(businessData));
    formData.append("businessImage", profile.business_image);
    if (profile.business_phone.length != 10) {
      toast.error("Incomplete Phone Number");
    }
    mutation.mutate(formData, {
      onSuccess: (data) => {
        updateBusiness(data?.data?.business);
        toast.success(data?.data?.message);
      },
      onError: (error: any) => {
        const errorMessage =
          error?.response?.data?.error || "Something went wrong";
        toast.error(errorMessage);
      },
    });
  };

  return (
    <div className="mt-6 flex items-center justify-center w-full">
      <form
        onSubmit={handleSubmit}
        style={{ boxShadow: "0px 5px 50px 0px #1A1A1A14" }}
        className="max-w-[540px] w-full p-4  md:p-6 rounded-2xl"
      >
        <div className="flex justify-between  bg-[#4F4CD8] p-4 md:p-6 rounded-2xl mb-6">
          <div className="flex items-center gap-2">
            {/* <div className="w-[50px] h-[50px] rounded-[50%] border border-white flex items-center">
              {business.business_image ? (
                <img
                  src={business.business_image}
                  alt="profile"
                  className="w-[50px] h-[50px] md:block"
                />
              ) : (
                <img
                  src={profil}
                  alt="profile"
                  className="w-[50px] h-[50px] md:block"
                />
              )}
            </div> */}
            <div
              className="flex w-[50px] h-[50px]   cursor-pointer    items-center justify-center relative"
              id="add-photo"
            >
              <label
                htmlFor="business-image"
                className="flex flex-col justify-center items-center gap-2"
              >
                {previewImage || profile.business_image ? (
                  <img
                    src={
                      previewImage ||
                      (typeof profile.business_image === "string"
                        ? profile.business_image
                        : "")
                    }
                    alt="Selected Business"
                    className="w-[50px] object-cover h-[50px] md:block rounded-[50%] border border-white"
                  />
                ) : (
                  <>
                    <img
                      src={profile.business_image}
                      alt="profile"
                      className="w-[50px] object-cover h-[50px] md:block"
                    />
                  </>
                )}
              </label>
              <input
                type="file"
                id="business-image"
                name="businessImage"
                accept="image/*"
                className="hidden"
                onChange={handleFileChange}
              />
            </div>
            <div>
              <h2 className="font-semibold text-sm md:text-lg text-[#FCFCFC]">
                {business.business_name}
              </h2>
              <p className="font-normal text-[#F5F5F5] text-xs md:text-sm">
                {business.business_phone}
              </p>
            </div>
          </div>
          <button
            type="button"
            className="flex items-center text-[#FCFCFC] gap-1 border border-[#DCDCDC] h-fit px-4 py-3 rounded-3xl"
          >
            <span className="hidden md:block">Edit Profile</span>
            <Edit size="16" />
          </button>
        </div>
        <SettingsInput
          placeholder="Business Name"
          label="Business Name"
          name="business_name"
          value={profile.business_name}
          onChange={handleChange}
        />

        <div className=" flex flex-col gap-2.5 mb-6">
          <p className="text-[#5B5B5B] font-sora text-sm">Business Type</p>
          <select
            name="business_type"
            value={profile.business_type}
            onChange={handleSelectChange}
            className="text-[#7b7b7b] text-sm bg-[#FCFCFC] font-sora font-normal h-[48px] px-4 border border-[#cccccc] rounded-2xl outline-none"
          >
            <option>Select Your Business Type</option>
            <option value="Fashion and Apparel">Fashion and Apparel</option>
            <option value="Electronics and Gadgets">
              Electronics and Gadgets
            </option>
            <option value="Home and Living">Home and Living</option>
            <option value="Beauty and Personal Care">
              Beauty and Personal Care
            </option>
            <option value="Food and Beverage">Food and Beverage</option>
            <option value="Other">Other</option>
          </select>
        </div>
        <div className="mb-6">
          <p className="text-[#5B5B5B] font-sora text-sm">
            Business Phone Number
          </p>
          <div className="flex items-center mt-2 w-full">
            <select
              name="countryCode"
              value={profile.countryCode}
              onChange={handleSelectChange}
              className="mr-2.5 w-[28%] bg-[#FCFCFC] text-xs text-[#7b7b7b] h-[48px] px-3.5 border border-[#cccccc] rounded-2xl outline-none"
            >
              <option value="+234">🇳🇬 +234</option>
              <option value="+1">🇺🇸 +1</option>
            </select>
            <input
              className="text-xs w-[70%] bg-[#FCFCFC] text-[#7b7b7b] h-[48px] px-3.5 border border-[#cccccc] rounded-2xl outline-none "
              type="text"
              placeholder="************"
              name="business_phone"
              value={profile.business_phone}
              onChange={handleChange}
            />
          </div>
        </div>
        <SettingsInput
          label="Business Website"
          placeholder="Enter your business website here"
          name="business_website"
          value={profile.business_website}
          onChange={handleChange}
        />
        <SettingsInput
          label="Instagram"
          placeholder="Enter your Instagram handle"
          name='social_instagram'
          value={profile.social_instagram}
          onChange={handleChange}
        />

         <SettingsInput
          label="TikTok"
          placeholder="Enter your TikTok handle"
          name='social_tiktok'
          value={profile.social_tiktok}
          onChange={handleChange}
        />

         <SettingsInput
          label="Twitter (X)"
          placeholder="Enter your Twitter (X) handle"
          name='social_twitter'
          value={profile.social_twitter}
          onChange={handleChange}
        />

         <SettingsInput
          label="Facebook"
          placeholder="Enter your Facebook handle"
          name='social_facebook'
          value={profile.social_facebook}
          onChange={handleChange}
        />
        <SettingsInput
          label="Business Address"
          placeholder="<EMAIL>"
          name="business_address"
          value={profile.business_address}
          onChange={handleChange}
        />

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={mutation.isLoading}
            className={` flex gap-[31px] px-4 h-[41px] rounded-2xl items-center font-semibold text-[#FCFCFC] text-sm ${
              mutation.isLoading
                ? "bg-[#4F4CD8]  opacity-50 cursor-not-allowed "
                : " bg-[#4F4CD8] "
            }`}
          >
            <span>{mutation.isLoading ? "saving . . ." : "Save Changes"}</span>
            <Save2 size="16" />
          </button>
        </div>
      </form>
    </div>
  );
};
export default BusinessProfile;
