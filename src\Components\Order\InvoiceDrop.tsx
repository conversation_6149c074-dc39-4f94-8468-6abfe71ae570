/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef } from 'react';

interface DropdownOption {
  label: string;
  onClick: () => void;
  disabled?: boolean;
}

interface ModalProps {
  onClose: () => void;
  options: DropdownOption[];
  positionStyles?: string; // Allows customizing position dynamically
}

const InvoiceDrop: React.FC<ModalProps> = ({
  onClose,
  options,
  positionStyles,
}) => {
  const modalRef = useRef<HTMLDivElement | null>(null);

  const handleOverlayClick = (event: any) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      onClose();
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleOverlayClick);
    return () => {
      document.removeEventListener('mousedown', handleOverlayClick);
    };
  }, []);

  return (
    <div
      ref={modalRef}
      className={`absolute bg-white rounded-2xl font-sora border border-gray-300 shadow-lg z-10 w-[190px] ${
        positionStyles || 'right-0 mb-44'
      }`}>
      <div className="cursor-pointer">
        {options.map((option, index) => (
          <p
            key={index}
            onClick={!option.disabled ? option.onClick : undefined}
            className={`py-4 px-4 text-[#5B5B5B] font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE] ${
              index === 0 ? 'hover:rounded-t-2xl border-b' : ''
            } ${index === options.length - 1 ? 'hover:rounded-b-2xl' : ''}`}>
            {option.label}
          </p>
        ))}
      </div>
    </div>
  );
};

export default InvoiceDrop;
