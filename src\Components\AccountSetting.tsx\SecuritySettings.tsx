import { useState } from 'react';
import progress from '../../assets/progress.svg';
import SettingsInput from '../../Constants/SettingsInput';
const SecuritySettings = () => {
  const [isToggled, setIsToggled] = useState<boolean>(false);
  const handleToggle = () => {
    setIsToggled(!isToggled);
  };
  return (
    <div className="mt-6 flex items-center justify-center w-full">
      <div
        style={{ boxShadow: '0px 5px 50px 0px #1A1A1A14' }}
        className="max-w-[540px] w-full  p-6 rounded-2xl">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h4 className="text-[#2A2A2A] text-lg font-semibold">
              Two-Factor Authentication
            </h4>
            <p className="text-[#5B5B5B] text-xs md:text-sm font-normal max-w-[242px] md:max-w-[350px]">
              Setting up a two factor Authentication helps keep your account
              safe from attackers
            </p>
          </div>
          <img
            src={progress}
            alt="prog_bar"
            className="lg:w-20 lg:h-20 h-[60px] w-[60px]"
          />
        </div>
        <SettingsInput
          placeholder="1234- 3456 - 234"
          label="Enter the code sent  to your account  "
        />
        <div className="flex justify-between">
          <button
            type="button"
            className="text-[#9B9B9B] font-semibold text-sm   py-3 max-w-[150px] w-full ">
            Skip
          </button>
          <button
            type="button"
            className="text-[#FCFCFC] font-semibold text-sm bg-[#4F4CD8] rounded-2xl py-3 max-w-[150px] w-full">
            Next
          </button>
        </div>
        <div className="flex items-center mt-6 gap-2">
          <div
            className={`w-10 h-5 flex items-center rounded-full p-1 cursor-pointer ${
              isToggled ? 'bg-[#4F4CD8]' : 'bg-gray-300'
            }`}
            onClick={handleToggle}>
            <div
              className={`bg-white w-4 h-4 rounded-full shadow-md transform ${
                isToggled ? 'translate-x-5' : 'translate-x-0'
              } transition`}></div>
          </div>
          <p className="text-[#5B5B5B] font-normal text-xs md:text-sm">
            Disable Two-Factor Authentication
          </p>
        </div>
      </div>
    </div>
  );
};
export default SecuritySettings;
