import { useEffect } from 'react';
import DashboardLayout from '../Components/DashboardLayout';
import { useNavigate } from 'react-router-dom';
import { RouteNames } from '../utils/RouteNames';
import ScrollToTop from '../utils/ScrollToTop';
import { useUserAuthStore } from '../store/auth';

const Dashboard = () => {
  const business = useUserAuthStore((state)=>state.business)
  const token = (() => {
    const userAuthStore = localStorage.getItem('user-auth-store');
    if (userAuthStore) {
      const parsedAuthStore = JSON.parse(userAuthStore);
      return parsedAuthStore?.state.token;
    }
    return null;
  })();
  const navigate = useNavigate();

  useEffect(() => {
    if (!token) {
      navigate(RouteNames.login);
    } else if (!business || Object.keys(business).length === 0) {
      navigate(RouteNames.business); 
    }
  }, [token, navigate, business]);

  if (!token) {
    return null;
  }

  return (
    <>
      <ScrollToTop />
      <DashboardLayout />
    </>
  );
};

export default Dashboard;
