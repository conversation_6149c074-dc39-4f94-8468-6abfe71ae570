const CardLoader = () => {
  return (
    <div className="border-[1px] flex-1 min-w-[256px] border-primary-neutral200 rounded-[16px] bg-primary-baseWhite px-[24px] py-[16px]">
      <div className="animate-pulse flex flex-col space-y-4 ">
        <div className="flex items-center gap-3">
          <div className="rounded-full bg-slate-200 h-7 w-7"></div>
          <div className="flex-col space-y-4 flex">
            <div className="rounded-2xl bg-slate-200 h-[10px] w-[200px]"></div>
          </div>
        </div>
        <div className="rounded-2xl bg-slate-200 h-[10px] w-full"></div>
      </div>
    </div>
  );
};

export default CardLoader;
