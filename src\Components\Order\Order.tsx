/* eslint-disable @typescript-eslint/no-explicit-any */

import {
  ArrowR<PERSON>,
  BagCross,
  BagTick2,
  BagTimer,
  Box,
  DocumentDownload,
  Profile,
  ProfileAdd,
  ShoppingBag,
} from 'iconsax-react';
import honeyComb from '../../assets/honeyComb.png';
import TableOrder from './OrderTable';
import { useState, useEffect, useRef } from 'react';
import CreateOrder from './CreateOrder';
import { Link } from 'react-router-dom';
import { RouteNames } from '../../utils/RouteNames';
import AddCustomer from './AddCustomer';
import { useQuery } from 'react-query';
import { orderServices } from './../../utils/Services/Order';
import { StatsService } from '../../utils/Services/StatsServices';
import { useUserAuthStore } from '../../store/auth';
import { useDateStore } from '../../store/date';
import { toast } from 'react-toastify';
import GenerateReport from '../Inventory/GenerateReport';
import { FormatPrice } from '../../utils/FormatPrice';
import CardLoader from '../../Constants/CardLoader';
import DeliveryCost from './DeliveryCost';
interface Payload {
  startDate?: string;
  endDate?: string;
}

const Order: React.FC<Payload> = () => {
  const [createOrderPanel, setCreateOrderPanel] = useState<boolean>(false);
  const [openDelivery, setOpenDelivery] = useState<boolean>(false);
  const [generateReportModal, setGenerateReportModal] =
    useState<boolean>(false);
  const [addCustomer, setAddCustomer] = useState<boolean>(false);
  const user = useUserAuthStore((state) => state.user);
  const { startDates, endDates } = useDateStore();
  const formattedStartDate = startDates
    ? new Date(startDates).toISOString().split('T')[0]
    : undefined;
  const formattedEndDate = endDates
    ? new Date(endDates).toISOString().split('T')[0]
    : undefined;

       const originalThemeColor = useRef<string | null>(null);
     useEffect(()=>{
    const meta = document.querySelector<HTMLMetaElement>(
          'meta[name="theme-color"]'
        );
        if (!meta) return;
    
        // on first ever run, capture the original content
        if (originalThemeColor.current === null) {
          originalThemeColor.current = meta.content;
        }
    
        // toggle between white (modal open) and the saved original
        meta.content = generateReportModal || createOrderPanel || openDelivery || addCustomer ? "#FFFFFF" : originalThemeColor.current;
     },[generateReportModal,createOrderPanel,openDelivery,addCustomer])

  const handleReportModal = () => {
    setGenerateReportModal((prev) => !prev);
  };

  const handleAddCustomer = () => {
    setAddCustomer((prev) => !prev);
  };

  const handleCreateOrder = () => {
    setCreateOrderPanel((prev) => !prev);
  };
  const handleDelivery = () => {
    setOpenDelivery((prev) => !prev);
  };

  useEffect(() => {
    document.title = 'Orders';
  }, []);

  const { data, isError, isLoading, refetch } = useQuery(
    ['orderTable', user.vendorId],
    () => orderServices.allOrders(user.vendorId),
    {
      enabled: !!user.vendorId,
    }
  );

  const customerList = data?.customers;
  const productList = data?.products;

  const {
    isLoading: statsLoading,
    refetch: statsRefetch,
    isError: statsError,
    data: statsData,
  } = useQuery(
    ['orderStats', user.vendorId, formattedStartDate, formattedEndDate],
    () =>
      StatsService.OverviewStats({
        vendorId: user.vendorId,
        startDate: formattedStartDate,
        endDate: formattedEndDate,
      }),
    {
      enabled: !!user.vendorId && !!formattedStartDate && !!formattedEndDate,
      onSuccess: (data) => {
        console.log('Overview Stats Response:', data);
      },
      onError: (error: any) => {
        toast.error('Failed to load stats');
        console.error('Error fetching stats:', error);
      },
    }
  );

  useEffect(() => {
    if (!isLoading && !isError && data) {
      console.log(data);
    }
  }, [
    data,
    isLoading,
    isError,
    refetch,
    statsData,
    statsLoading,
    statsRefetch,
  ]);

  const handleRefetch = async () => {
    try {
      await refetch(); // Wait for inventory refetch to complete
      await statsRefetch(); // Then refetch stats data
    } catch (error) {
      console.error('Error during refetch:', error);
    }
  };

  const stats = statsData?.data;
  const productStats = [
    {
      id: 1,
      title: 'Total Available Products',
      value: stats?.availableProducts,
      iconColor: '#4F4CD8',
      bgColor: '#E6E5F9',
      icon: Box,
    },
    {
      id: 2,
      title: 'Completed Orders (Sold)',
      value: stats?.completedOrders,
      iconColor: '#28a745',
      bgColor: '#e0f3e5',
      icon: BagTick2,
    },
    {
      id: 3,
      title: 'Pending Orders',
      value: stats?.pendingOrders,
      iconColor: '#ffc107',
      bgColor: '#fff3cd',
      icon: BagTimer,
    },
    {
      id: 4,
      title: 'Cancelled/Returned',
      value: stats?.cancelledOrders,
      iconColor: '#dc3545',
      bgColor: '#f8d7da',
      icon: BagCross,
    },
  ];

  return (
    <div className="p-4 md:p-0 flex flex-col gap-[20px]">
      <div className="flex flex-col md:flex-row justify-between gap-5 my- font-sora ">
        <div className="md:w-[308px] w-full">
          <h1 className="text-2xl font-bold m-0">Order Mangement</h1>
          <p className="text-base text-[#7e8494]">
            Manage all your products sales here
          </p>
        </div>
        <div className="flex flex-col gap-3 w-full md:w-[368px] md:justify-between md:flex-row items-center">
          <div className="w-full">
            <button
              onClick={handleCreateOrder}
              className="flex-1 w-full h-[50px] hover:bg-[#6866DE] duration-300 border border-gray-300 gap-[4px] rounded-2xl px-3 py-2 text-[13px] md:text-[14px] flex items-center cursor-pointer bg-[#4f4cd8] text-primary-baseWhite font-semibold justify-center">
              <ShoppingBag size={16} /> Create New Order
            </button>
          </div>
          <div className="w-full">
            <button
              onClick={handleReportModal}
              className=" flex-1 w-full h-[50px] border gap-[4px] text-primary-neutralt1 border-gray-300 rounded-2xl px-3 py-2 text-[13px] md:text-[14px] flex items-center cursor-pointer bg-white font-bold justify-center">
              <DocumentDownload size="16" color="#1a1a1a" />
              Order Reports
            </button>
          </div>
        </div>
      </div>

      <div className="my-8  flex lg:justify-between overflow-x-scroll scrollbar-hide md:overflow-hidden md:flex-wrap gap-2 lg:gap-y-0 lg:flex-nowrap">
        {statsError ? (
          <div className="text-center w-full py-20">
            Sorry, An error occured fetching the cards. Kindly refresh the page
          </div>
        ) : (
          <>
            {productStats?.map((stat) => (
              <div key={stat.id} className=" flex-1 min-w-[256px]  ">
                {statsLoading ? (
                  <CardLoader />
                ) : (
                  <div className="border-[1px] border-primary-neutral200 rounded-[16px] bg-primary-baseWhite p-6 relative">
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex items-center gap-1">
                        <div
                          className={`p-1 rounded-lg`}
                          style={{ backgroundColor: stat.bgColor }}>
                          {/* <Box size="15" color={stat.iconColor} variant="Bold" /> */}
                          <stat.icon color={stat.iconColor} variant="Bold" />
                        </div>
                        <span className="text-sm font-normal">
                          {stat.title}
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-start items-center mt-5">
                      <span className="text-2xl font-bold">
                        {FormatPrice(stat.value)}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </>
        )}
      </div>
{/* delivery */}
      <div className='w-full mb-5 grid gap-2 px-6 py-4 bg-[#F5F5F5] rounded-xl '>
        <h3 className='font-sora font-semibold text-[24px] text-[#2A2A2A]'>Delivery</h3>
        <div className='flex flex-col gap-3 md:flex-row md:items-center justify-between'>
          <p className='text-[14px] text-[#5B5B5B] font-sora'>Set and manage your delivery</p>
          <button onClick={handleDelivery} className='flex border-primary-purple  p-2 border-[1px] rounded-2xl justify-between items-center gap-2'> Manage Delivery <ArrowRight size={16}  color='#5b5b5b'/></button>
          </div>

      </div>

      <div className="flex flex-col lg:flex-row justify-between gap-7 mb-8 flex-wrap">
        <div className="p-6 bg-gradient-to-r from-[#28A745] to-[#10411B] relative rounded-xl flex-1 w-full">
          <h4 className="text-primary-baseWhite text-2xl font-sora font-semibold leading-[28.8px]">
            Promo/Sales (Discount & Coupons)
          </h4>
          <p className="text-[#EAF7ED] font-sora text-sm leading-[22.4px] mt-4  w-full font-normal">
            To run sales, “clearance” or “black Friday”. Create irresistible
            offers to attract more customers and increase sales. Manage your
            promotions effortlessly to keep your business competitive and
            thriving.
          </p>
          <Link
            to={RouteNames.promo}
            state={{ data: { customerList, productList } }}
            className="bg-[#ff9900] w-full text-primary-baseWhite border-none px-3 py-3 rounded-2xl flex md:w-[180px] mt-4 text-sm font-semibold cursor-pointer justify-between items-center hover:bg-[#ee8f00]">
            Manage Promotions
            <ArrowRight size="16" color="#fff" />
          </Link>
          <img
            src={honeyComb}
            alt="honeycomb"
            className="absolute top-1 right-0"
          />
        </div>
        <div className="p-6 border border-[#4f4cd8] rounded-xl flex-1 w-full">
          <div className="flex justify-between items-center">
            <h4 className="text-2xl font-sora font-semibold text-primary-neutralt1">
              Customers
            </h4>
            <Profile size="22" color="#4F4CD8" variant="Bold" />{' '}
          </div>
          <p className="text-sm text-[#5b5b5b] max-w-[506px] font-normal w-full leading-[22.4px] mt-4">
            Easily access and organize your entire customer list. Add new
            customers, update their information, and keep track of interactions
            to better manage your relationships.
          </p>
          <div className="flex-col flex md:flex-row w-full items-center gap-4 md:gap-6 mt-2 md:mt-0">
            <Link
              to={RouteNames.customer}
              className="text-primary-baseWhite h-[50px] md:gap-2  hover:bg-[#6866DE] duration-300 border bg-[#4f4cd8] px-4 py-3 rounded-2xl flex w-full md:w-fit mt-0 md:mt-4 text-[14px] md:text-sm font-semibold cursor-pointer justify-between items-center">
              Manage Customers
              <ArrowRight size="16" color="#FCFCFC" />
            </Link>
            <button
              onClick={handleAddCustomer}
              className="text-primary-neutralt1 h-[50px] border border-[#4f4cd8] px-4 py-3 rounded-2xl flex w-full md:w-[180px] mt-0 md:mt-4 text-[14px] md:text-sm font-semibold cursor-pointer justify-between items-center">
              Add Customers
              <ProfileAdd size="16" />
            </button>
          </div>
        </div>
      </div>
      <div>
        <TableOrder
          data={data}
          isLoading={isLoading}
          isError={isError}
          refetch={handleRefetch}
        />
      </div>
      {createOrderPanel && (
        <CreateOrder
          closeModal={handleCreateOrder}
          isOpen={createOrderPanel}
          refetch={handleRefetch}
        />
      )}

      {generateReportModal && (
        <GenerateReport
          closeModal={handleReportModal}
          isOpen={generateReportModal}
          title="Order"
        />
      )}
      {addCustomer && (
        <AddCustomer closeModal={handleAddCustomer} isOpen={addCustomer} />
      )}
      {openDelivery && (
        <DeliveryCost
          closeModal={handleDelivery}
          isOpen={openDelivery}
        />)}
    </div>
  );
};
export default Order;
