/* Dropdown.tsx */
import { useState, useEffect, useRef } from 'react';

interface DropdownProps {
  options: string[];
  placeholder?: string;
  value?: string;
  onSelect: (value: string) => void;
}

export default function BankDropdown({
  options,
  placeholder = 'Select…',
  value,
  onSelect,
}: DropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const wrapperRef = useRef<HTMLDivElement>(null);

  // Close dropdown on outside click
  useEffect(() => {
    function handleClickOutside(e: MouseEvent) {
      if (wrapperRef.current && !wrapperRef.current.contains(e.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter options by search term
  const filtered = options.filter(opt =>
    opt.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="relative md:w-[492px]" ref={wrapperRef}>
      {/* Trigger button */}
      <button
        type="button"
        onClick={() => setIsOpen(open => !open)}
        className="w-full text-[#5B5B5B] font-sora text-[14px] bg-[#FCFCFC] h-[48px] text-left px-4 py-2 border-[1px] border-[#CCCCCC] rounded-2xl "
      >
        {value || placeholder}
      </button>

      {/* Dropdown panel */}
      {isOpen && (
        <div className="absolute mt-1 w-full bg-white border rounded-md shadow-lg z-50">
          {/* Search box */}
          <div className="px-2 py-1">
            <input
              type="text"
              autoFocus
              placeholder="Search bank..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border rounded-md font-sora text-[#5B5B5B] focus:outline-none"
            />
          </div>
          {/* Options list */}
          <ul className="max-h-56 overflow-auto">
            {filtered.length > 0 ? (
              filtered.map(opt => (
                <li
                  key={opt}
                  onClick={() => {
                    onSelect(opt);
                    setIsOpen(false);
                    setSearchTerm('');
                  }}
                  className="px-4 font-sora text-[#5B5B5B] text-[14px] py-2 hover:bg-gray-100 cursor-pointer"
                >
                  {opt}
                </li>
              ))
            ) : (
              <li className="px-4 py-2 text-gray-500">No matches</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
}
