import { ArrowLeft2, ArrowRight2 } from "iconsax-react";

/* eslint-disable @typescript-eslint/no-explicit-any */
interface Props {
  page?: any;
  setPage?: any;
  limit?: any;
  setlimit?: any;
  total?: any;
  pageNumbers?: any;
  disablePrevPage?: any;
  disableNextPage?: any;
  gotoPrevPage?: any;
  gotoNextPage?: any;
}

export const Pagination: React.FC<Props> = ({
  page,
  // setPage,
  pageNumbers,
  disablePrevPage,
  disableNextPage,
  gotoPrevPage,
  gotoNextPage,
}) => {
  return (
    <div>
      {pageNumbers?.length > 0 && (
        <>
          <div className="flex justify-between w-full md:justify-end items-center pt-5 pr-5 pb-5 ">
            <div id="itemInfo" className="mr-[15px]">
              <p className="font-normal text-sm font-sora text-[#5A5A5A]">
                Page {page} of {pageNumbers.length}
              </p>
            </div>
            <div className="pagination-controls flex gap-[10px]">
              <button
                type="button"
                disabled={disablePrevPage}
                onClick={gotoPrevPage}
                className="bg-white md:inline hidden text-[#5a5a5a] border border-[#dedede] !font-inter py-2 px-4 rounded-full cursor-pointer text-sm transition-all duration-300 outline-none hover:bg-[#f5f5f5]"
              >
                Previous
              </button>

              <button
                type="button"
                disabled={disablePrevPage}
                onClick={gotoPrevPage}
                className="bg-white md:hidden text-[#5a5a5a] border border-[#dedede] !font-inter py-2 px-4 rounded-full cursor-pointer text-sm transition-all duration-300 outline-none hover:bg-[#f5f5f5]"
              >
                <ArrowLeft2 />
              </button>
              <button
                type="button"
                disabled={disableNextPage}
                onClick={gotoNextPage}
                className="bg-white md:inline hidden text-[#5a5a5a] border border-[#dedede] !font-inter py-2 px-4 rounded-full cursor-pointer text-sm transition-all duration-300 outline-none hover:bg-[#f5f5f5]"
              >
                Next
              </button>

              <button
                type="button"
                disabled={disableNextPage}
                onClick={gotoNextPage}
                className="bg-white md:hidden text-[#5a5a5a] border border-[#dedede] !font-inter py-2 px-4 rounded-full cursor-pointer text-sm transition-all duration-300 outline-none hover:bg-[#f5f5f5]"
              >
                <ArrowRight2/>
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
