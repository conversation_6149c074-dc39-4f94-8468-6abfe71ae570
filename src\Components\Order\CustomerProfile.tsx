/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  CallCalling,
  ClipboardClose,
  MessageText1,
  ReceiptEdit,
  Whatsapp,
} from 'iconsax-react';
import { useEffect, useState } from 'react';
import { formatDate, FormatPrice } from '../../utils/FormatPrice';

interface CustomerDetailsProps {
  isOpen: boolean;
  closeModal: () => void;
  openEditCustomer: () => void;
  profileData: any;
  isLoading: boolean;
}
const CustomerProfile: React.FC<CustomerDetailsProps> = ({
  isOpen,
  closeModal,
  profileData,
  openEditCustomer,
  isLoading,
}) => {
      const [copyPhone, setCopyPhone] = useState<boolean>(false);

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('no-scroll');
    } else {
      document.body.classList.remove('no-scroll');
    }

    return () => {
      document.body.classList.remove('no-scroll');
    };
  }, [isOpen]);
  if (!isOpen) return null;
  

    const handlePhoneNumber = () => {
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      if (isMobile) {
        // Initiate phone call on mobile
        window.location.href = `tel:+234${profileData.customerPhone}`;
      } else {
        // Copy phone number on desktop
        navigator.clipboard.writeText(`+234${profileData.customerPhone}`).then(
          () => {
            setCopyPhone(true);
            setTimeout(() => setCopyPhone(false), 4000);
          },
          (err) => {
            console.error("Failed to copy phone number:", err);
          }
        );
      }
    };


  const Socials = [
    {
      icon: Whatsapp,
      bgColor: "bg-[#28A745]",
      borderColor: "border-[#28A745]",
      color: "#FCFCFC",
      onclick: () => {
        window.open(`https://wa.me/+234${profileData.customerPhone}`);
      },
    },
    {
      icon: CallCalling,
      color: "#FF9900",
      borderColor: " border-[#FF9900]",
      onclick:  handlePhoneNumber ,
    },
  ];

  return (
    <div className="fixed inset-0 z-50 font-sora bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white z-30 p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-0 md:my-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="overflow-y-auto h-screen flex flex-col gap-4 scrollbar-none hidescroll ">
            <div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
              ) : (
                <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                  Customer Profile
                </h2>
              )}
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px] mt-3"></div>
              ) : (
                <p className=" text-sm text-[#919191] font-sora ">
                  See and mange the details of your customer.
                </p>
              )}
            </div>

            <div className=" rounded-2xl flex flex-col gap-6 bg-[#F5F5F5] px-[16px] md:px-[24px] py-[16px]">
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[120px]"></div>
              ) : (
                <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                  Profile
                </h4>
              )}

              <div className=" flex flex-col gap-5 relative  ">
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Name
                    </p>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      {profileData?.customerName || "-"}
                    </p>
                  )}
                </div>
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Phone Number
                    </p>
                  )}

                  <div className=" flex items-end justify-between">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                    ) : (
                      <p
                        className={`text-[14px]  text-center rounded-2xl font-sora font-normal `}
                      >
                        {profileData?.customerPhone || "-"}
                      </p>
                    )}

                    <div className="flex gap-6 items-center">
                      {isLoading ? (
                        <div className="flex gap-6 items-center">
                          <div className="rounded-full animate-pulse bg-slate-200 w-[36px] h-[36px]"></div>
                          <div className="rounded-full animate-pulse bg-slate-200 w-[36px] h-[36px]"></div>
                        </div>
                      ) : (
                        <>
                          {" "}
                          {Socials.map((item, index) => (
                            <button
                              onClick={item.onclick}
                              key={index}
                              className="w-[36px] relative h-[36px]"
                            >
                              <item.icon
                                size={36}
                                color={item.color}
                                className={`${item.bgColor}   border-[1px] ${item.borderColor} p-2 rounded-2xl`}
                              />
                              {copyPhone && item.icon === CallCalling && (
                                <div className="absolute w-[150px] mt-[10px] left-1/20 transform -translate-x-1/2 text-[14px] text-primary-neutralt1 bg-white px-2 py-2 rounded shadow">
                                  Contact Copied
                                </div>
                              )}
                            </button>
                          ))}
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Email
                    </p>
                  )}

                  <div className="flex items-end justify-between">
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[170px]"></div>
                    ) : (
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        {profileData?.customerEmail || "-"}
                      </p>
                    )}
                    {isLoading ? (
                      <div className="rounded-full animate-pulse bg-slate-200 w-[36px] h-[36px]"></div>
                    ) : (
                      <div
                        onClick={() => {
                          window.location.href = `mailto:${profileData.customerEmail}`;
                        }}
                        className="cursor-pointer"
                      >
                        <MessageText1
                          size={36}
                          className="text-primary-purple border-primary-purple border p-2 rounded-2xl"
                        />
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Social Media
                    </p>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[170px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      {profileData?.socialMediaHandle || "-"}
                    </p>
                  )}
                </div>
                <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300 ">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[170px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Shopping Address
                    </p>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[300px]"></div>
                  ) : (
                    <div className="flex flex-col gap-2">
                      <p className="text-[14px] font-sora font-normal gap-2 text-[#5b5b5b]">
                        {profileData?.shippingAddresses?.[0]?.shippingAddress
                          ? `${profileData.shippingAddresses[0].shippingAddress}, ${profileData.shippingAddresses[0].shippingCity}`
                          : "-"}
                      </p>
                    </div>
                  )}
                </div>
                <div className="flex flex-col gap-3 pb-6 ">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[170px]"></div>
                  ) : (
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Billing Address
                    </p>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[300px]"></div>
                  ) : (
                    <div className="flex flex-col gap-2">
                      <p className="text-[14px] font-sora font-normal gap-2 text-[#5b5b5b]">
                        {profileData?.billingAddresses?.[0]?.billingAddress
                          ? `${profileData.billingAddresses[0].billingAddress}, ${profileData.billingAddresses[0].billingCity}`
                          : "-"}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className=" rounded-2xl flex flex-col gap-6 bg-[#F5F5F5] px-[24px] py-[24px]">
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[170px]"></div>
              ) : (
                <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                  {profileData?.customerName + "'s" || "-"} History
                </h4>
              )}{" "}
              <div className=" rounded-2xl flex flex-col gap-6 bg-[#F5F5F5] ">
                {profileData?.orderHistory.length > 0 ? (
                  <div className="md:p-6 p-4  rounded-2xl  flex bg-primary-baseWhite flex-col gap-6 relative last:border-none ">
                    {profileData?.orderHistory
                      .slice(0, 3)
                      .map((product: any, index: any) => (
                        <div
                          className="justify-between border-b  md:pb-4 last:border-none items-start gap-3 flex flex-col"
                          key={index}
                        >
                          <div className="flex flex-col md:flex-row gap-6 md:gap-0 md:items-start w-full justify-between">
                            {" "}
                            <div className="flex  items-center gap-2">
                              <div className="rounded-2xl ">
                                <img
                                  className="w-[100px] h-[100px] object-contain rounded-2xl"
                                  src={product?.productImage}
                                  alt="productImage"
                                />
                              </div>
                              <div className="flex flex-col md:h-[100px] gap-3">
                                <p className="text-[#919191] font-sora text-[12px]">
                                  {product?.sku}
                                </p>
                                <h4 className="text-[16px] font-sora text-primary-neutralt1">
                                  {product?.productName}
                                </h4>
                                <p className="text-[#919191] mt-auto font-sora text-[12px]">
                                  Quantity : {product?.quantity || 0}
                                </p>
                              </div>
                            </div>
                            <div className="flex flex-col h-[100px]  text-right">
                              <div>
                                <h4 className="font-sora  text-[#28A745] text-[16px] font-semibold">
                                  ₦ {FormatPrice(product?.totalPrice)}
                                </h4>
                                <p className="text-[14px] text-[#919191] font-sora">
                                  Unit Price : ₦{" "}
                                  {FormatPrice(product?.unitPrice)}
                                </p>
                              </div>
                              <div className="md:mt-auto mt-0">
                                <p className="text-[14px] text-[#919191] font-sora">
                                  {formatDate(product?.orderDate)}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                ) : (
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    {" "}
                    This Customer nas no Order History
                  </p>
                )}
              </div>
            </div>

            <div className="mt-[190px]"></div>
          </div>

          {/* bottom nav */}
          <div className=" border-t-[1px] rounded-2xl fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-10">
            <button
              type="button"
              onClick={openEditCustomer}
              className=" flex flex-col items-center cursor-pointer"
            >
              <div className="bg-[#F5F5F5] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                <ReceiptEdit size="24" color="#2A2A2A" />{" "}
              </div>
              <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                Edit
              </p>
            </button>

            <button
              type="button"
              onClick={closeModal}
              className=" flex flex-col items-center cursor-pointer"
            >
              <div className="bg-[#FAF2F2] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                <ClipboardClose size="24" color="#DC3545" />
              </div>
              <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                Cancel
              </p>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerProfile;
