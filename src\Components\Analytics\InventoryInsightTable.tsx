/* eslint-disable @typescript-eslint/no-explicit-any */
import { InsightTable } from "../../utils/InsightTable";

const InventoryInsightTable = ({ data }: any) => {
  console.log(data)
    const headers = [
      {
        title: 'Products',
        key: 'product',
        render: (row: any) => (
          <p className="text-[#5A5A5A] text-sm font-normal ">{row.product}</p>
        ),
      },
      {
        title: 'Current Stock',
        key: 'currentStock',
        render: (row: any) => (
          <p className="text-[#FFC107] text-xs font-normal !font-inter">
            {row.currentStock}
          </p>
        ),
      },
      {
        title: 'Re-order',
        key: 'reorderLevel',
        render: (row: any) => (
          <p className="text-[#5A5A5A] text-xs font-normal !font-inter">
            {row.reorderLevel ?? 'N/A'}
          </p>
        ),
      },
      {
        title: 'Action',
        key: 'action',
        render: (row: any) => (
          <button
            type="button"
            className="text-[#2A2A2A] font-normal text-xs px-4 border border-[#4F4CD8] py-1 rounded-3xl ">
            {row.action.displayButton}
          </button>
        ),
      },
  ];
  
  const rows = data?.insights?.inventoryData?.lowStockProducts ?? [];

    
  

  return (
    <div className="">
      <h4 className="text-[#3A3A3A] font-semibold text-lg mb-4">
        Inventory Data
      </h4>
      <p className="text-[#8B8B8B] font-normal text-base mb-6">
        Your <span className="font-semibold">total stock in</span> was
        <span className="font-semibold"> 20,000 units</span>, while{' '}
        <span className="font-semibold">total stock out</span> was{' '}
        <span className="font-semibold">10,000 units</span>
      </p>
      <div className="bg-[#F5F5F5] py-6 px-4 rounded-3xl mb-6 ">
        <p className="text-[#7B7B7B] font-normal text-base mb-6">
          <span className="font-semibold">
            T-shirts, Women's Dresses, and Unisex Hoodies
          </span>{' '}
          have low stock levels. Restocking is recommended
        </p>
        <div className="overflow-x-auto max-w-[240px] sm:max-w-full">
          <InsightTable rows={rows} headers={headers} showHead={true} />
        </div>
      </div>
    </div>
  );
}
export default InventoryInsightTable