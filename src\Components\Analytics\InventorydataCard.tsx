/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  MoneyRecive,
} from 'iconsax-react';
import AnalyticButton from '../../Constants/AnalyticButton';
import { useState } from 'react';
import CurrentStockModal from './CurrentStockModal';
import StockMovementModal from './StockMovementModal';

const InventorydataCard = ({
  data,
  isLoading,
  formattedStartDate,
  formattedEndDate,
}: any) => {
  const [currentStockModal, setCurrentStockModal] = useState<boolean>(false);
  const [stockMovementModal, setStockMovementModal] = useState<boolean>(false);

  const handleStockMovementModal = () => {
    setStockMovementModal((prev) => !prev);
  };

  const handleCurrentStockModal = () => {
    setCurrentStockModal((prev) => !prev);
  };
  const currentStockLevel = data?.currentStockLevel;
  const maxStockLevel = Math.max(
    ...currentStockLevel.map((item: any) => item.stockLevel)
  );
  return (
    <>
      <div className="w-full font-sora ">
        <div className="border rounded-xl py-4 px-6">
          <div className="flex gap-4 items-center">
            {isLoading ? (
              <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
            ) : (
              <div className="bg-[#E8F2FB] p-1.5 rounded-full">
                <MoneyRecive size="16" color="#1976D2" variant="Bold" />
              </div>
            )}
            {isLoading ? (
              <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
            ) : (
              <p className="font-semibold text-lg text-[#2A2A2A]">
                Current Stock Level
              </p>
            )}
          </div>
          <div className="flex my-6 justify-between">
            {isLoading ? (
              <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
            ) : (
              <p className="font-semibold text-[#1E1B39] text-2xl">
                {data?.StockLevel?.totalCurrentStock?.toLocaleString()}
              </p>
            )}
            {isLoading ? (
              <div className="animate-pulse bg-slate-200 rounded-3xl w-[40px] "></div>
            ) : (
              <p
                className={`flex items-center rounded-3xl  text-[10px] p-1 ${
                  data?.StockLevel?.percentageChange < 1
                    ? 'border-[#DC3545] border'
                    : 'border-[#AEEBBC] border'
                } `}>
                <span
                  className={`${
                    data?.StockLevel?.percentageChange < 1
                      ? 'text-[#DC3545]'
                      : 'text-[#28A745]'
                  }`}>
                  {data?.StockLevel?.percentageChange || 0} %
                </span>
                {data?.StockLevel?.percentageChange < 1 ? (
                  <ArrowDown className="text-[#DC3545]" size="12" />
                ) : (
                  <ArrowUp className="text-[#28A745]" size="12" />
                )}
              </p>
            )}
          </div>
          {isLoading ? (
            <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] max-w-[320px]"></div>
          ) : (
            <p className="text-[#9B9B9B] text-sm font-normal">
              Current stock levels of your top three best-selling products
            </p>
          )}

          <div className="mt-6">
            {data?.currentStockLevel
              ?.slice(0, 3)
              .map((item: any, index: any) => {
                const stockPercentage = (item.stockLevel / maxStockLevel) * 100;
                const backgroundColors = ['#B4B2EE', '#CDCCF4', '#E6E5F9'];
                const bgColor =
                  backgroundColors[index % backgroundColors.length];

                return (
                  <div className="mt-6" key={item.productName}>
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[28px] my-5"></div>
                    ) : (
                      <div
                        className="h-[36px]"
                        style={{
                          width: `${stockPercentage || 0}%`,
                          backgroundColor: bgColor,
                        }}></div>
                    )}
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[130px]"></div>
                    ) : (
                      <p className="font-bold text-sm text-[#2A2A2A] mt-4">
                        {item.productName}
                      </p>
                    )}
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[190px] mt-4"></div>
                    ) : (
                      <p className="flex items-center gap-1 text-[#5B5B5B]">
                        Stock Level
                        <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9]"></span>
                        <span className="font-semibold">
                          {item.stockLevel.toLocaleString()} units
                        </span>
                      </p>
                    )}
                  </div>
                );
              })}
          </div>
          {/* <div className="mt-6">
            <div
              className=" bg-[#B4B2EE]  h-[36px] "
              style={{ width: `${womenDem || 0}%` }}></div>
            <p className="font-bold text-sm text-[#2A2A2A] mt-4">
              Men's Denim Jacket{' '}
            </p>
            <p className="flex items-center gap-1 text-[#5B5B5B]">
              Stock Level
              <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9] ">
                {' '}
              </span>{' '}
              <span className="font-semibold">2000 units</span>
            </p>
          </div>
          <div className="mt-6">
            <div
              className=" bg-[#CDCCF4]  h-[36px] "
              style={{ width: `${unisex || 0}%` }}></div>
            <p className="font-bold text-sm text-[#2A2A2A] mt-4">
              Women's Summer Dress
            </p>
            <p className="flex items-center gap-1 text-[#5B5B5B]">
              Stock Level
              <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9] ">
                {' '}
              </span>{' '}
              <span className="font-semibold">2000</span>
            </p>
          </div>
          <div className="mt-6">
            <div
              className=" bg-[#E6E5F9]  h-[36px] "
              style={{ width: `${menDem || 0}%` }}></div>
            <p className="font-bold text-sm text-[#2A2A2A] mt-4">
              Unisex Hoodie
            </p>
            <p className="flex items-center gap-1 text-[#5B5B5B]">
              Stock Level
              <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9] ">
                {' '}
              </span>{' '}
              <span className="font-semibold">1200</span>
            </p>
          </div> */}
          {isLoading ? (
            <div className="rounded-2xl animate-pulse bg-slate-200 h-[36px] max-w-[100px] mt-3"></div>
          ) : (
            <AnalyticButton
              title="View More"
              image={ArrowRight}
              onClick={handleCurrentStockModal}
            />
          )}
        </div>
        <div className="border rounded-xl py-4 px-6 mt-6 ">
          <div className="flex gap-4 items-center">
            {isLoading ? (
              <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
            ) : (
              <div className="bg-[#E8F2FB] p-1.5 rounded-full">
                <MoneyRecive size="16" color="#1976D2" variant="Bold" />
              </div>
            )}
            {isLoading ? (
              <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
            ) : (
              <p className="font-semibold text-lg text-[#2A2A2A]">
                Stock Movement{' '}
              </p>
            )}
          </div>
          {isLoading ? (
            <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] max-w-[320px] my-6"></div>
          ) : (
            <p className="text-[#9B9B9B] text-sm font-normal my-6">
              Stock movement of your products between {formattedStartDate} to
              {formattedEndDate}
            </p>
          )}

          <div className="mt-6">
            {data?.stockMovement
              ?.slice(0, 3)
              ?.map((product: any, index: any) => (
                <div
                  className="flex justify-between items-center my-6"
                  key={index}>
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                  ) : (
                    <div className="flex gap-2 items-center">
                      <img
                        src={product.productImage}
                        alt={product.productName}
                        className="w-[32px] h-[32px]"
                      />
                      <span className="font-bold w-[36px] truncate text-xs text-[#5A5A5A]">
                        {product.productName}
                      </span>
                    </div>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[120px]"></div>
                  ) : (
                    <div className="flex gap-2 items-center">
                      <div className="bg-[#EAF7ED] p-1 rounded-md">
                        <ArrowRight size="16" color="#28A745" variant="Bold" />
                      </div>
                      <span className="font-normal text-xs text-[#2A2A2A]">
                        Stock in {product.stockIn.toLocaleString()}
                      </span>
                    </div>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[120px]"></div>
                  ) : (
                    <div className="flex gap-2 items-center">
                      <div className="bg-[#FAF2F2] p-1 rounded-md">
                        <ArrowLeft size="16" color="#DC3545" variant="Bold" />
                      </div>
                      <span className="font-normal text-xs text-[#2A2A2A]">
                        Stock out {product.stockOut.toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>
              ))}
          </div>
          {/* <div className="flex justify-between items-center my-6">
            <div className="flex gap-2 items-center ">
              <img
                src={data?.stockMovementTopThree[0]?.productImage}
                alt="product"
                className="w-[32px] h-[32px]"
              />
              <span className="font-bold text-xs text-[#5A5A5A]">
                Men's Denim Jacket
              </span>
            </div>
            <div className="flex gap-2 items-center ">
              <div className="bg-[#EAF7ED] p-1 rounded-md">
                <ArrowRight size="16" color="#28A745" variant="Bold" />
              </div>
              <span className="font-normal text-xs text-[#2A2A2A]">
                Stock in {data?.stockMovementTopThree[0]?.stockIn}
              </span>
            </div>
            <div className="flex gap-2 items-center ">
              <div className="bg-[#FAF2F2] p-1 rounded-md">
                <ArrowLeft size="16" color="#DC3545" variant="Bold" />
              </div>

              <span className="font-normal text-xs text-[#2A2A2A]">
                Stock out {data?.stockMovementTopThree[0]?.stockOut}
              </span>
            </div>
          </div> */}
          {isLoading ? (
            <div className="rounded-2xl animate-pulse bg-slate-200 h-[36px] max-w-[100px] mt-3"></div>
          ) : (
            <AnalyticButton
              title="View More"
              image={ArrowRight}
              onClick={handleStockMovementModal}
            />
          )}
        </div>
      </div>
      {currentStockModal && (
        <CurrentStockModal
          isOpen={currentStockModal}
          onClose={handleCurrentStockModal}
          data={data}
        />
      )}
      {stockMovementModal && (
        <StockMovementModal
          isOpen={stockMovementModal}
          onClose={handleStockMovementModal}
          data={data}
        />
      )}
    </>
  );
};
export default InventorydataCard;
