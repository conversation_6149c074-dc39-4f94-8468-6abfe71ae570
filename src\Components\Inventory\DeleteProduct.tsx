/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQuery } from 'react-query';
import { inventoryServices } from '../../utils/Services/InventoryServices';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useUserAuthStore } from '../../store/auth';
import { Notifications } from '../../utils/Services/Notification';

interface Product {
  productId: string;
  itemID: string;
  userEmail: string;
  vendorId: string;
  SKU: string;
  productName: string;
  stockLevel: string;
}

const DeleteProduct = ({
  onClose,
  isOpen,
  viewingId,
  refetch,
  productDetails,
  isError,
}: any) => {
  const [product, setProduct] = useState<Product>({
    productId: '',
    itemID: '',
    vendorId: '',
    userEmail:'',
    SKU: '',
    productName: '',
    stockLevel: '',
  });
  const user = useUserAuthStore((state) => state.user);

  useEffect(() => {
    if (productDetails) {
      setProduct({
        ...productDetails,
      });
    }
  }, [productDetails]);
  const mutation = useMutation((formData: FormData) =>
    inventoryServices.editProduct(formData)
  );
   const { refetch: notificationRefetch } = useQuery(
      ["notifications", user.vendorId],
      () => Notifications.getNotification(user.vendorId),
      {
        enabled: !!user.vendorId,
        onSuccess: () => {},
        onError: (error: any) => {
          toast.error("Failed to fetch notifications");
          console.log("notification error:", error);
        },
      }
    );
  const deleteProduct = () => {
    const formData = new FormData();
    formData.append('productId', viewingId);
    formData.append('vendorId', user.vendorId);
    formData.append('userEmail', user.userEmail);
    formData.append('itemID', product.itemID);
    formData.append('status', 'deleted');

    mutation.mutate(formData, {
      onSuccess: (response) => {
        toast.success(response?.data?.message);
        notificationRefetch();
        refetch();
        onClose();
      },
      onError: (error: any) => {
        toast.error(error?.response?.data?.error || error?.message);
      },
    });
  };
  return (
    <div className="fixed font-sora  top-0 left-0 w-full h-full  bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
        <div>
          <span
            onClick={onClose}
            className="close-drawer text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]">
            &times;
          </span>
          {isError ? (
            <div>
              <h2>An error occured, please refresh the page</h2>
            </div>
          ) : (
            <div className="overflow-y-auto h-screen scrollbar-none hidescroll">
              <div className="pl- ">
                <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                  Delete Product
                </h2>
                <p className=" text-sm text-[#919191] font-sora ">
                  Once a product is deleted, it will be removed permanently.{' '}
                </p>
              </div>

              <div>
                <div className="p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6">
                  <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
                    Product Information
                  </h4>
                  <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
                    <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                      Product Name
                    </p>
                    <p className="text-sm font-sora font-normal text-[#5b5b5b]">
                      {product?.productName}
                    </p>
                  </div>
                  <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
                    <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                      SKU
                    </p>
                    <p className="text-sm font-sora font-normal text-[#5b5b5b]">
                      {product?.SKU}
                    </p>
                  </div>
                  <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
                    <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                      Current Stock Level
                    </p>
                    <p className="text-sm font-sora font-normal text-[#5b5b5b]">
                      <span> {product?.stockLevel?.toLocaleString()}</span>{' '}
                      Units
                    </p>
                  </div>
                </div>
                <div className="p-4 mb-44">
                  <h4 className="text-[#DC3545] text-xl font-sora font-semibold leading-[21.6px] mb-4">
                    Confirm Product Deletion
                  </h4>
                  <p className="text-sm font-sora font-normal leading-[22.4px] mb-4 text-[#5b5b5b]">
                    Are you sure you want to delete the product Men’s Denim
                    Jacket? This action cannot be undone and will remove all
                    related data from the system.
                  </p>
                </div>
              </div>
              <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2 md:p-10">
                <div className="flex w-full px-7.5 gap-2.5">
                  <button
                    type="button"
                    onClick={onClose}
                    className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2">
                    Back
                  </button>
                  <button
                    type="button"
                    disabled={mutation.isLoading}
                    onClick={deleteProduct}
                    className={`${
                      mutation.isLoading ? 'opacity-50 cursor-auto' : ''
                    } bg-[#DC3545] w-full text-[#fcfcfc] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 h-[49px] rounded-2xl cursor-pointer flex-2`}>
                    {mutation.isLoading
                      ? 'deleting product ...'
                      : 'Yes, Delete this product'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
export default DeleteProduct;
