/* eslint-disable @typescript-eslint/no-explicit-any */

export function FormatPrice(amount: any): string {
  return new Intl.NumberFormat("en-NG", {
    style: "decimal", // You can change this to 'currency' for automatic currency symbol addition
    minimumFractionDigits: 0, // No decimal points
  }).format(amount);
}

 // eslint-disable-next-line react-refresh/only-export-components
 export const formatDate = (dateString: any) => {
   if (!dateString) return null; // Immediately return null if input is null or empty

   const date = new Date(dateString);
   if (isNaN(date.getTime())) {
     // Check if the date object is valid
     return null;
   } else {
     const year = date.getFullYear();
     const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based, hence add 1
     const day = String(date.getDate()).padStart(2, '0');
     return `${year}-${month}-${day}`;
   }
 };
