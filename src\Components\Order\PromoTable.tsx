/* eslint-disable @typescript-eslint/no-explicit-any */
import { More } from "iconsax-react";
// import sweater from "../../assets/sweater 3.jpg";
import Table from "../../utils/Table";
import OrderTableHead from "./OrderTableHead";
import PromoDetails from "./PromoDetails";
import { useMemo, useState } from "react";
import { orderServices } from "./../../utils/Services/Order";
import { useUserAuthStore } from "../../store/auth";
import { useMutation, useQuery } from "react-query";
import usePagination from "../../Constants/usePagination";
import ProductCard from "../../utils/ProductCard";
import logo from "../../../public/Favicon.png";
import PromoTableDropdown from "./PromoTableDropdown";
import { toast } from "react-toastify";
import ConfirmationModal from "../../Constants/ConfirmationModal";
import PromoEmptyIcon from "../../assets/PromoEmptyIcon.svg";
import leftArrowIcon from "../../assets/leftArrow.svg";
import rightArrowIcon from "../../assets/rightArrow.svg";

const PromoTable = ({
  data,
  customer,
  products,
  isPromoTableLoading,
  isPromoTableError,
  refetchTable,
}: any) => {
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
  const [modalPosition, setModalPosition] = useState<{
    top: number;
  } | null>(null);
  const handleRowClick = (promoCode: any) => {
    setViewEditModal(true);
    setSelectedRow(promoCode);
  };
  const handleActionClick = (event: React.MouseEvent, promoCode: any) => {
    event.stopPropagation();
    setSelectedRow(promoCode);
    const target = event.currentTarget.getBoundingClientRect();
    setModalPosition({
      top: target.top + window.scrollY,
    });
    setDropdownVisible(true);
  };
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [viewEditModal, setViewEditModal] = useState<boolean>(false);
  const [deleteModal, setDeleteModal] = useState<boolean>(false);
  const [deactivate, setDeactivate] = useState<boolean>(false);
  const user = useUserAuthStore((state) => state.user);
  const deleteMutate = useMutation(orderServices.deletePromo);
  const deactivateMutate = useMutation(orderServices.editPromo);


  const handleEditPromoModal = () => {
    setViewEditModal(true);
    setDropdownVisible(false);
  };

  const closeDropDown = () => setDropdownVisible(false);
  const handleDeleteModal = () => {
    setDropdownVisible(false);
    setDeleteModal((prev) => !prev);
  };
  const handleDeactivateModal = () => {
    setDropdownVisible(false);
    setDeactivate((prev) => !prev);
  };
  const handleDelete = () => {
    const payload = {
      vendorId: user.vendorId,
      promoCode: promo?.promoCode,
    };

    try {
      deleteMutate.mutate(payload, {
        onSuccess: (response) => {
          setViewEditModal(false);
          refetchTable();
          toast.success(response?.data?.message);
          setDeleteModal(false);
        },
        onError: (error: any) => {
          toast.error(error?.response?.data?.error || error?.message);
        },
      });
    } catch (error) {
      console.log(error);
    }
  };
  const handleDeactivate = () => {
    const payload = {
      vendorId: user.vendorId,
      promoCode: promo?.promoCode,
      status: "Inactive",
    };

    try {
      deactivateMutate.mutate(payload, {
        onSuccess: (response) => {
          refetchTable();
          refetch();

          toast.success(response?.message);
          setDeactivate(false);
        },
        onError: (error: any) => {
          toast.error(error?.response?.data?.error || error?.message);
        },
      });
    } catch (error) {
      console.log(error);
    }
  };
  const headers = [
    {
      title: "Promo/Sales Name",
      render: (row: any) => (
        <div className="flex items-center gap-2">
          <p className="truncate font-bold">{row.promoName}</p>
        </div>
      ),
    },
    {
      title: "Codes",
      render: (row: any) => <div>{row.promoCode}</div>,
    },
    {
      title: "Type",
      render: (row: any) => <div>{row.promoType}</div>,
    },
    {
      title: "Value",
      render: (row: any) => <div>{row.promoValue}</div>,
    },
    {
      title: "Start & End Date",
      render: (row: any) => (
        <div>
          {`${new Date(row.promoStartDate).toLocaleDateString()} - ${new Date(
            row.promoEndDate
          ).toLocaleDateString()}`}
        </div>
      ),
    },
    {
      title: "Status",
      render: (row: any) => (
        <div
          className={`text-center px-4 py-1 rounded-xl truncate ${
            row.promoStatus === "Active"
              ? "text-[#28a745] bg-[#EAF7ED]"
              : row.promoStatus === "Inactive"
              ? "text-[#FFC107] bg-[#FFF3E0]"
              : "text-[#DC3545] bg-[#F8D7DA]"
          }`}
        >
          {row.promoStatus}
        </div>
      ),
    },
    {
      title: "Usage Limit",
      render: (row: any) => <p>{row.usageLimitType}</p>,
    },

    {
      title: "Action",
      render: (row: any) => (
        <div className="relative">
          <More
            size="16"
            color="#1A1A1A"
            className="cursor-pointer"
            onClick={(event) => handleActionClick(event, row.promoCode)}
          />
        </div>
      ),
    },
  ];

  const {
    data: promo,
    isLoading,
    // isError,
    refetch,
  } = useQuery(
    ["promoDetail", user.vendorId, selectedRow],
    () => orderServices.getPromoDetails(user.vendorId, selectedRow),
    {
      enabled: !!selectedRow,
    }
  );
  console.log("Promo Data:", promo);

  const { page, limit, Pagination } = usePagination({
    page: 1,
    limit: 8,
    total: data?.promoTable?.length,
  });

  const filteredPromos = useMemo(() => {
    return data?.promoTable?.filter((promo: any) => {
      const matchesSearch = promo.promoName
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesCategory =
        selectedCategory === "" || promo.category === selectedCategory;
      const matchesStatus =
        selectedStatus === "" || promo.promoStatus === selectedStatus;

      return matchesSearch && matchesCategory && matchesStatus;
    });
  }, [data?.promoTable, searchTerm, selectedCategory, selectedStatus]);

  const paginatedRows = filteredPromos?.slice((page - 1) * limit, page * limit);


  return (
    <div className="my-15 bg-white border border-[#dedede] font-sora rounded-3xl mb-8 ">
      <OrderTableHead
        searchTerm={searchTerm}
        selectedCategory={selectedCategory}
        selectedStatus={selectedStatus}
        onSearchChange={setSearchTerm}
        onCategoryChange={setSelectedCategory}
        onStatusChange={setSelectedStatus}
      />
      <div className="relative ">
        {/* <Table rows={data?.promoTable} headers={headers} showHead={true} allowRowClick onRowClick={(row) => handleRowClick(row.promoCode)} /> */}
        <div className="spinner" id="products-loading-1"></div>

        {isPromoTableError ? (
          <div className="text-center py-10 mb-5">
            Error loading data, refresh
          </div>
        ) : (
          <div className="relative">
            {isPromoTableLoading ? (
              <div className="text-center py-10 flex justify-center">
                <img
                  className="animate-spin w-[50px] h-[50px]"
                  src={logo}
                  alt="logo"
                />
              </div>
            ) : (
              <>
                {filteredPromos && filteredPromos.length > 0 ? (
                  <>
                    <div className="hidden md:block">
                      <Table
                        rows={paginatedRows}
                        headers={headers}
                        showHead={true}
                        allowRowClick
                        onRowClick={(row) => handleRowClick(row.promoCode)}
                      />
                    </div>

                    {paginatedRows.map((product: any, index: any) => (
                      <div key={index} className="md:hidden mt-4 px-5">
                        <ProductCard
                          productName={product.promoName}
                          productSKU={product.promoCode}
                          promoType={product.promoType}
                          status={product.status}
                          promoValue={product.promoValue}
                          promoStatus={product.promoStatus}
                          onClick={() => handleRowClick(product.promoCode)}
                        />
                      </div>
                    ))}

                    <div className="px-5 w-full">
                      <Pagination />
                    </div>
                  </>
                ) : (
                  <div className="flex mb-7 flex-col items-center justify-center text-center mt-8 space-y-4">
                    <img
                      src={PromoEmptyIcon}
                      alt="No Data"
                      className="w-16 h-16"
                    />
                    <p className="text-sm p-4 md:p-2 font-sora text-[#5B5B5B]">
                      You currently have no discount of coupons,once you have
                      run any promotions they will appear here.
                    </p>
                  </div>
                )}
              </>
            )}

            <div className="hidden md:block">
              <div className="p-4 items-center gap-4 justify-end text-[#5A5A5A] text-[14px] flex">
                <p>0 items</p>

                <button className="py-3 px-3.5 border rounded-2xl">
                  Previous
                </button>
                <button className="py-3 px-3.5 border rounded-2xl">Next</button>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 md:hidden mb-4 items-center text-[#5A5A5A] flex p-4 justify-between">
          <p>0 items</p>
          <div className="flex gap-4">
            <div className="py-3 border border-[#DEDEDE] rounded-2xl px-3">
              <img src={rightArrowIcon} alt="" />
            </div>
            <div className="py-3 border border-[#DEDEDE] rounded-2xl px-3">
              <img src={leftArrowIcon} alt="" />
            </div>
          </div>
        </div>
      </div>
      {dropdownVisible && modalPosition && (
        <PromoTableDropdown
          promoCode={selectedRow}
          position={modalPosition}
          onEditPromo={handleEditPromoModal}
          onDeactivatePromo={handleDeactivateModal}
          onDeletePromo={handleDeleteModal}
          onClose={closeDropDown}
        />
      )}
      {viewEditModal && selectedRow && (
        <PromoDetails
          isOpen={viewEditModal}
          closeModal={() => setViewEditModal(false)}
          promo={promo}
          refetch={refetch}
          customer={customer}
          products={products}
          isLoading={isLoading}
          refetchTable={refetchTable}
          handleDeleteModal={handleDeleteModal}
          handleDeactivateModal={handleDeactivateModal}
        />
      )}
      {deleteModal && (
        <ConfirmationModal
          isOpen={deleteModal}
          title="Are you sure you want to delete this promo?"
          text="This action will remove this promo."
          actionButtonText={
            deleteMutate?.isLoading ? "Deleting Promo" : "Delete Promo"
          }
          cancelButtonText="No, Cancel"
          onActionClick={handleDelete}
          onCancelClick={handleDeleteModal}
          actionButtonStyle="bg-[#DC3545]"
          actionButtonDisabled={deleteMutate?.isLoading}
        />
      )}
      {deactivate && (
        <ConfirmationModal
          isOpen={deactivate}
          title="Are you sure you want to deactivate this promo?"
          text="This action will deactivate this promo."
          actionButtonText={
            deactivateMutate?.isLoading
              ? "Deactivating Promo"
              : "Deactivate Promo"
          }
          cancelButtonText="No, Cancel"
          onActionClick={handleDeactivate}
          onCancelClick={handleDeactivateModal}
          actionButtonStyle="bg-[#FF9900]"
          actionButtonDisabled={deactivateMutate?.isLoading}
        />
      )}
    </div>
  );
};
export default PromoTable;
