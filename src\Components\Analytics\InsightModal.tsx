/* eslint-disable @typescript-eslint/no-explicit-any */
import { ArrowUp } from 'iconsax-react';
import msg from '../../assets/msg.svg';
import InsightCard from '../../Constants/InsightCard';
import Insights from './Insights';
import { analyticsServices } from '../../utils/Services/Analytics';
import { useUserAuthStore } from '../../store/auth';
import { useQuery } from 'react-query'
import { useEffect, useRef } from 'react';


const InsightModal = ({ isOpen, onClose }: any) => {
  const user = useUserAuthStore((state) => state.user);
  const { data } = useQuery(
    ['analyticsInsight', user.vendorId],
    () => analyticsServices.getInsight(user.vendorId),
    {
      enabled: !!user.vendorId, // Only fetch if vendorId is available
    }
  );

   const modalRef = useRef<HTMLDivElement | null>(null);


  useEffect(() => {
    if (data) {
      console.log('Fetched data:', data);
      // Any additional logic you want to execute when data changes
    }
     if (isOpen && modalRef.current) {
       modalRef.current.focus();
     }
  }, [data,isOpen]);

  return (
    <div
      ref={modalRef}
      tabIndex={-1}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
        }
      }}
      className="fixed font-sora  top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out"
    >
      <div
        className={`fixed top-0 right-0 h-screen md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={onClose}
            className="text-4xl font-bold cursor-pointer my-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="overflow-y-auto h-screen scrollbar-none hidescroll pb-32">
            <div className="flex items-center gap-3">
              <img src={msg} alt="msg-icon" />
              <p className="text-[#181818] font-semibold text-2xl">
                Get Insights
              </p>
            </div>
            <p className="text-[#919191] font-normal text-sm mt-2">
              AI-driven insights to boost your business performance and
              decision-making
            </p>
            <h4 className="text-[#3A3A3A] font-semibold text-lg mt-6 mb-4">
              Sales Performance{" "}
            </h4>
            <div className="flex flex-wrap items-center ">
              <div className="flex items-center ">
                <p className="text-nowrap text-[#9B9B9B] font-normal text-base">
                  <span className="font-semibold">Sales </span>grew by
                </p>
                <p className="flex items-center rounded-2xl mx-1 text-[#28A745] text-[10px] p-0.5 border border-[#AEEBBC]">
                  <span> 16%</span> <ArrowUp size="12" />
                </p>
              </div>
              <div className="flex items-center flex-wrap">
                <p className="text-nowrap text-[#9B9B9B] font-normal text-base">
                  <span className="font-semibold">Refunds reduced </span> by{" "}
                </p>{" "}
                <p className="flex items-center rounded-2xl mx-1 text-[#28A745] text-[10px] p-0.5 border border-[#AEEBBC]">
                  <span> 16%</span> <ArrowUp size="12" />
                </p>
                <p className="text-[#9B9B9B] font-normal text-base">
                  suggesting
                </p>
              </div>
            </div>
            <p className="md:text-nowrap text-[#9B9B9B] font-normal text-base mb-8">
              improved customer satisfaction.
            </p>
            <InsightCard
              buttonText="Boost Ads"
              text={
                <div className="">
                  <div className=" flex items-center flex-wrap">
                    <p className="text-[#7B7B7B] font-normal text-base">
                      {" "}
                      Your{" "}
                      <span className="font-semibold mx-1">
                        {" "}
                        Sales By Channel{" "}
                      </span>
                      show
                    </p>
                    <p className="flex items-center rounded-2xl mx-1 text-[#28A745] text-[10px] p-0.5 border border-[#AEEBBC]">
                      <span> 16%</span> <ArrowUp size="12" />
                    </p>
                    <span className="text-[#7B7B7B] font-normal text-base">
                      {" "}
                      through online
                    </span>
                  </div>{" "}
                  <p className="text-[#7B7B7B] font-normal text-base max-w-[468px] w-full">
                    {" "}
                    channels. Focus more on your top-performing social media
                    campaigns to increase sales
                  </p>
                </div>
              }
            />
            <InsightCard
              text={
                <p className="text-[#7B7B7B] font-normal text-base max-w-[468px] w-full">
                  <span className="font-semibold"> Men’s Denim Jacket</span> is
                  your top-selling product, accounting for{" "}
                  <span className="font-semibold">₦2,400,000</span>. Consider
                  increasing inventory for fast-moving products
                </p>
              }
              buttonText="Restock"
            />
            <InsightCard
              text={
                <p className="text-[#7B7B7B] font-normal text-base max-w-[468px] w-full">
                  Your <span className="font-semibold">discounts </span>
                  contributed <span className="font-semibold">₦600,000</span> to
                  total revenue. Run targeted offers for repeat customers
                </p>
              }
              buttonText="Create New Discount"
            />
            <Insights data={data} />
          </div>
        </div>
      </div>
    </div>
  );
};
export default InsightModal;
