// import { InfoCircle } from 'iconsax-react';
import DatePicker from '../../utils/DatePicker';
import { useState } from 'react';
import InsightModal from './InsightModal';

const DateHead = () => {
  const [insightModal, setInsightModal] = useState<boolean>(false);
  const handleInsightModal = () => {
    setInsightModal((prev) => !prev);
  };
  return (
    <div className="flex flex-col md:flex-row gap-3 md:gap-0 items-center md:justify-between my-10">
      <div className='w-full md:w-fit'>
        <DatePicker />
      </div>
      {/* <div className='w-full md:w-[180px]'>
        <button
          type="button"
          onClick={handleInsightModal}
          className={`border hover:border-primary-purple duration-300 w-full justify-center items-center font-semibold text-base font-sora rounded-[16px] h-[49px] flex gap-2`}>
          <InfoCircle size="16" color="#1A1A1A" />
          <span> Get Insights </span>
        </button>
      </div> */}
      {insightModal && (
        <InsightModal isOpen={insightModal} onClose={handleInsightModal} />
      )}
    </div>
  );
};
export default DateHead;
