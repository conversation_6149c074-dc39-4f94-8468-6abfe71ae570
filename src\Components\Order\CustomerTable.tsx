/* eslint-disable @typescript-eslint/no-explicit-any */

import { More, SearchNormal1 } from "iconsax-react";
import Table from "../../utils/Table";
import { useMemo, useState } from "react";
import CustomerProfile from "./CustomerProfile";
import { useQuery } from "react-query";
import { orderServices } from "./../../utils/Services/Order";
import { useUserAuthStore } from "../../store/auth";
import usePagination from "../../Constants/usePagination";
import ProductCard from "../../utils/ProductCard";
import CustomerTableDropDown from "./CustomerTableDropDown";
import EditCustomer from "./EditCustomer";
import { FormatPrice } from "../../utils/FormatPrice";
import Filter from "../../Constants/Filter";
import logo from "../../../public/Favicon.png";

const CustomerTable = ({
  customerData,
  customerIsLoading,
  customerIsError,
  refetchTable
}: any) => {
  const user = useUserAuthStore((state) => state.user);
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const handleRowClick = (customerId: any) => {
    setViewCustomerModal(customerId);
    setSelectedRow(customerId);
  };
  const [dropDown, setDropDown] = useState<any>(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
  const [viewCustomerModal, setViewCustomerModal] = useState<boolean>(false);
  const [editCustomerModal, setEditCustomerModal] = useState<boolean>(false);
  const [modalPosition, setModalPosition] = useState<{
    top: number;
  } | null>(null);
  const headers = [
    {
      title: "Customer Name",
      key: "customerName",
      render: (row: any) => (
        <div className="flex items-center gap-2">
          <p className="truncate font-bold">{row.customerName}</p>
        </div>
      ),
    },
    {
      title: "Phone Number",
      key: "phoneNumber",
      render: (row: any) => <div>{row.phoneNumber}</div>,
    },
    {
      title: "Email Address",
      key: "email",
      render: (row: any) => <div>{row.emailAddress}</div>,
    },
    {
      title: "Average Spend/Order",
      key: "avgSpend",
      render: (row: any) => <div>{FormatPrice(row.avgSpendPerOrder)} </div>,
    },
    {
      title: "Date Added",
      key: "dateAdded",
      render: (row: any) => (
        <div>{new Date(row.dateAdded).toLocaleDateString()}</div>
      ),
    },
    {
      title: "Status",
      key: "status",
      render: (row: any) => (
        <div
          className={`p-3 truncate  align-middle  text-xs  ${
            row.status === "New" ? "text-[#28a745]" : "text-[#FFC107]"
          }`}
        >
          <span
            className={`px-4 py-1  rounded-xl ${
              row.status === "New" ? "bg-[#EAF7ED]" : "bg-[#FFF9E6]"
            }`}
          >
            {row.status}
          </span>
        </div>
      ),
    },

    {
      title: "Action",
      render: (row: any) => (
        <div className="relative">
          <More
            size="16"
            color="#1A1A1A"
            onClick={(event) => handleActionClick(event, row.customerId)}
            className="cursor-pointer"
          />
        </div>
      ),
    },
  ];

  const {
    data: profileData,
    isLoading,
    // isError,
    refetch,
  } = useQuery(
    ["customerProfile", selectedRow, user.vendorId],
    () => orderServices.getProfile(selectedRow, user.vendorId),
    {
      enabled: !!selectedRow,
    }
  );
  const handleActionClick = (event: React.MouseEvent, orderId: string) => {
    event.stopPropagation();
    setSelectedRow(orderId);
    const target = event.currentTarget.getBoundingClientRect();
    setModalPosition({
      top: target.top + window.scrollY,
    });
    setDropdownVisible(true);
  };

  const { page, limit, Pagination } = usePagination({
    page: 1,
    limit: 8,
    total: customerData?.customerTable?.length,
  });

  const filteredCustomers = useMemo(() => {
    return customerData?.customerTable?.filter((customer: any) => {
      const matchesSearch = customer?.customerName
        ?.toLowerCase()
        ?.includes(searchTerm.toLowerCase());

      const matchesStatus =
        selectedStatus === "" || customer.status === selectedStatus;

      return matchesSearch && matchesStatus;
    });
  }, [customerData?.customerTable, searchTerm, selectedStatus]);

  const paginatedRows = filteredCustomers?.slice(
    (page - 1) * limit,
    page * limit
  );

  const handleSearchChange = (event: any) => {
    setSearchTerm(event.target.value);
  };

  const handleStatusChange = (event: any) => {
    setSelectedStatus(event.target.value);
  };
  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    handleStatusChange(e);
    setDropDown(false);
  };
  const handleViewCustomer = () => {
    setViewCustomerModal(true);
    setDropdownVisible(false);
  };
  const handleEditCustomer = () => {
    setEditCustomerModal(true);
    setDropdownVisible(false);
  };
  const closeDropDown = () => setDropdownVisible(false);

  return (
    <div className="my-15 bg-white border border-[#dedede] font-sora rounded-3xl mb-8 ">
      <div className="flex flex-col md:flex-row justify-between items-start p-5">
        <div className="table-tittle md:w-[397px]">
          <h1 className="text-lg text-[#393939] mb-2 font-semibold ">
            Customers List
          </h1>
          <p className="text-sm text-[#919191] font-normal font-sora mb-2.5">
            See all information about your customers
          </p>
        </div>
        <div className="w-full md:w-[442px] flex flex-col md:flex-row justify-between gap-2.5">
          <div className="flex w-full gap-2 items-center">
            <div className="flex items-center h-[42px] gap-[10px] py-2 px-2 border border-gray-300 text-[#ABABAB] text-xs rounded-2xl bg-[#fcfcfc] w-full">
              <SearchNormal1 size="20" color="#ABABAB" />
              <input
                type="text"
                id="searchInput"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearchChange}
                className="bg-[#fcfcfc] outline-none "
              />
            </div>

            <div className="md:hidden">
              <Filter
                statuses="Status"
                stat1="New"
                stat2="Returning"
                onCustomerChange={handleFilterChange}
                setDropDown={setDropDown}
                dropDown={dropDown}
                selectedStatus={selectedStatus}
              />
            </div>
          </div>

          <select
            id="statusFilter"
            value={selectedStatus}
            onChange={handleStatusChange}
            className="py-2 hidden md:inline px-4 border text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none w-full"
          >
            <option value="">Filter by Status</option>
            <option value="New">New</option>
            <option value="Returning">Returning</option>
          </select>
        </div>
      </div>
      {customerIsError ? (
        <div className="text-center py-10 mb-5">
          Error loading data, refresh
        </div>
      ) : (
        <div className="relative">
          {customerIsLoading ? (
            <div className="text-center py-10 flex justify-center">
              <img
                className="animate-spin w-[50px] h-[50px]"
                src={logo}
                alt="logo"
              />
            </div>
          ) : (
            <>
              {filteredCustomers && filteredCustomers.length > 0 ? (
                <>
                  <div className="hidden md:block">
                    <Table
                      rows={paginatedRows}
                      headers={headers}
                      showHead={true}
                      allowRowClick
                      onRowClick={(row) => handleRowClick(row.customerId)}
                    />
                  </div>

                  {paginatedRows?.map((product: any, index: any) => (
                    <div key={index} className="md:hidden mt-4 px-5">
                      <ProductCard
                        productName={product.customerName}
                        date={product.dateAdded.split("T")[0]}
                        phoneNumber={product.phoneNumber}
                        customerStatus={product.status}
                        onClick={() => handleRowClick(product.customerId)}
                      />
                    </div>
                  ))}
                  <div className="px-5">
                    <Pagination />
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center text-center my-[70px]">
                  <p className="mt-6 text-base font-medium">
                    Customer list is currently empty, proceed to add customer
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      )}

      {dropdownVisible && modalPosition && (
        <CustomerTableDropDown
          customerId={selectedRow}
          position={modalPosition}
          onViewOrder={handleViewCustomer}
          onEditOrder={handleEditCustomer}
          onClose={closeDropDown}
        />
      )}
      {viewCustomerModal && selectedRow && (
        <CustomerProfile
          isOpen={viewCustomerModal}
          closeModal={() => setViewCustomerModal(false)}
          profileData={profileData}
          openEditCustomer={handleEditCustomer}
          isLoading={isLoading}
        />
      )}
      {editCustomerModal && selectedRow && (
        <EditCustomer
          isOpen={editCustomerModal}
          closeModal={() => setEditCustomerModal(false)}
          profileData={profileData}
          refetch={refetch}
          refetchTable={refetchTable}
        />
      )}
    </div>
  );
};
export default CustomerTable;
