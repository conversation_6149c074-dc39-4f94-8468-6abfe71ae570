/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from 'react';
import ProductImageCard from './ProductImageCard';
import InputField from '../../Constants/InputField';
import { DocumentUpload } from 'iconsax-react';
import PreviewEditProduct from './PreviewEditProduct';
import { generateSku } from '../../utils/Helpers';
import { toast } from 'react-toastify';
import { useMutation } from 'react-query';
import { inventoryServices } from '../../utils/Services/InventoryServices';
import aiStar from '../../assets/aiStar.svg';
import { useUserAuthStore } from '../../store/auth';

interface Product {
  SKU: string;
  userEmail: string;
  productName: string;
  category: string;
  description: string;
  stockLevel: string;
  reorderLevel: string;
  supplierName: string;
  discountedPrice: number;
  storageLocation: string;
  purchasePrice: string;
  supplierEmail: string;
  sellingPrice: number;
  discount: number;
  batchNumber: string;
  notes: string;
  images: any[];
  countryCode: string;
  businessPhone: string;
  itemID: string;
}
const EditProduct = ({
  onClose,
  isOpen,
  viewingId,
  refetch,
  productDetails,
  isError,
  productRefetch,
}: any) => {
  const [previewData, setPreviewData] = useState<Product | null>(null);
  const [coverImageId, setCoverImageId] = useState<number>(1);
  const [isPreview, setIsPreview] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const user = useUserAuthStore((state) => state.user);
  const [product, setProduct] = useState<Product>({
    SKU: '',
    userEmail: '',
    productName: '',
    category: '',
    description: '',
    stockLevel: '',
    reorderLevel: '',
    supplierName: '',
    discountedPrice: 0,
    storageLocation: '',
    purchasePrice: '',
    supplierEmail: '',
    sellingPrice: 0,
    discount: 0,
    batchNumber: '',
    notes: '',
    itemID: '',
    businessPhone: '',
    images: [],
    countryCode: '',
  });

  const writeWithAIMutation = useMutation(inventoryServices.aiDescription);

  useEffect(() => {
    if (productDetails) {
      const supplierPhone = productDetails?.supplierPhone || '';
      let countryCode = '+234';
      let businessPhone = supplierPhone;

      if (supplierPhone.startsWith('+234')) {
        countryCode = '+234';
        businessPhone = supplierPhone.slice(4).trim();
      } else if (supplierPhone.startsWith('+1')) {
        countryCode = '+1';
        businessPhone = supplierPhone.slice(2).trim();
      }
      setProduct({
        ...productDetails,
        businessPhone,
        countryCode,
      });
    }
  }, [productDetails]);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    if (name === 'productName') {
      const sku = generateSku(value);
      setProduct((prevDetails) => ({
        ...prevDetails,
        productName: value,
        SKU: sku,
      }));
    } else {
      setProduct((prevDetails) => ({
        ...prevDetails,
        [name]: value,
      }));
    }
    if (name === 'purchasePrice' || name === 'sellingPrice') {
      const updatedPurchasePrice =
        name === 'purchasePrice' ? +value : +product.purchasePrice;
      const updatedSellingPrice =
        name === 'sellingPrice' ? +value : +product.sellingPrice;

      if (updatedSellingPrice < updatedPurchasePrice) {
        setErrorMessage('Selling price cannot be less than purchase price');
      } else {
        setErrorMessage(null);
      }
    }
  };

  // const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const files = e.target.files;
  //   const maxImages = 10;

  //   if (files && files.length > 0) {
  //     const fileArray = Array.from(files);
  //     if (product.images.length + fileArray.length > maxImages) {
  //       toast.error(`You can only upload a maximum of ${maxImages} images.`);
  //       return;
  //     }
  //     const newImages = fileArray.map((file) => {
  //       const imageUrl = URL.createObjectURL(file);
  //       return {
  //         file,
  //         previewUrl: imageUrl,
  //       };
  //     });
  //     setProduct((prevDetails) => ({
  //       ...prevDetails,
  //       images: [...prevDetails.images, ...newImages],
  //     }));
  //   }
  // };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    const maxImages = 4; 
    const maxImageSize = 3 * 1024 * 1024; 
    const maxVideoSize = 10 * 1024 * 1024; 

    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);

    const validImages = fileArray.filter((file) => {
      if (file.type.startsWith('image/')) {
        if (file.size > maxImageSize) {
          toast.error(`Image "${file.name}" exceeds 3MB limit`);
          return false;
        }
        return true;
      }
      return false;
    });

    const validVideos = fileArray.filter((file) => {
      if (file.type.startsWith('video/')) {
        if (file.size > maxVideoSize) {
          toast.error(`Video "${file.name}" exceeds 10MB limit`);
          return false;
        }
        return true;
      }
      return false;
    });

    if (validVideos.length > 0) {
      if (validVideos.length > 1) {
        toast.error('You can only upload one video');
        return;
      }

      const videoFile = validVideos[0];
      const videoUrl = URL.createObjectURL(videoFile);

      setProduct((prevDetails) => {
        const hasExistingVideo = prevDetails.images.some(
          (img) => img.type === 'video'
        );

        if (hasExistingVideo) {
          toast.error('Only one video is allowed');
          return prevDetails;
        }

        const updatedImages =
          prevDetails.images.length >= maxImages
            ? [...prevDetails.images.slice(0, -1)]
            : [...prevDetails.images];

        return {
          ...prevDetails,
          images: [
            ...updatedImages,
            { file: videoFile, previewUrl: videoUrl, type: 'video' },
          ],
        };
      });
    }

    if (validImages.length > 0) {
      const newImages = validImages.map((file) => ({
        file,
        previewUrl: URL.createObjectURL(file),
        type: 'image',
      }));

      setProduct((prevDetails) => {
        const totalMediaCount = prevDetails.images.length + newImages.length;

        if (totalMediaCount > maxImages) {
          const availableSlots = maxImages - prevDetails.images.length;
          toast.error(
            `You can only upload ${availableSlots} more file(s) (max ${maxImages} total)`
          );
          return prevDetails;
        }

        return {
          ...prevDetails,
          images: [...prevDetails.images, ...newImages],
        };
      });
    }
  };
  const handleBack = () => {
    setIsPreview(false);
  };
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setPreviewData(product);
    setIsPreview(true);
  };
  const handleSetCover = (index: number) => {
    setCoverImageId(index);
  };

  const handleRemoveImage = (index: number) => {
    setProduct((prevDetails) => ({
      ...prevDetails,
      images: prevDetails.images.filter((_, i) => i !== index),
    }));
  };
  const handleAIDESC = async () => {
    const payload = {
      productName: productDetails.productName,
      category: productDetails.category,
      description: productDetails.description,
      vendorId: user.vendorId,
    };
    writeWithAIMutation.mutate(payload, {
      onSuccess: (response) => {
        const { enhancedDescription } = response.data;
        toast('Description Optimized');
        setProduct((prev) => ({
          ...prev,
          description: enhancedDescription,
        }));
      },
      onError: (error) => {
        console.error('Error optimizing description:', error);
        toast.error('Failed to optimize description.');
      },
    });
  };
  const discountedPrice =
    product.sellingPrice - product.sellingPrice * (product.discount / 100);
  product.discountedPrice = discountedPrice;

  return (
    <div className="fixed font-sora  top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-2 md:p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
        <div>
          <span
            onClick={onClose}
            className="text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]">
            &times;
          </span>
          {isError ? (
            <div>
              <h2>An error occured, please refresh the page</h2>
            </div>
          ) : (
            <div className="overflow-y-auto h-screen scrollbar-none hidescroll">
              {!isPreview ? (
                <form onSubmit={handleSubmit}>
                  <div className="p-6 md:pl-6">
                    <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold">
                      Edit Product 
                    </h2>
                    <p className="font-sora text-sm text-[#919191]">
                      You can update the details of the product below
                    </p>
                  </div>
                  <div>
                    <div className="p-6 rounded-[16px] my-0 md:my-5 flex flex-col gap-6 ">
                      <h4 className="text-[16px] font-sora font-semibold text-[#2a2a2a]">
                        Product Information
                      </h4>
                      <InputField
                        label="Product Name"
                        name="productName"
                        placeholder="Enter your product name"
                        value={product.productName}
                        onChange={handleChange}
                      />
                      <InputField
                        name="SKUNumber"
                        label="SKU Number"
                        placeholder="SKU is auto generated"
                        readOnly={true}
                        value={product.SKU}
                        onChange={handleChange}
                      />
                      <InputField
                        name="category"
                        label="Category"
                        placeholder="Enter category name"
                        value={product.category}
                        onChange={handleChange}
                      />
                      {/* <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0">
                        <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                          Description
                        </p>
                        <textarea
                          rows={12}
                          className=" text-[#7b7b7b] text-xs p-4 border border-[#cccccc] rounded-2xl outline-none"
                          placeholder="Enter description of the product "
                          name="description"
                          value={product.description}
                          onChange={handleChange}></textarea>
                      </div> */}
                      <div className="p-0 flex flex-col gap-2 relative">
                        <p className="text-sm font-sans font-normal text-[#5b5b5b]">
                          Description
                        </p>
                        <textarea
                          rows={12}
                          className=" text-[#7b7b7b] text-xs p-4 border border-[#cccccc] rounded-2xl outline-none"
                          placeholder="Enter description of the product "
                          name="description"
                          value={product.description}
                          onChange={handleChange}></textarea>
                        {/* Loading overlay */}
                        {writeWithAIMutation.isLoading && (
                          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 rounded-2xl">
                            <div className="flex flex-col items-center ">
                              <div className="  animate-spin">
                                <img src={aiStar} />
                              </div>
                              <p className="text-xs text-gray-500 mt-2">
                                Loading optimized description...
                              </p>
                            </div>
                          </div>
                        )}
                        <div className="absolute bottom-2 right-2 mt-5">
                          <button
                            type="button"
                            onClick={handleAIDESC}
                            disabled={writeWithAIMutation.isLoading}
                            className={`flex items-center bg-[#e6e5f9] p-2.5 gap-2.5 text-[#4f4cd8] rounded-2xl font-semibold cursor-pointer border border-[#4f4cd8]`}>
                            <img
                              className={`${
                                writeWithAIMutation.isLoading
                                  ? 'animate-spin'
                                  : ''
                              }`}
                              src={aiStar}
                              alt="ai"
                            />
                            <span className="text-xs sm:text-sm">
                              {writeWithAIMutation.isLoading
                                ? 'Optimizing with AI'
                                : productDetails.description
                                ? 'Optimize with AI'
                                : 'Write with AI'}
                            </span>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="p-6 rounded-[16px] my-5 flex flex-col gap-6 ">
                      <h4 className="text-[16px] font-sora font-semibold text-[#2a2a2a]">
                        Product Images
                      </h4>
                      <>
                        <div className="">
                          {product?.images?.map((image, index) => {
                            const isUploadedImage = image?.file;

                            return (
                              <ProductImageCard
                                key={index}
                                imageUrl={
                                  isUploadedImage ? image?.previewUrl : image
                                }
                                imageName={
                                  isUploadedImage
                                    ? image?.file?.name
                                    : 'Existing Image'
                                }
                                imageSize={
                                  isUploadedImage
                                    ? `${(image?.file?.size / 1024).toFixed(
                                        2
                                      )} KB`
                                    : 'Unknown Size'
                                }
                                imageNumber={index + 1}
                                isCover={index === coverImageId}
                                onSetCover={() => handleSetCover(index)}
                                onRemove={() => handleRemoveImage(index)}
                              />
                            );
                          })}

                          {/* {product?.images?.map((image, index) => (
                            <ProductImageCard
                              key={index}
                              imageUrl={image}
                              imageName={image?.file?.name}
                              imageSize={`${(image?.file?.size / 1024).toFixed(
                                2
                              )} KB`}
                              imageNumber={index + 1}
                              isCover={index === coverImageId}
                              onSetCover={() => handleSetCover(index)}
                              onRemove={() => handleRemoveImage(index)}
                            />
                          ))} */}
                        </div>

                        <p className="mb-2.5 text-sm font-sora text-[#5b5b5b]">
                          Upload Product Image
                        </p>
                        <div className="border border-dashed border-[#6200ea] rounded-2xl p-4 text-center cursor-pointer bg-[#e6e5f9] transition-colors duration-300">
                          <label htmlFor="fileInput" className="cursor-pointer">
                            <input
                              type="file"
                              accept="image/*"
                              id="fileInput"
                              multiple
                              onChange={handleFileChange}
                              name="productImages"
                              className="hidden"
                            />
                            <div className="flex flex-col items-center">
                              <div className=" mb-2.5 ">
                                <DocumentUpload size="32" color="#4F4CD8" />
                              </div>
                              <p className="text-base text-[#5b5b5b]">
                                Click here to upload or Drag & Drop
                              </p>
                              <p className="small-text mt-[5px] text-[#9b9b9b] text-xs">
                                Maximum file size is 10MB
                              </p>
                            </div>
                          </label>
                        </div>
                      </>
                    </div>
                    <div className="p-6 rounded-[16px] my-5 flex flex-col gap-6 ">
                      <h4 className="text-[16px] font-sora font-semibold text-[#2a2a2a]">
                        Inventory Details
                      </h4>
                      <InputField
                        label=" Initial Stock Level"
                        name="stockLevel"
                        placeholder="Enter how much you have in stock currently"
                        type="number"
                        value={product.stockLevel}
                        onChange={handleChange}
                      />
                      <InputField
                        label="Reorder Stock Level"
                        placeholder="How much stock should be left before reordering"
                        name="reorderLevel"
                        type="number"
                        value={product.reorderLevel}
                        onChange={handleChange}
                      />
                      <InputField
                        label="Supplier Name"
                        placeholder="Enter Supplier Name"
                        name="supplierName"
                        value={product.supplierName}
                        onChange={handleChange}
                      />
                      <div className="p-0 flex flex-col gap-2 relative">
                        <p className="text-sm font-sans font-normal text-[#5b5b5b]">
                          Supplier Phone Number (Optional)
                        </p>
                        <div className="flex items-center">
                          <select
                            name="countryCode"
                            className="mr-2.5 flex-shrink-0 flex-[0.3] text-xs text-[#7b7b7b] h-[48px] px-3.5 border border-[#cccccc] rounded-2xl outline-none"
                            value={product.countryCode}
                            onChange={handleChange}>
                            <option value="+234">🇳🇬 +234</option>
                            <option value="+1">🇺🇸 +1</option>
                          </select>
                          <input
                            className="text-xs flex-1 text-[#7b7b7b] h-[48px] px-3.5 border border-[#cccccc] rounded-2xl outline-none "
                            type="text"
                            name="businessPhone"
                            placeholder="************"
                            value={product.businessPhone}
                            onChange={handleChange}
                          />
                        </div>
                      </div>
                      <InputField
                        label="Supplier Email (Optional)"
                        placeholder="Enter Supplier Email"
                        name="supplierEmail"
                        value={product.supplierEmail}
                        onChange={handleChange}
                      />
                      <InputField
                        label="Storage Location (Optional)"
                        placeholder="Enter where this product is stored (Shelf Number etc)"
                        name="storageLocation"
                        value={product.storageLocation}
                        onChange={handleChange}
                      />
                    </div>
                    <div className="p-6 rounded-[16px] my-5 flex flex-col gap-6 ">
                      <h4 className="text-[16px] font-sora font-semibold text-[#2a2a2a]">
                        Pricing Information
                      </h4>
                      <InputField
                        label=" Purchase Price"
                        placeholder="Enter the amount you purchased this product"
                        name="purchasePrice"
                        type="number"
                        value={product?.purchasePrice}
                        onChange={handleChange}
                        price={true}
                      />
                      <InputField
                        label="Selling Price"
                        placeholder="Enter the amount you want to sell this product"
                        name="sellingPrice"
                        type="number"
                        value={product?.sellingPrice?.toString()}
                        onChange={handleChange}
                        price={true}
                      />

                      {errorMessage && (
                        <p className="text-[#DC3545] border border-[#DC3545] bg-[#FAF2F2] py-1 px-2.5 rounded-full text-xs font-sora">
                          {errorMessage}
                        </p>
                      )}
                      <InputField
                        label="Apply Discount % (Optional)"
                        placeholder="Enter Supplier Name"
                        name="discount"
                        value={product?.discount?.toString()}
                        onChange={handleChange}
                      />
                      <InputField
                        label="Discounted Price"
                        placeholder="Price will be determined by discount %"
                        value={product?.discountedPrice?.toFixed(2)}
                        readOnly={true}
                        name="discountedPrice"
                        price={true}
                      />
                    </div>
                    <div className="p-6 rounded-[16px] my-5 flex flex-col gap-6 ">
                      <h4 className="text-[16px] font-sora font-semibold text-[#2a2a2a]">
                        Additional Details
                      </h4>
                      <InputField
                        label="Batch Number (Optional)"
                        placeholder="Enter the batch number"
                        name="batchNumber"
                        type="number"
                        value={product?.batchNumber}
                        onChange={handleChange}
                      />
                      <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 pb-40">
                        <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                          Notes
                        </p>
                        <textarea
                          rows={10}
                          className="text-[#7b7b7b] text-xs p-4 border border-[#cccccc] rounded-2xl outline-none"
                          placeholder="Enter any additional notes on this product"
                          name="notes"
                          value={product?.notes}
                          onChange={handleChange}></textarea>
                      </div>
                    </div>
                    <div className=" fixed bottom-0 left-0 w-full   flex justify-around z-50 bg-white py-2.5 p-2  md:p-5">
                      <div className="flex w-full px-7.5 gap-2.5">
                        <button
                          onClick={onClose}
                          className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2">
                          Back
                        </button>
                        <button
                          type="submit"
                          className="bg-[#4f4cd8] w-full text-[#fcfcfc] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 h-[49px] rounded-2xl cursor-pointer flex-2">
                          Save & Preview
                        </button>
                      </div>
                    </div>
                  </div>
                </form>
              ) : (
                <PreviewEditProduct
                  productDetails={previewData}
                  coverImageId={coverImageId}
                  onBack={handleBack}
                  productId={viewingId}
                  refetch={refetch}
                  onClose={onClose}
                  productRefetch={productRefetch}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
export default EditProduct;
