import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import WebSettings from '../WebSettings';

// Mock the auth store
vi.mock('../../../store/auth', () => ({
  useUserAuthStore: vi.fn(() => ({
    business: {
      business_name: 'Test Business',
      business_phone: '+1234567890',
      business_image: 'test-image.jpg'
    },
    user: {
      userEmail: '<EMAIL>'
    }
  }))
}));

// Mock the template components
vi.mock('../DefaultTemplate', () => ({
  default: ({ primaryColor }: { primaryColor: string }) => (
    <div data-testid="default-template" data-primary-color={primaryColor}>
      Default Template
    </div>
  )
}));

vi.mock('../SecoundTemplate', () => ({
  default: ({ primaryColor }: { primaryColor: string }) => (
    <div data-testid="second-template" data-primary-color={primaryColor}>
      Second Template
    </div>
  )
}));

const renderWebSettings = () => {
  return render(
    <BrowserRouter>
      <WebSettings />
    </BrowserRouter>
  );
};

describe('WebSettings Template and Color Selection', () => {
  test('should render default template initially', () => {
    renderWebSettings();
    
    // Click to expand template section
    const templateSection = screen.getByText('Website Template');
    fireEvent.click(templateSection);
    
    // Default template should be rendered initially
    expect(screen.getByTestId('default-template')).toBeInTheDocument();
  });

  test('should switch to second template when selected', () => {
    renderWebSettings();
    
    // Click to expand template section
    const templateSection = screen.getByText('Website Template');
    fireEvent.click(templateSection);
    
    // Find and click the second template
    const templateOptions = screen.getAllByRole('img', { name: /template preview/i });
    fireEvent.click(templateOptions[1]); // Click second template
    
    // Second template should now be rendered
    expect(screen.getByTestId('second-template')).toBeInTheDocument();
    expect(screen.queryByTestId('default-template')).not.toBeInTheDocument();
  });

  test('should update primary color when color is selected', () => {
    renderWebSettings();
    
    // Click to expand template section
    const templateSection = screen.getByText('Website Template');
    fireEvent.click(templateSection);
    
    // Find color picker elements
    const colorElements = screen.getAllByRole('generic').filter(el => 
      el.style.backgroundColor && el.classList.contains('cursor-pointer')
    );
    
    // Click on a different color (orange)
    const orangeColor = colorElements.find(el => 
      el.style.backgroundColor === 'rgb(255, 153, 0)' // #FF9900
    );
    
    if (orangeColor) {
      fireEvent.click(orangeColor);
      
      // Check if the template receives the new color
      const template = screen.getByTestId('default-template');
      expect(template).toHaveAttribute('data-primary-color', '#FF9900');
    }
  });

  test('should show correct template with updated color', () => {
    renderWebSettings();
    
    // Click to expand template section
    const templateSection = screen.getByText('Website Template');
    fireEvent.click(templateSection);
    
    // Switch to second template
    const templateOptions = screen.getAllByRole('img', { name: /template preview/i });
    fireEvent.click(templateOptions[1]);
    
    // Change color
    const colorElements = screen.getAllByRole('generic').filter(el => 
      el.style.backgroundColor && el.classList.contains('cursor-pointer')
    );
    
    const greenColor = colorElements.find(el => 
      el.style.backgroundColor === 'rgb(40, 167, 69)' // #28A745
    );
    
    if (greenColor) {
      fireEvent.click(greenColor);
      
      // Check if the second template receives the new color
      const template = screen.getByTestId('second-template');
      expect(template).toHaveAttribute('data-primary-color', '#28A745');
    }
  });
});
