/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useRef } from 'react';

interface ModalProps {
  productId: any;
  position: { top: number };
  onClose: () => void;
  onViewProduct: (row: any) => void;
  onAdjustProduct: (row: any) => void;
  onRestockProduct: (row: any) => void;
}

const TrackstockDropdown: React.FC<ModalProps> = ({
  productId,
  position,
  onClose,
  onViewProduct,
  onAdjustProduct,
  onRestockProduct,
}) => {
  const modalRef = useRef<HTMLDivElement | null>(null);

  const handleOverlayClick = (event: any) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      onClose();
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleOverlayClick);
    return () => {
      document.removeEventListener('mousedown', handleOverlayClick);
    };
  }, []);

  return (
    <div
      ref={modalRef}
      className="absolute bg-white rounded-2xl font-sora border border-gray-300 shadow-lg  w-[190px]   lg:left-[80%] "
      style={{ top: `${position.top}px` }}>
      <div className="cursor-pointer">
        <p
          onClick={() => onViewProduct(productId)}
          className="py-2 px-4 text-[#5B5B5B]  rounded-t-2xl font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          View Details
        </p>
        <p
          onClick={() => onAdjustProduct(productId)}
          className="py-2 px-4 text-[#5B5B5B] font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          Adjust
        </p>
        <p
          onClick={() => onRestockProduct(productId)}
          className="py-2 px-4 text-[#5B5B5B] font-normal rounded-b-2xl hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          Restock
        </p>
      </div>
    </div>
  );
};

export default TrackstockDropdown;
