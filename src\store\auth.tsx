import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

type UserAuthStore = {
  user: {
    firstName: string;
    lastName: string;
    fullName: string;
    userEmail: string;
    vendorId: string;
  };
  business: {
    business_name: string;
    business_image: string;
    business_type: string;
    business_phone: string;
    business_website: string;
    business_address: string;
    business_city: string;
    business_state: string;
    business_zip_code: string;
    subdomain: string;
    website_status: string;
    website_status_by_admin: string;
    social_facebook:string;
    social_instagram:string;
    social_twitter:string;
    social_tiktok:string;
  };
  bank: {
    account_number?: string;
    bank_name?: string;
    account_name?: string;
    bank_code?: string;
  };

  newAccount: boolean;
  businessSocialMedia:boolean;
  userStatus:string;
  businessWebsite:string;

  token: string;
};

type UserAuthStoreAction = {
  updateUser: (data: UserAuthStore["user"]) => void;
  updateAccount: (accountData: boolean) => void;
  updateBusinessSocialMedia: (businessSocialMedia: boolean) => void;
  updateBusiness: (businessData: UserAuthStore["business"]) => void;
  updateBank: (bankData: UserAuthStore["bank"]) => void;
  updateToken: (token: string) => void;
  updateUserStatus: (userStatus: string) => void;
  updateBusinessWebsite: (businessWebsite: string) => void;
  clearToken: () => void;
  resetStore: () => void;
};

const initialAuthStore: UserAuthStore = {
  user: {
    firstName: "",
    lastName: "",
    fullName: "",
    userEmail: "",
    vendorId: "",
  },
  business: {
    business_name: "",
    business_image: "",
    business_type: "",
    business_phone: "",
    business_website: "",
    business_address: "",
    business_city: "",
    business_state: "",
    business_zip_code: "",
    subdomain: "",
    website_status: "",
    website_status_by_admin: "",
    social_facebook:"",
    social_instagram:"",
    social_tiktok:"",
    social_twitter:""
  },
  bank: {
    account_name: "",
    bank_name: "",
    account_number: "",
    bank_code: "",
  },
  newAccount: false,
  userStatus: "",
  businessWebsite: "",
  token: "",
  businessSocialMedia: false,
};

export const useUserAuthStore = create<UserAuthStore & UserAuthStoreAction>()(
  persist(
    (set) => ({
      ...initialAuthStore,
      updateUser: (data: UserAuthStore["user"]) => {
        set(() => ({
          user: data,
        }));
      },
      updateBusiness: (businessData: UserAuthStore["business"]) => {
        set(() => ({
          business: businessData,
        }));
      },
      updateBank: (bankData: UserAuthStore["bank"]) => {
        set(() => ({
          bank: bankData,
        }));
      },
      updateAccount: (accountData: boolean) => {
        set(() => ({ newAccount: accountData }));
      },
      updateBusinessSocialMedia: (businessSocialMedia: boolean) => {
        set(() => ({
          businessSocialMedia,
        }));
      },
      updateToken: (token: string) => {
        set(() => ({
          token,
        }));
      },
      updateUserStatus: (userStatus: string) => {
        set(() => ({
          userStatus,
        }));
      },
      updateBusinessWebsite: (businessWebsite: string) => {
        set(() => ({
          businessWebsite,
        }));
      },
      clearToken: () => {
        set(() => ({
          token: "",
        }));
      },
      resetStore: () => {
        console.log("Resetting store");
        set(() => initialAuthStore); // Corrected this line
      },
    }),
    {
      name: "user-auth-store",
      storage: createJSONStorage(() => localStorage),
    }
  )
);
