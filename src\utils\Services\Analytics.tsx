import KatajereAPI from '../KatajereAPI';


export const analyticsServices = {
    getAnalytics: async (vendorId: string ,startDate:string | undefined,endDate:string|undefined) => {
        const data = await KatajereAPI().get(`/analytics/analysis?vendorId=${vendorId}&startDate=${startDate}&endDate=${endDate}`)
        return data.data;
    },
    getInsight: async (vendorId: string) => {
        const data = await KatajereAPI().get(`/analytics/get-insight?vendorId=${vendorId}`)
        return data.data;
    },
};