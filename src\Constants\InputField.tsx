interface InputFieldProps {
  id?: string;
  max?:string | number;
  min?:string | number;
  label?: string;
  placeholder: string;
  type?: string;
  name?: string;
  value?: string | number;
  defaultValue?: string;
  readOnly?: boolean;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  onInput?:React.ChangeEventHandler<HTMLInputElement>;
  onKeyDown?: React.KeyboardEventHandler<HTMLInputElement>; // Optional onKeyDown prop
  className?: string;
  price?: boolean;
}

const InputField: React.FC<InputFieldProps> = ({
  id,
  label,
  placeholder,
  type = 'text',
  name,
  value,
  readOnly,
  onChange,
  min,
  onInput,
  onKeyDown, // Accept optional onKeyDown
  className,
  defaultValue,
  price,
  max
}) => {
  return (
    <div className="p-0 flex flex-col gap-2.5 relative">
      {label && (
        <p className="text-xs font-sora font-normal text-[#5b5b5b]">{label}</p>
      )}
      <div className="relative w-full">
        {price && (
          <span className="absolute top-1/2 left-4 transform -translate-y-1/2 text-[#5b5b5b] font-sora text-sm">
            ₦
          </span>
        )}
        <input
          id={id}
          
          type={type}
          placeholder={placeholder}
          defaultValue={defaultValue}
          value={value}
          name={name}
          max={max}
          min={min}
          readOnly={readOnly}
          onInput={onInput}
          onChange={onChange}
          onKeyDown={onKeyDown} // Use optional onKeyDown handler
          className={`text-[#5b5b5b] focus:bg-[#E6E5F9] hover:border-primary-purple500 duration-300 w-full text-sm font-sora font-normal h-[48px] ${
            price ? 'pl-8' : 'px-4'
          } border border-[#cccccc] rounded-2xl outline-none ${
            readOnly ? 'bg-[#e6e5f9] border-none' : ''
          } ${className}`}
        />
      </div>
    </div>
  );
};

export default InputField;
