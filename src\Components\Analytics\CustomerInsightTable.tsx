/* eslint-disable @typescript-eslint/no-explicit-any */
import { InsightTable } from '../../utils/InsightTable';

const CustomerInsightTable = ({ data }: any) => {
  console.log(data)
  const headers = [
    {
      title: 'Customer Name',
      key: 'customerName',
      render: (row: any) => (
        <p className="text-[#5A5A5A] text-sm font-normal">{row.customer_name}</p>
      ),
    },
    {
      title: 'Segment',
      key: 'segment',
      render: (row: any) => (
        <p className="text-[#5A5A5A] text-sm font-normal">{row.segment}</p>
      ),
    },
    {
      title: 'Total',
      key: 'total',
      render: (row: any) => (
        <p className="text-[#FFC107] text-xs font-normal !font-inter">
          {row.total_spent !== undefined ? row.total_spent : row.total_purchased}
        </p>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      render: (row: any) => (
        <button
          type="button"
          className="text-[#2A2A2A] font-normal text-xs px-4 border border-[#4F4CD8] py-1 rounded-3xl"
        >
          {row.action.displayButton}
        </button>
      ),
    },
  ];

  
  const rows = [
    ...(data?.insights?.customerSegments?.highSpenders || []).map((item: any) => ({
      ...item,
      segment: 'High Spender'
    })),
    ...(data?.insights?.customerSegments?.frequentBuyers || []).map((item: any) => ({
      ...item,
      segment: 'Frequent Buyer'
    })),
    ...(data?.insights?.customerSegments?.occasionalBuyers || []).map((item: any) => ({
      ...item,
      segment: 'Occasional Buyer'
    })),
  ];


  return (
    <div>
      <h4 className="text-[#3A3A3A] font-semibold text-lg mb-4">
        Customer Insights
      </h4>
      <p className="text-[#8B8B8B] font-normal text-base mb-6">
        Take immediate action to boost customer retention, engagement, and
        sales.
      </p>
      <div className="bg-[#F5F5F5] py-6 px-4 rounded-3xl mb-6 ">
        <div className="overflow-x-auto max-w-[240px] sm:max-w-full">
          <InsightTable rows={rows} headers={headers} showHead={true} />
        </div>
      </div>
    </div>
  );
};
export default CustomerInsightTable;
