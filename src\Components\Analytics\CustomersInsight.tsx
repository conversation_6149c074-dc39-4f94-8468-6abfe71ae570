/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Arrow<PERSON>eft,
  ArrowRight,
  ArrowUp,
  Profile2User,
  Refresh,
  ShoppingCart,
} from 'iconsax-react';
import DateHead from './DateHead';
import Progressbar from '../../Constants/Progressbar';
import AnalyticButton from '../../Constants/AnalyticButton';
import AnalyticCard from '../../Constants/AnalyticCard';
import { useEffect, useState } from 'react';
import CLVModal from './CLVModal';

const CustomersInsight = ({
  data,
  isError,
  isLoading,
  formattedEndDate,
  formattedStartDate,
}: any) => {
  const [clvModal, setClvModal] = useState<boolean>(false);
  const handleClvModal = () => {
    setClvModal((prev) => !prev);
  };

  useEffect(() => {
    document.title = 'Customer Insights - Analytics';
  });
  const counts = Object.values(data?.customerSegments || {}) as number[];

  // Calculate the total count, ensure <PERSON><PERSON> understands it's a number
  const totalCount: number = counts.reduce((acc: any, count) => acc + count, 0);
  return (
    <div className="font-sora">
      <DateHead />
      {isError ? (
        <h2 className="flex justify-center items-center py-16">
          An Error Occurred, Please Refresh
        </h2>
      ) : (
        <>
          {' '}
          <div className="flex flex-col lg:flex-row gap-6">
            <div className="w-full max-w-[660px]">
              <div className="border rounded-xl py-4 px-6">
                <div className="flex gap-4 items-center">
                  {isLoading ? (
                    <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                  ) : (
                    <div className="bg-[#E8F2FB] p-1.5 rounded-full">
                      <Profile2User size="16" color="#1976D2" variant="Bold" />
                    </div>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                  ) : (
                    <p className="font-semibold text-lg text-[#2A2A2A]">
                      Customer Segmentation
                    </p>
                  )}
                </div>
                <div className="flex my-6 justify-between">
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <p className="font-semibold text-[#1E1B39] text-2xl">
                      {data?.totalCustomers} Customers
                    </p>
                  )}
                  {/* <p
                    className={`flex items-center rounded-3xl  text-[10px] p-1 ${
                      data?.totalSalesChange < 0
                        ? 'border-[#DC3545] border'
                        : 'border-[#AEEBBC] border'
                    } `}>
                    <span
                      className={`${
                        data?.totalSalesChange < 0
                          ? 'text-[#DC3545]'
                          : 'text-[#28A745]'
                      }`}>
                      {data?.totalSalesChange}
                    </span>
                    {data?.totalSalesChange < 0 ? (
                      <ArrowDown className="text-[#DC3545]" size="12" />
                    ) : (
                      <ArrowUp className="text-[#28A745]" size="12" />
                    )}
                  </p> */}
                  {isLoading ? (
                    <div className="animate-pulse bg-slate-200 rounded-3xl w-[40px] "></div>
                  ) : (
                    <p className="flex items-center rounded-3xl text-[#28A745] text-[10px] p-1 border border-[#AEEBBC]">
                      <span> 16%</span> <ArrowUp size="12" />
                    </p>
                  )}
                </div>
                {Object.entries(data?.customerSegments).map(
                  ([segment, count]) => {
                    const segmentCount = count as number;
                    return (
                      <>
                        <Progressbar
                          key={segment}
                          itemName={segment}
                          level={segmentCount}
                          units="customers"
                          progressPercentage={(segmentCount / totalCount) * 100}
                          barColor={
                            segment === 'Frequent Buyers'
                              ? '#9A99E9'
                              : segment === 'High Spenders'
                              ? '#B4B2EE'
                              : segment === 'Occasional Buyers'
                              ? '#CDCCF4'
                              : '#E6E5F9'
                          }
                          isLoading={isLoading}
                        />
                      </>
                    );
                  }
                )}

                {/* <Progressbar
              itemName="Frequent Buyers"
              level={data?.customerSegments['Frequent Buyers']}
              units="customers"
              progressPercentage={data?.customerSegments['Frequent Buyers']}
              barColor="#9A99E9"
            />
            <Progressbar
              itemName="High Spenders "
              level={data?.customerSegments['High Spenders']}
              units="customers"
              progressPercentage={data?.customerSegments['High Spenders']}
              barColor="#B4B2EE"
            />
            <Progressbar
              itemName="Occasional  Buyers"
              level={data?.customerSegments['Occasional Buyers']}
              units="customers"
              progressPercentage={data?.customerSegments['Occasional Buyers']}
              barColor="#CDCCF4"
            />
            <Progressbar
              itemName="Inactive Customers "
              level={data?.customerSegments['Inactive Customers']}
              units="customers"
              progressPercentage={data?.customerSegments['Inactive Customers']}
              barColor="#E6E5F9"
            /> */}
              </div>
            </div>

            <div className="w-full">
              <div className="border rounded-xl py-4 px-6">
                <div className="flex gap-4 items-center">
                  {isLoading ? (
                    <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                  ) : (
                    <div className="bg-[#E8F2FB] p-1.5 rounded-full">
                      <Profile2User size="16" color="#1976D2" variant="Bold" />
                    </div>
                  )}
                  {isLoading ? (
                    <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                  ) : (
                    <p className="font-semibold text-lg text-[#2A2A2A]">
                      Customer Lifetime Value (CLV)
                    </p>
                  )}{' '}
                </div>
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px] mt-6"></div>
                ) : (
                  <p className="font-normal text-sm text-[#7B7B7B] mt-6">
                    See your CLV analytics here
                  </p>
                )}

                {data?.customerLifetimeValueFullList
                  ?.slice(0, 3)
                  ?.map((data: any, index: any) => {
                    return (
                      <div className="mt-6" key={index}>
                        {isLoading ? (
                          <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[120px] mb-3"></div>
                        ) : (
                          <h2 className="text-[#2A2A2A] font-bold text-sm">
                            {data.customerName}
                          </h2>
                        )}

                        <div className="flex flex-col sm:flex-row gap-4">
                          <div className="flex items-center gap-1">
                            {isLoading ? (
                              <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[70px] "></div>
                            ) : (
                              <p className="text-[#8B8B8B] text-sm font-normal">
                                Clv
                              </p>
                            )}

                            <span
                              className={`h-1.5 w-1.5 block rounded-full bg-[#D9D9D9]`}></span>
                            {isLoading ? (
                              <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[70px] "></div>
                            ) : (
                              <p className="text-[#5B5B5B]">{data.clv}</p>
                            )}
                          </div>
                          <div className="flex items-center gap-1">
                            {isLoading ? (
                              <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[90px] "></div>
                            ) : (
                              <p className="text-[#8B8B8B] text-sm font-normal">
                                Segmentation
                              </p>
                            )}

                            <span
                              className={`h-1.5 w-1.5 block rounded-full bg-[#D9D9D9]`}></span>
                            {isLoading ? (
                              <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[90px] "></div>
                            ) : (
                              <p className="text-[#5B5B5B]">
                                {data.segmentation}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[36px] max-w-[100px] mt-4"></div>
                ) : (
                  <AnalyticButton
                    title="View More"
                    image={ArrowRight}
                    onClick={handleClvModal}
                  />
                )}
              </div>
              <AnalyticCard
                title="Churn Rate"
                churnRate={data?.ChurnRate}
                percentageChange={data?.ChurnRateChange || 0}
                description={`Percentage of customers you lost from ${formattedStartDate} to ${formattedEndDate}`}
                hasMaxWidth={false}
                icon={<ArrowLeft size="16" color="#FF9900" variant="Bold" />}
                backgroundColor="#FFF2DF"
                isLoading={isLoading}
                isPositive={data?.ChurnRateChange < 0}
              />
            </div>
          </div>
          <div className="flex flex-col md:flex-row justify-center gap-6 ">
            <AnalyticCard
              title="Average Order Value (AOV)"
              churnRate="0"
              percentageChange={data?.averageOrderValue?.percentageChange || 0}
              description={`Average order value of  your customers from  ${formattedStartDate} to ${formattedEndDate}`}
              isPositive={data?.averageOrderValue?.percentageChange < 0}
              hasMaxWidth={true}
              icon={<ShoppingCart size="16" color="#00C7BE" variant="Bold" />}
              backgroundColor="#E9FBFA"
              isLoading={isLoading}
            />
            <AnalyticCard
              title="Customer Retention Rate"
              churnRate={data?.RetentionRate}
              percentageChange={data?.RetentionRateChange || 0}
              description={`Your customers retention rate from  ${formattedStartDate} to ${formattedEndDate}`}
              isPositive={data?.RetentionRateChange < 0}
              hasMaxWidth={true}
              icon={<Refresh size="16" color="#4F4CD8" variant="Bold" />}
              backgroundColor="#F5EAFA"
              isLoading={isLoading}
            />
          </div>
        </>
      )}

      {clvModal && (
        <CLVModal isOpen={clvModal} onClose={handleClvModal} data={data} />
      )}
    </div>
  );
};
export default CustomersInsight;
