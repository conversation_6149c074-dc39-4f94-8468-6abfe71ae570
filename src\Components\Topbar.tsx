/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useState } from "react";
import { Notification } from "iconsax-react";
import { useUserAuthStore } from "../store/auth";
import NotificationModal from "./NotificationModal";
import { Notifications } from "../utils/Services/Notification";
import { useQuery } from "react-query";
import { toast } from "react-toastify";
import { Link } from "react-router-dom";
import { RouteNames } from "../utils/RouteNames";

import { IoGlobeOutline } from "react-icons/io5";

const Topbar = () => {
  const [currentDate, setCurrentDate] = useState("");
  const user = useUserAuthStore((state) => state.user);
  const [isNotificationOpen, setIsNotificationOpen] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState("all");

  const { data, refetch } = useQuery(
    ["notifications", user.vendorId],
    () => Notifications.getNotification(user.vendorId),
    {
      enabled: !!user.vendorId,
      onSuccess: () => {},
      onError: (error: any) => {
        toast.error("Failed to fetch notifications");
        console.log("notification error:", error);
      },
    }
  );

  const NotificationData = data?.data;
  const unread = data?.data?.unreadCount;

  useEffect(() => {
    const today = new Date();
    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    const formattedDate = today.toLocaleDateString("en-US", options);
    setCurrentDate(formattedDate);
    if (isNotificationOpen) {
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    return () => {
      document.body.classList.remove("no-scroll");
    };
  }, [isNotificationOpen]);

  const handleNotification = () => {
    setIsNotificationOpen((prev) => !prev);
    setActiveTab("all");
  };
  return (
    <div className="z-20 bg-primary-baseWhite fixed left-[250px] right-0 justify-between border-b-[1px] px-[20px] border-primary-neutral200 flex items-center h-[74px]">
      <div>
        <p className="text-primary-neutralt1 font-sora text-[18px] capitalize">
          Welcome {user.firstName} 👋
        </p>
      </div>

      <div className="flex items-center">
        <div className="flex items-center gap-10">
          <Link to={RouteNames.websiteSettings} className="flex cursor-pointer text-[14px] font-sora font-semibold text-[#2a2a2a] items-center gap-3 border-[1px] border-[#4f4cd8] px-4 py-2 rounded-[16px]">
            {" "}
            <IoGlobeOutline /> <span>Website Settings</span>
          </Link>
          <div
            onClick={handleNotification}
            className="bg-[#e6e5f9] cursor-pointer relative border-[1px] border-primary-purple p-[8px] rounded-[16px] mr-[15px]"
          >
            <Notification size={24} variant="Outline" />
            {NotificationData?.unreadCount > 0 && (
              <p className="absolute w-[16px] h-[16px] text-[10px] -top-[8px] -right-[3px] rounded-full text-center bg-[#DC3545] text-primary-baseWhite">
                {NotificationData.unreadCount}
              </p>
            )}
          </div>
        </div>

        <p className="text-[14px] text-primary-neutral1000 font-sora">
          {currentDate ? currentDate : "Loading date..."}
        </p>
      </div>
      {isNotificationOpen && (
        <NotificationModal
          isOpen={isNotificationOpen}
          onClose={handleNotification}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          data={NotificationData?.notifications}
          unreadCount={unread}
          refetch={refetch}
        />
      )}
    </div>
  );
};

export default Topbar;
