import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BiShoppingBag } from "react-icons/bi";
import { products } from "../../utils/MockData";
import {
  ArrowRight,
  CallCalling,
  Facebook,
  Instagram,
  SearchNormal1,
  ShoppingBag,
  Sms,
  Sort,
  Whatsapp,
} from "iconsax-react";

// import logo from "../../assets/logoWhite.svg";
const SecoundTemplate = ({ primaryColor, business, user }: any) => {
  return (
    <div>
      {/* header */}
      <div
        className={`flex bg-[${primaryColor}] py-2 gap-2 md:gap-0 flex-col md:flex-row md:items-center justify-between px-5 md:px-4 font-sora`}
      >
        <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
          <div className="flex items-center gap-2">
            <CallCalling size="6" color="#FCFCFC" />
            <span className="text-[10px] text-white">
              {business?.business_phone}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Sms size="6" color="#FCFCFC" />
            <span className="text-[10px] text-white">{user.userEmail}</span>
          </div>
        </div>

        <div className="w-full md:w-auto mx-auto overflow-hidden">
          <div className="marquee">
            <span className="text-[10px] text-white font-bold mx-4">
              Holiday Mega Sales 25% OFF
            </span>
          </div>
        </div>

        <div className="flex gap-4 items-center">
          <Instagram size={16} color="white" />

          <BiLogoTiktok size={16} color="white" />

          <BiLogoTwitter size={16} color="white" />

          <Whatsapp size={16} color="white" />

          <Facebook size={16} variant="Outline" color="white" />
        </div>
      </div>
      {/* second */}

      <div className="flex  md:w-[737px] relative  w-full md:mt-2 md:shadow-md md:shadow-gray-100 md:rounded-full md:mx-auto bg-[#ffffff] flex-wrap pt-2 items-center justify-between pb-3  md:py-[5px] px-4 md:px-10  font-sora">
        <div className="flex items-center w-full md:w-fit  justify-between gap-6 mb-2 md:mb-0">
          <div className="flex flex-row sm:flex-row items-center font-semibold gap-2 sm:gap-4 text-[#1A1A1A]">
            <img
              alt="businessLogo"
              className="rounded-full bg-contain w-[50px] h-[50px] py-2 px-2 text-xs "
              src={business?.business_image}
              width={50}
              height={50}
            />
            <p className="text-sm">{business?.business_name}</p>
          </div>

          <div className="md:hidden">
            <button
              type="button"
              className="text-black bg-[#f5f5f5] flex gap-2 items-center justify-center py-2.5 px-4 rounded-full"
            >
              <BiShoppingBag size="20" color="black" />
              <span className="text-[16px]">Cart</span>
              <span
                className={`bg-[${primaryColor}] text-[10px] rounded-full text-[#FCFCFC] px-1.5 py-0.5`}
              >
                3
              </span>
            </button>
          </div>
        </div>

        <div className="w-full md:w-[338px]">
          <div className="text-[#9B9B9B] w-full  gap-2 rounded-full flex items-center border-[1px] border-[#CCCCCC] px-4  flex-1">
            <SearchNormal1 size="20" color="#7b7b7b" />

            <input
              type="text"
              placeholder="What are you looking for ?"
              className="w-full py-3 bg-transparent outline-none  text-[14px]"
            />
          </div>
        </div>

        <div className="md:flex flex-row hidden items-center gap-4 w-full md:w-auto">
          <button
            type="button"
            className="text-black bg-[#f5f5f5] flex gap-2 items-center justify-center py-2.5 px-4 rounded-full"
          >
            <BiShoppingBag size="20" color="black" />
            <span className="text-[16px]">Cart</span>
            <span
              className={`bg-[${primaryColor}] text-[10px] rounded-full text-[#FCFCFC] px-1.5 py-0.5`}
            >
              3
            </span>
          </button>
        </div>
      </div>

      {/* carousel */}
      <div className="w-full pt-[20px] md:pt-[20px]  px-4 mx-auto">
        <div className="overflow-hidden relative rounded-[12px]">
          <div className="flex">
            <div
              className={`w-full  px-2 md:px-3 bg-[${primaryColor}] items-center  cursor-pointer flex flex-row flex-shrink-0 overflow-hidden rounded-[12px]`}
            >
              {/* Text Section */}
              <div
                className={`w-1/2 flex flex-col text-white justify-center items-start md:px-4 py-4 sm:py-6 md:py-12 h-[200px] sm:h-[250px] md:h-[500px] `}
              >
                <h2 className="text-[16px] w-full truncate  font-sora font-medium mb-2 md:mb-4 leading-snug">
                  {products[0].name}
                </h2>
                <p className="text-sm hidden md:block text-white sm:text-base mb-2 md:mb-4 font-sora leading-relaxed">
                  {products[0].description}
                </p>
                <p className="text-lg sm:text-xl mb-2 md:mb-6 font-sora leading-relaxed">
                  {products[0].price
                    ? `₦ ${Number(products[0].price).toLocaleString()}`
                    : ""}
                </p>
                <div
                  className={`flex  text-[${primaryColor}] text-[12px]  md:text-[16px] items-center gap-2  bg-white font-sora  rounded-full px-[8px] py-[6px] md:py-[11px] md:px-6`}
                >
                  Buy Now <ArrowRight size={24} color="#4f4cd8" />
                </div>
              </div>

              {/* Image Section */}
              <div className="w-1/2 relative h-[180px] sm:h-[200px] md:h-[480px]">
                <img
                  src={products[0].image}
                  alt={products[0].name || "Banner image"}
                  className="w-full h-full object-cover rounded-[16px]"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Dots Indicator */}
        <div className="mt-4 flex justify-center gap-2">
          {products?.map((_, idx) => (
            <button
              key={idx}
              className={`h-1 rounded-full focus:outline-none ${
                idx === 1
                  ? `bg-[${primaryColor}] w-[50px]`
                  : "bg-gray-300 w-[12px]"
              }`}
            />
          ))}
        </div>
      </div>

      {/* filter */}
      <div className="flex flex-col md:flex-row items-center border-b border-[#CCCCCC] justify-between pt-4 pb-4 gap-4">
        <div className="text-[#4F4CD8] flex items-center outline-none text-[16px] font-sora font-normal w-full md:max-w-[358px] px-2 h-[35px] border border-[#B4B2EE] rounded-xl">
          All Products
        </div>
        <div className="flex items-center bg-[#1A1A1A] rounded-xl px-4 h-[35px] gap-2 w-full md:w-auto">
          <Sort size="20" color="#FCFCFC" />
          <p className="text-white text-sm">Sort By</p>
        </div>
      </div>
      {/* product list */}
      <div className="grid w-full grid-cols-2 gap-6  md:grid-cols-3  ">
        {products.map((p: any) => (
          <div key={p.id}>
            <div>
              <div className=" pt-[5px]  bg-[#f5f5f5] flex-col flex items-center justify-center  px-0 py-0">
                {/* Product Image */}

                <img
                  src={p.image}
                  alt="product"
                  className="md:w-[200px] h-[150px] md:h-[200px] object-contain mx-auto"
                  width={200}
                  height={200}
                />
              </div>
              <p className="text-[13px] truncate md:text-[13px] my-2 text-[#2A2A2A] font-normal">
                {p?.name}
              </p>
              <div className="flex justify-between text-sm md:text-[14px]">
                <span className="text-[#5B5B5B]">{p.category}</span>
                <span className="text-black font-semibold">
                  {" "}
                  ₦ {p.price?.toLocaleString()}
                </span>
              </div>
            </div>

            <button
              className={`cursor-pointer text-[14px] hover:bg-[${primaryColor}] hover:text-[#F5F5F5] border-[${primaryColor}] transition-all duration-500 p-2  md:rounded-2xl mt-1 flex items-center justify-between w-full border 
                            `}
            >
              Add To Cart <ShoppingBag color={primaryColor} size={16} />
            </button>
          </div>
        ))}
      </div>
      {/* footer */}
      {/* <div className="mt-5 bg-[#1A1A1A]  overflow-hidden w-full md:mx-auto md:rounded-[24px] font-sora py-[10px] px-[4px]  md:py-[20px] md:px-[20px] ">
        <div className="w-full flex flex-col gap-3 md:gap-3">
          <div className="flex justify-center">
            <div className="flex items-center font-semibold gap-2">
              <img
                alt="logo"
                className="rounded-2xl bg-contain w-[50px] h-[50px] text-xs "
                src={
                  business.business_image || "../../../public/huxxlelogo.svg"
                }
                width={50}
                height={50}
              />
              <p className="text-sm text-[#fcfcfc]">
                {business?.business_name}
              </p>
            </div>
          </div>
          <div className="flex justify-between items-center flex-col md:flex-row w-full">
            <div className="flex   gap-2 md:gap-4">
              <div className="flex  items-center gap-2">
                <p className="border-blue-600 border-[1px] p-2 rounded-full">
                  {" "}
                  <CallCalling size="20" color="white" />
                </p>

                <span className=" text-[12px]  text-white">
                  {business?.business_phone}
                </span>
              </div>
              <div className="flex items-center gap-2 my-6">
                <p className="border-blue-600 border-[1px] p-2 rounded-full">
                  <Sms size="20" color="#FCFCFC" />
                </p>

                <span className="text-[12px] text-white">
                  {user?.userEmail}
                </span>
              </div>
            </div>

            <div className="flex  gap-4 items-center">
              <Instagram size={16} color="white" />

              <BiLogoTiktok size={16} color="white" />

              <BiLogoTwitter size={16} color="white" />

              <Whatsapp size={16} color="white" />

              <Facebook size={16} variant="Outline" color="white" />
            </div>
          </div>

          <div className="h-[2px] w-full bg-gradient-to-t from-[#1252A5] via-[#CAE1FF] to-[#1252A5]"></div>
          <div className="flex flex-col gap-4 justify-between w-full md:flex-row items-center">
            <div className="flex gap-5 items-center">
              <p className="text-[#9B9B9B] text-sm text-center sm:text-left">
                Terms of Use Policy
              </p>
              <p className="text-[#9B9B9B] text-sm text-center sm:text-left">
                Refund Policy
              </p>
            </div>
            <div className="flex items-center gap-4">
              <p className="text-[14px] font-sora text-[#cccccc]">Powered by</p>
              <img src={logo} alt="huxxle logo" />
            </div>
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default SecoundTemplate;
