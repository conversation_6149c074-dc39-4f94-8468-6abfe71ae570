/* eslint-disable @typescript-eslint/no-explicit-any */

import { ArrowSwapVertical, Refresh } from "iconsax-react";
import AlertBox from "../../../Constants/AlertBox";
import { useEffect, useState } from "react";
import FrequencyModal from "./FrequencyModal";
import ThresholdModal from "./ThresholdModal";
import { useMutation, useQuery} from "react-query";
import { inventoryServices } from "../../../utils/Services/InventoryServices";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../../store/auth";
import { useDateStore } from "../../../store/date";
import { StatsService } from "../../../utils/Services/StatsServices";
import { Link } from "react-router-dom";
import { RouteNames } from "../../../utils/RouteNames";

// Define the keys for the alert settings
type AlertSettingKeys =
  | "lowStockEnabled"
  | "outOfStockEnabled"
  | "highStockEnabled"
  | "sensitivityEnabled"
  |"emailEnabled"
  ;

// Define the alert settings state type using the union of keys
interface AlertSettings {
  lowStockEnabled: string;
  outOfStockEnabled: string;
  highStockEnabled: string;
  sensitivityEnabled: string;
  emailEnabled: string;
}

const Alert: React.FC = () => {
  const [frequency, setFrequency] = useState<boolean>(false);
  const [threshold, setThreshold] = useState<boolean>(false);
  const [emailNotify, setEmailNotify] = useState<boolean>(false)
  const handleFrequency = () => {
    setFrequency((prev) => !prev);
  };
  const handleThreshold = () => {
    setThreshold((prev) => !prev);
  };
 
  const user = useUserAuthStore((state) => state.user);

 

  
    const { startDates, endDates } = useDateStore();
    const formattedStartDate = startDates
      ? new Date(startDates).toISOString().split("T")[0]
      : undefined;
    const formattedEndDate = endDates
      ? new Date(endDates).toISOString().split("T")[0]
      : undefined;
    const {
      data: stat,
      refetch
    } = useQuery(
      ["overviewstats", user.vendorId, formattedStartDate, formattedEndDate],
      () =>
        StatsService.OverviewStats({
          vendorId: user.vendorId,
          startDate: formattedStartDate,
          endDate: formattedEndDate,
        }),
      {
        enabled: !!user.vendorId,
      }
      );
const data = stat?.data?.alerts
  const alertMutate = useMutation(inventoryServices.alertSettings);
  // const location = useLocation();
  // const { data } = location.state || {}; // Fallback if state is empty

  // State to track the settings of all the alert toggles
  const [alertSettings, setAlertSettings] = useState<AlertSettings>({
    lowStockEnabled: data?.lowStockEnabled || "No",
    outOfStockEnabled: data?.outOfStockEnabled || "No",
    highStockEnabled: data?.highStockEnabled || "No",
    sensitivityEnabled: data?.sensitivityEnabled || "No",
    emailEnabled: data?.emailEnabled || "No",
  });

  // Function to handle the toggle state change

  const handleToggle = (alertType: AlertSettingKeys, newState: boolean) => {
    setAlertSettings((prevSettings) => ({
      ...prevSettings,
      [alertType]: newState ? "Yes" : "No", // Update toggle state
    }));
  };

   useEffect(() => {
     if (data?.emailEnabled === "Yes") {
       setEmailNotify(true);
     }
   }, [data.emailEnabled]);
  
 const handlePreference = (name: AlertSettingKeys, value: string) => {
   setAlertSettings((prev) => ({
     ...prev,
     [name]: value, // Directly assign Yes/No
   }));

   if (name === "emailEnabled") {
     setEmailNotify(value === "Yes"); // Keep emailNotify in sync
   }
 };


  const handleSave = () => {
    const payload = {
      lowStockEnabled: alertSettings.lowStockEnabled,
      highStockEnabled: alertSettings.highStockEnabled,
      outOfStockEnabled: alertSettings.outOfStockEnabled,
      sensitivityEnabled: alertSettings.sensitivityEnabled,
      emailEnabled:alertSettings.emailEnabled,
      vendorId:user.vendorId
    };
    try {
      alertMutate.mutate(payload, {
        onSuccess: (response) => {
          refetch()
          toast.success(response?.data?.message);
          console.log(alertSettings);
        },

        onError: (error: any) => {
          toast.error(error?.response?.data?.error || error?.message);
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="md:mt-[70px] p-4 md:p-0">
      {/* header */}
      <div className="flex flex-col md:flex-row justify-between gap-2 font-sora ">
        <div className="">
          <h1 className="text-2xl font-bold m-0">Alert Settings</h1>
          <Link
            to={RouteNames.inventory}
            className="track-stock-subheader text-[#7b7b7b] text-sm cursor-pointer"
          >
            Inventory Management &gt;&gt;{" "}
            <span className="text-[#4f4cd8]">Alert Settings</span>
          </Link>
        </div>
        <div className="flex-col flex gap-3 md:gap-5 md:flex-row md:w-[400px] justify-between w-full items-center">
          <div className="w-full">
            <button
              onClick={handleFrequency}
              className=" border h-[49px] w-full justify-center border-gray-300 rounded-2xl px-3  text-base flex gap-2 items-center cursor-pointer bg-white font-bold md:mr-5"
            >
              <Refresh size={14} />
              Set Frequency
            </button>
          </div>
          <button
            onClick={handleThreshold}
            className="border h-[49px] w-full justify-center border-gray-300 rounded-2xl gap-2 px-3  text-base flex items-center cursor-pointer bg-[#4f4cd8] text-white font-bold"
          >
            <ArrowSwapVertical size={14} /> Set Threshold
          </button>
        </div>
      </div>

      {/* body */}
      <div className="flex-col space-y-5 lg:space-y-0 lg:flex lg:flex-row md:w-[1055px]  items-start  gap-4 my-[30px] md:my-[20px] ">
        <div className="py-4  min-h-[664px] px-6 rounded-2xl border-primary-neutral200 border-[1px] flex flex-col gap-[16px]">
          <h4 className="text-[18px] font-semibold font-sora text-primary-neutralt1">
            Alert Types
          </h4>
          <AlertBox
            Title="Low Stock Alerts"
            content="Get notified when the stock level of any product drops below a predefined threshold."
            nameAlert="lowStockEnabled"
            onToggle={handleToggle}
            isToggled={alertSettings.lowStockEnabled === "Yes"}
          />

          <AlertBox
            Title="Out of Stock Alerts"
            content="Get notified when a product is completely out of stock and they need action."
            nameAlert="outOfStockEnabled"
            onToggle={handleToggle}
            isToggled={alertSettings.outOfStockEnabled === "Yes"}
          />
          <AlertBox
            Title="High Stock Alerts"
            content="Get notified for when stock levels exceed a certain limit, to prevent overstocking."
            nameAlert="highStockEnabled"
            onToggle={handleToggle}
            isToggled={alertSettings.highStockEnabled === "Yes"}
          />

          <AlertBox
            Title="Stock Movement Alerts"
            content="Get notified when a product is completely out of stock and they need action."
            nameAlert="sensitivityEnabled"
            onToggle={handleToggle}
            isToggled={alertSettings.sensitivityEnabled === "Yes"}
          />
        </div>
        <div className="py-4  min-h-[664px] px-6 rounded-2xl border-primary-neutral200 border-[1px] flex flex-col gap-[16px]">
          <h4 className="text-[18px] font-semibold font-sora text-primary-neutralt1">
            Notification Preferences
          </h4>

          <div className="rounded-2xl flex flex-col gap-2 border-primary-neutral200 border-[1px] p-4 md:w-[420px]">
            <div className="flex justify-between items-center">
              <p className="text-primary-neutralt1 font-sora text-[16px] leadig-[25.6px]">
                Email Notifications
              </p>
              <input
                type="checkbox"
                className="w-5 h-5 accent-primary-purple rounded-full"
                checked={emailNotify}
                name="emailEnabled"
                onChange={(e) => {
                  handlePreference(
                    "emailEnabled",
                    e.target.checked ? "Yes" : "No"
                  );
                }}
              />
            </div>
            <p className="text-[#9B9B9B] font-sora text-[14px] md:w-[414px]">
              Receive alerts directly in your inbox
            </p>
          </div>

          <div className="rounded-2xl flex flex-col gap-2 border-primary-neutral200 border-[1px] p-4 md:w-[420px]">
            <div className="flex justify-between items-center">
              <p className="text-primary-neutralt1 font-sora text-[16px] leadig-[25.6px]">
                Whatsapp Notifications
              </p>
              <input
                type="checkbox"
                className="w-5 h-5 accent-primary-purple cursor-not-allowed rounded-full"
                disabled={true}
              />
            </div>
            <p className="text-[#9B9B9B] font-sora text-[14px] md:w-[414px]">
              Get real-time updates via Whatsapp.
            </p>
          </div>

          <div className="rounded-2xl flex flex-col gap-2 border-primary-neutral200 border-[1px] p-4 md:w-[420px]">
            <div className="flex justify-between items-center">
              <p className="text-primary-neutralt1 font-sora text-[16px] leadig-[25.6px]">
                In-App Notifications
              </p>
              <input
                type="checkbox"
                className="w-5 h-5 cursor-not-allowed accent-primary-purple rounded-full"
                checked={true}
                disabled={true}
              />
            </div>
            <p className="text-[#9B9B9B] font-sora text-[14px] md:w-[414px]">
              View alerts within the dashboard.
            </p>
          </div>

          <button
            onClick={handleSave}
            disabled={alertMutate.isLoading}
            className={`${
              alertMutate.isLoading ? "opacity-50 cursor-auto" : ""
            } w-full text-[#fcfcfc] bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl cursor-pointer flex-2 `}
          >
            {alertMutate.isLoading ? "Saving Changes" : "Save Changes"}
          </button>
        </div>
      </div>
      {frequency && <FrequencyModal refetch={refetch} settings={data} isOpen closeModal={handleFrequency} />}
      {threshold && (
        <ThresholdModal
          refetch={refetch}
          settings={data}
          isOpen
          closeModal={handleThreshold}
        />
      )}
    </div>
  );
};

export default Alert;
