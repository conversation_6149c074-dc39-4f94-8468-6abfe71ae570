/* eslint-disable @typescript-eslint/no-explicit-any */
import KatajereAPI from '../KatajereAPI';

export const orderServices = {
  addCustomer: async (payload: {
    vendorId: string;
    userEmail: string;
    customerId: string;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
    billingAddress: string;
    billingCity: string;
    billingState: string;
    billingZipCode: string;
    // shippingAddress: string;
    // shippingCity: string;
    // shippingState: string;
    // shippingZipCode: string;
    country: string;
    saveAddress: string;
    socialMediaHandle: string;
    firstName: string;
    lastName: string;
  }) => {
    const data = await KatajereAPI().post(
      `/customers/create-customer`,
      payload
    );
    return data.data;
  },
  editCustomer: async (payload: {
    vendorId: string;
    userEmail: string;
    customerId: string;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
    billingAddress: string;
    billingCity: string;
    billingState: string;
    billingZipCode: string;
    country: string;
    saveAddress: string;
    socialMediaHandle: string;
    firstName: string;
    lastName: string;
  }) => {
    const data = await KatajereAPI().patch(`/customers/edit-customer`, payload);
    return data.data;
  },
  createOrder: async (payload: {
    vendorId: string;
    userEmail: string;
    customerId: string;
    customerName: string;
    customerEmail?: string;
    customerPhone: string;
  }) => {
    const data = await KatajereAPI().post(`/order/create-order`, payload);
    return data.data;
  },
  editOrder: async (payload: {
    vendorId: string;
    customerId: string;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
    cartProducts: any[];
    vendorEmail: string;
    billingAddress: string;
    billingCity: string;
    billingState: string;
    billingZipCode: string;
    country: string;
    paymentMethod: string;
    channel: string;
    promoCode: string;
    deliveryFee: number;
    orderDate: Date;
    paymentStatus: string;
    amountPaid: number;
    isVendorCreated: 'No';
    saveAddress: string;
    socialMediaHandle: string;
    firstName: string;
    lastName: string;
    orderNumber: any;
  }) => {
    const data = await KatajereAPI().post(`/order/edit-order`, payload);
    return data.data;
  },
  markOrder: async (payload: {
    vendorId: string;
    orderId: string;
    vendorEmail: string;
    paymentStatus: string;
    amountPaid: string;
    orderStatus: string;
    // sentOutDate: string,
  }) => {
    const data = await KatajereAPI().post(`/order/edit-order`, payload);
    return data.data;
  },

  allOrders: async (vendorId: string) => {
    const data = await KatajereAPI().get(`/order/orders?vendorId=${vendorId}`);
    return data.data;
  },
  getCustomers: async (vendorId: string) => {
    const data = await KatajereAPI().get(
      `/customers/get-customers?vendorId=${vendorId}`
    );
    return data.data;
  },
  getProfile: async (customerId: string, vendorId: string) => {
    const data = await KatajereAPI().get(
      `/customers/customer-profile?customerId=${customerId}&vendorId=${vendorId}`
    );
    return data.data;
  },
  createPromo: async (payload: {
    vendorId: string;
    promoCode: string;
    promoType: string;
    promoName: string;
    valueType: string;
    value: string;
    description: string;
    startDate: string;
    endDate: string;
    eligibleCustomersType: string;
    eligibleCustomers: string[];
    eligibleProductsType: string;
    eligibleProducts: string[];
    usageLimitType: string;
    usageLimit: string;
  }) => {
    const data = await KatajereAPI().post(`/promo/create-promo`, payload);
    return data.data;
  },
  deliveryFee: async (payload: {
    vendorId: string;
    vendorEmail: string;
    deliveryFeeValue: string;
    deliveryTitle: string;
    isPublic: string;
  }) => {
    const data = await KatajereAPI().post(
      `/order/create-delivery-fee`,
      payload
    );
    return data.data;
  },
  getPromo: async (vendorId: string) => {
    const data = await KatajereAPI().get(
      `/promo/get-promo?vendorId=${vendorId}`
    );
    return data.data;
  },
  getPromoDetails: async (vendorId: string, promoCode: string) => {
    const data = await KatajereAPI().get(
      `/promo/promo-details?vendorId=${vendorId}&promoCode=${promoCode}`
    );
    return data.data;
  },
  getOrderDetails: async (orderId: string, vendorId: string) => {
    const data = await KatajereAPI().get(
      `/order/order-details?orderId=${orderId}&vendorId=${vendorId}`
    );
    return data.data;
  },
  getDeliveryFee: async (vendorId: string) => {
    const data = await KatajereAPI().get(
      `/order/get-delivery-fees?vendorId=${vendorId}`
    );
    return data.data;
  },
  editPromo: async (payload: {
    vendorId?: string;
    promoCode?: string;
    promoType?: string;
    promoName?: string;
    valueType?: string;
    value?: string;
    description?: string;
    startDate?: string;
    endDate?: string;
    eligibleCustomersType?: string;
    eligibleProductsType?: string;
    eligibleCustomers?: string[];
    eligibleProducts?: string[];
    usageLimitType?: string;
    usageLimit?: number;
    status?: string;
  }) => {
    const data = await KatajereAPI().post(`/promo/edit-promo`, payload);
    return data.data;
  },
  deletePromo: async (payload: { promoCode: string; vendorId: string }) => {
    return await KatajereAPI().delete('/promo/delete-promo', { data: payload });
  },
  deleteDelivery:async(payload:{vendorId:string; deliveryId:string})=>{
    return await KatajereAPI().delete('/order/delete-delivery-fee',{data:payload})
  },
  updateDelivery:async(payload: {
    vendorId: string;
    deliveryFeeValue: number;
    deliveryTitle: string;
    isPublic: string;
    deliveryId:string;
  })=>{
    const data =await KatajereAPI().patch('/order/update-delivery-fee',payload)
    return data.data
    
  },

  deleteEntireOrder: async (payload: {
    vendorId: string;
    orderId: string;
    deleteEntireOrder: boolean;
    vendorEmail: string;
  }) => {
    return await KatajereAPI().post('/order/delete', payload);
  },
  sendAsEmail: async (payload: {
    vendorId: string;
    customerId: string;
    orderId: string;
    type: string;
    publicUrl: string;
    invoiceDownload?: string;
    receiptDownload?: string;
    paymentStatus: string;
    paymentMethod: string;
    orderDate: string;
    paidOn: string;
    grandTotal: string;
    subTotalFee: string;
    deliveryFee: string;
    discountedFee: string;
  }) => {
    return await KatajereAPI().post('/order/send-email', payload);
  },
  removeProductCustomer: async (payload: {
    vendorId: string;
    customerId?: string | null;
    productId?: string | null;
    promoCode: string;
  }) => {
    return await KatajereAPI().delete('/promo/remove-item', { data: payload });
  },
};
