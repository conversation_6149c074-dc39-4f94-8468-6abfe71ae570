// import React from "react";
// import { Line } from "react-chartjs-2";
// import {
//   Chart as ChartJS,
//   CategoryScale,
//   LinearScale,
//   PointElement,
//   LineElement,
//   Title,
//   Tooltip,
//   Legend,
//   Filler,
//   ChartOptions,
// } from "chart.js";

// // Register Chart.js components
// ChartJS.register(
//   CategoryScale,
//   LinearScale,
//   PointElement,
//   LineElement,
//   Title,
//   Tooltip,
//   Legend,
//   Filler
// );

// // Define the data type for salesTrendData
// interface SalesTrendData {
//   period: string; // ISO date string
//   totalSales: number;
// }

// // Props for the chart component
// interface LineChartProps {
//   salesTrendData: SalesTrendData[];
// }

// const Chart: React.FC<LineChartProps> = ({ salesTrendData }) => {
//   // Map the data for chart usage
//   const labels = salesTrendData.map((sale) =>
//     new Date(sale.period).toLocaleString("default", {
//       month: "short",
//       day: "2-digit",
//     })
//   ); // e.g., "Dec 21"

//   const salesData = salesTrendData.map((sale) => sale.totalSales);

//   // Chart data object
//   const data = {
//     labels,
//     datasets: [
//       {
//         label: "Total Sales Over Time",
//         data: salesData,
//         borderColor: "#28A745",
//         fill: false,
//         tension: 0.4,
//         pointRadius: 6,
//         pointHoverRadius: 8,
//         borderWidth: 2,
//       },
//     ],
//   };

//   // Chart options
//   const options: ChartOptions<"line"> = {
//     responsive: true,
//     maintainAspectRatio: true,
//     plugins: {
//       legend: {
//         display: false,
//       },
//       title: {
//         display: false,
//       },
//       tooltip: {
//         callbacks: {
//           label: function (tooltipItem) {
//             const rawValue = tooltipItem.raw as number;
//             // Tooltip logic for small and large values
//             if (rawValue < 1000) {
//               return `${rawValue} Sales`; // Display small values as-is
//             }
//             const formattedValue = (rawValue / 1000).toFixed(1) + "k";
//             return `${formattedValue} Sales`; // Format larger values
//           },
//         },
//       },
//     },
//     scales: {
//       x: {
//         type: "category",
//         grid: {
//           display: false,
//         },
//         ticks: {
//           color: "#999",
//           font: {
//             size: 12,
//             weight: "bold",
//           },
//         },
//       },
//       y: {
//         beginAtZero: true,
//         grid: {
//           color: "rgba(200, 200, 200, 0.3)",
//         },
//         ticks: {
//           callback: function (value) {
//             const numericValue = Number(value);
//             // No "k" formatting for smaller values
//             if (numericValue < 1000) {
//               return numericValue.toString(); // Show the actual value
//             }

//             // Format larger values in "k"
//             return (numericValue / 1000).toFixed(0) + "k";
//           },
//           color: "#999",
//           font: {
//             size: 12,
//             weight: "bold",
//           },
//         },
//       },
//     },
//   };

//   return <Line data={data} options={options} />;
// };

// export default Chart;


import React from "react";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from "chart.js";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

// Define the data type for salesTrendData
interface SalesTrendData {
  period: string; // ISO date string
  totalSales: number;
}

// Props for the chart component
interface BarChartProps {
  salesTrendData: SalesTrendData[];
}

const Chart: React.FC<BarChartProps> = ({ salesTrendData }) => {
  // Map the data for chart usage
  const labels = salesTrendData.map((sale) =>
    new Date(sale.period).toLocaleString("default", {
      month: "short",
      day: "2-digit",
    })
  ); // e.g., "Dec 21"

  const salesData = salesTrendData.map((sale) => sale.totalSales);

  // Chart data object
  const data = {
    labels,
    datasets: [
      {
        label: "Total Sales Over Time",
        data: salesData,
        backgroundColor: "#4f4cd8",
        borderColor: "#4f4cd8",
        borderWidth: 1,
        barThickness: 20, // Set the thickness of the bars
        maxBarThickness: 20, // Optional: maximum width of the bars
      },
    ],
  };

  // Chart options
  const options: ChartOptions<"bar"> = {
    responsive: true,
    maintainAspectRatio: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function (tooltipItem) {
            const rawValue = tooltipItem.raw as number;
            // Tooltip logic for small and large values
            if (rawValue < 1000) {
              return `${rawValue} Sales`; // Display small values as-is
            }
            const formattedValue = (rawValue / 1000).toFixed(1) + "k";
            return `${formattedValue} Sales`; // Format larger values
          },
        },
      },
    },
    scales: {
      x: {
        type: "category",
        grid: {
          display: false,
        },
        ticks: {
          color: "#999",
          font: {
            size: 12,
            weight: "bold",
          },
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(200, 200, 200, 0.3)",
        },
        ticks: {
          callback: function (value) {
            const numericValue = Number(value);
            // No "k" formatting for smaller values
            if (numericValue < 1000) {
              return numericValue.toString(); // Show the actual value
            }
            // Format larger values in "k"
            return (numericValue / 1000).toFixed(0) + "k";
          },
          color: "#999",
          font: {
            size: 12,
            weight: "bold",
          },
        },
      },
    },
  };

  return <Bar data={data} options={options} />;
};

export default Chart;

