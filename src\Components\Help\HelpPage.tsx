/* eslint-disable @typescript-eslint/no-explicit-any */
import { DocumentUpload } from "iconsax-react";
import InputField from "../../Constants/InputField";
import { useEffect, useState } from "react";
import Success from "./Success";
import { useUserAuthStore } from "../../store/auth";
import { useMutation } from "react-query";
import { orderServices } from "../../utils/Services/Settings";
import { toast } from "react-toastify";

interface helpModal {
  isOpen: boolean;
  closeModal: () => void;
}
interface feedBackDetails {
  email_address: string;
  full_name: string;
  content: string;
  uploaded_image: File | null;
}
type ImageState = {
  previewUrl: string;
  file: File;
} | null;
const HelpPage: React.FC<helpModal> = ({ isOpen, closeModal }) => {
  const [success, setSuccess] = useState<boolean>(false);
  const [enableButton, setEnableButton] = useState<boolean>(false);
  const [image, setImage] = useState<ImageState>(null);
  const user = useUserAuthStore((state) => state.user);
  const [feedBackDetails, setFeedBackDetails] = useState<feedBackDetails>({
    email_address: user?.userEmail || "",
    full_name: "",
    content: "",
    uploaded_image: null,
  });
  const mutation = useMutation((formData: FormData) =>
    orderServices.feedBack(formData)
  );
  useEffect(() => {
    const allFieldsFilled = Object.entries(feedBackDetails).every(
      ([key, value]) => {
        if (key === "uploaded_image") return true;
        return value !== "";
      }
    );
    setEnableButton(allFieldsFilled);
  }, [feedBackDetails]);
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFeedBackDetails((prevDetails) => ({
      ...prevDetails,
      [name]: value,
    }));
  };
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      const previewUrl = URL.createObjectURL(file);
      setFeedBackDetails((prevDetails) => ({
        ...prevDetails,
        uploaded_image: file,
      }));

      setImage({
        previewUrl,
        file,
      });
    }
  };

  const handleSuccess = () => {
    closeModal();
    setSuccess((prev) => !prev);
  };
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData();

    // Append text fields to FormData
    formData.append("email_address", feedBackDetails.email_address);
    formData.append("full_name", feedBackDetails.full_name);
    formData.append("content", feedBackDetails.content);

    // Append the file to FormData if a file is selected
    if (feedBackDetails.uploaded_image) {
      formData.append("uploaded_image", feedBackDetails.uploaded_image);
    }
    mutation.mutate(formData, {
      onSuccess: (data) => {
        toast.success(data?.data?.message);
        setSuccess(true);
      },
      onError: (error: any) => {
        const errorMessage =
          error?.response?.data?.error || "Something went wrong";
        toast.error(errorMessage);
        setSuccess(false);
      },
    });
  };

  return (
    <div className="fixed font-sora top-0 left-0 w-full h-full bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-screen md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="h-[calc(100vh-80px)] overflow-y-auto scrollbar-none hidescroll">
            <form
              onSubmit={handleSubmit}
              encType="multipart/form-data"
              className="pl-1 flex flex-col gap-5"
            >
              <div>
                <h2 className="text-[24px] font-sora leading-7 mb-2.5 font-semibold">
                  Do you need Help?
                </h2>
                <p className="text-sm text-[#919191] font-sora">
                  Kindly lodge your compliant below, and someone on our team
                  will respond to you promptly
                </p>
              </div>

              <InputField
                label="Name"
                type="text"
                name="full_name"
                placeholder="Enter your name"
                value={feedBackDetails.full_name}
                onChange={handleChange}
              />

              <InputField
                label="Email Address"
                type="email"
                name="email_address"
                placeholder="Enter your email address"
                value={feedBackDetails.email_address}
                onChange={handleChange}
              />

              <div className="p-0 flex flex-col gap-2 relative">
                <p className="text-sm font-sans font-normal text-[#5b5b5b]">
                  Description
                </p>
                <textarea
                  rows={12}
                  className=" text-[#7b7b7b] text-xs p-4 border border-[#cccccc] rounded-2xl outline-none"
                  placeholder="Describe why you are reaching out"
                  value={feedBackDetails.content}
                  onChange={handleChange}
                  name="content"
                ></textarea>
              </div>
              <div>
                {image && (
                  <div className="flex items-center justify-between w-full p-4 shadow-sm rounded-lg border border-[#CCCCCC] my-4">
                    <div className="flex items-center space-x-4">
                      <img
                        src={image?.previewUrl}
                        alt={image?.file?.name}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div>
                        <p className="text-sm font-medium">
                          {image?.file?.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {image?.file?.size
                            ? `${(image.file.size / 1024).toFixed(2)} KB`
                            : "Unknown Size"}
                        </p>
                      </div>
                    </div>

                    <div>
                      <span
                        onClick={() => setImage(null)}
                        className="text-base font-bold cursor-pointer my-4 flex justify-end z-[999999]"
                      >
                        &times;
                      </span>
                      <button
                        type="button"
                        onClick={() => setImage(null)}
                        className="text-red-500 text-sm hover:text-red-600 transition"
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                )}
                <p className="text-[#5B5B5B] font-sora text-sm font-normal mb-2">
                  Add Images to describe the problem (Optional)
                </p>
                <div className="border border-dashed border-[#6200ea] rounded-2xl p-4 text-center cursor-pointer bg-[#e6e5f9] transition-colors duration-300 mb-[200px]">
                  <label htmlFor="fileInput" className="cursor-pointer">
                    <input
                      type="file"
                      accept="image/*"
                      id="fileInput"
                      multiple
                      name="uploaded_image"
                      className="hidden"
                      onChange={handleFileChange}
                    />
                    <div className="flex flex-col items-center">
                      <div className=" mb-2.5 ">
                        <DocumentUpload size="32" color="#4F4CD8" />
                      </div>
                      <p className="text-base text-[#303237] font-semibold">
                        Click here to upload or Drag & Drop
                      </p>
                      <p className=" mt-1 text-[#7E8494] text-xs">
                        You can add up to 5 files (PNG, JPG, PDF)
                      </p>
                    </div>
                  </label>
                </div>
              </div>
              <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5  p-10">
                <div className="flex w-full px-7.5 gap-2.5">
                  <button
                    onClick={closeModal}
                    className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 py-4 rounded-2xl cursor-pointer flex-1/2"
                  >
                    Back
                  </button>
                  <button
                    disabled={mutation.isLoading || !enableButton}
                    type="submit"
                    className={`${
                      mutation.isLoading || !enableButton
                        ? "cursor-default bg-[#9392e4]"
                        : "bg-[#4f4cd8] hover:bg-[#3e3abf] cursor-pointer"
                    }
                     w-full text-[#fcfcfc] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl flex-2`}
                  >
                    {mutation.isLoading ? "sending message..." : "Send Message"}{" "}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
      {success && <Success isOpen={success} closeModal={handleSuccess} />}
    </div>
  );
};

export default HelpPage;
