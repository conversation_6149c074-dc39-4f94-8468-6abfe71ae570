/* eslint-disable @typescript-eslint/no-explicit-any */
import { More } from "iconsax-react";
import Table from "../../utils/Table";
import InventoryTableHead from "./InventoryTableHead";
import { useState, useMemo, useEffect } from "react";
import DropModal from "./DropModal";
import ProductDetails from "./ProductDetails";
import EditProduct from "./EditProduct";
import AdjustProduct from "./AdjustProduct";
import RestockProduct from "./RestockProduct";
import DeleteProduct from "./DeleteProduct";
import usePagination from "../../Constants/usePagination";
import { inventoryServices } from "../../utils/Services/InventoryServices";
import { useUserAuthStore } from "../../store/auth";
import { useQuery } from "react-query";
import ProductCard from "../../utils/ProductCard";
import logo from "../../../public/Favicon.png";
import AiemptyLogo from "../../assets/Aiempty.svg";
import leftArrowIcon from "../../assets/leftArrow.svg";
import rightArrowIcon from "../../assets/rightArrow.svg";
import thumbnail from'../../assets/video_thumbnail.jpg'
// import { Link } from "react-router-dom";
// import HomeButtons from "../../Constants/HomeButtons";

const TableInventory = ({
  products,
  refetch,
  isError,
  isLoading,
  category,
}: any) => {
  const [selectedRow, setSelectedRow] = useState<any>(null);
  const [viewProductModal, setViewProductModal] = useState<any>(null);
  const [editProductModal, setEditProductModal] = useState<any>(null);
  const [adjustProductModal, setAdjustProductModal] = useState<any>(null);
  const [restockProductModal, setRestockProductModal] = useState<any>(null);
  const [deleteProductModal, setDeleteProductModal] = useState<any>(null);
  const [modalPosition, setModalPosition] = useState<{
    top: number;
  } | null>(null);
  const user = useUserAuthStore((state) => state.user);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("");

  const {
    data,
    isError: detailsError,
    isLoading: detailsIsLoading,
    refetch: productRefetch,
  } = useQuery(
    ["product_details", user.vendorId, selectedRow],
    () => inventoryServices.getProductDetails(user.vendorId, selectedRow),
    {
      enabled: !!selectedRow,
    }
  );
  const selectedProduct = data?.data;
  const { page, limit, Pagination } = usePagination({
    page: 1,
    limit: 10,
    total: products?.length,
  });

  const filteredProducts = useMemo(() => {
    return products?.filter((product: any) => {
      const matchesSearch = product.productName
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesCategory =
        selectedCategory === "" || product.category === selectedCategory;
      const matchesStatus =
        selectedStatus === "" || product.status === selectedStatus;

      return matchesSearch && matchesCategory && matchesStatus;
    });
  }, [products, searchTerm, selectedCategory, selectedStatus]);

  const paginatedRows = filteredProducts?.slice(
    (page - 1) * limit,
    page * limit
  );

  useEffect(() => {
    if (viewProductModal) {
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    return () => {
      document.body.classList.remove("no-scroll");
    };
  }, [viewProductModal]);

  const handleActionClick = (event: React.MouseEvent, productId: string) => {
    event.stopPropagation();
    setSelectedRow(productId);
    const target = event.currentTarget.getBoundingClientRect();
    setModalPosition({
      top: target.top + window.scrollY,
    });
  };

  const handleRowClick = (productId: string) => {
    setViewProductModal(productId);
    setSelectedRow(productId);
  };

  const handleViewProduct = (productId: string) => {
    setViewProductModal(productId);
    setSelectedRow(productId);
  };

  const handleEditProduct = (productId: string) => {
    setEditProductModal(productId);
    setSelectedRow(productId);
  };

  const handleAdjustProduct = (productId: string) => {
    setAdjustProductModal(productId);
    setSelectedRow(productId);
  };

  const handleRestockProduct = (productId: string) => {
    setRestockProductModal(productId);
    setSelectedRow(productId);
  };

  const handleDeleteProduct = (productId: string) => {
    setDeleteProductModal(productId);
    setSelectedRow(productId);
  };
  const deleteModalClose = () => {
    setDeleteProductModal(null);
    setViewProductModal(null);
  };

  const headers = [
    {
      title: "Product Name",
      key: "productName",
      render: (row: any) => (
        <div className="flex items-center gap-2">
          <img
            src={row.productImage}
            alt="product"
            className="h-[30px] rounded-full object-cover w-[30px]"
            onError={(e: any) => {
              e.target.src = thumbnail; // Fallback to thumbnail on error
            }}
          />
          <p className="truncate font-bold">{row.productName}</p>
        </div>
      ),
    },
    {
      title: "Sku",
      key: "SKU",
      render: (row: any) => <div>{row.SKU}</div>,
    },
    {
      title: "Category",
      key: "category",
      render: (row: any) => <div>{row.category}</div>,
    },
    {
      title: "Stock Level",
      key: "stockLevel",
      render: (row: any) => <div>{row?.stockLevel?.toLocaleString()}</div>,
    },
    {
      title: "Reorder Level",
      key: "reorderLevel",
      render: (row: any) => <div>{row?.reorderLevel?.toLocaleString()}</div>,
    },
    {
      title: "Status",
      key: "status",
      render: (row: any) => (
        <div
          className={`text-center px-4 py-1 rounded-xl truncate ${
            row.status === "In Stock"
              ? "text-[#28a745] bg-[#EAF7ED]"
              : row.status === "Low Stock"
              ? "text-[#FFC107] bg-[#FFF3E0]"
              : "text-[#DC3545] bg-[#F8D7DA]"
          }`}
        >
          {row.status}
        </div>
      ),
    },
    {
      title: "Purchase Price",
      key: "purchasePrice",
      render: (row: any) => <p>{row?.purchasePrice?.toLocaleString()}</p>,
    },
    {
      title: "Selling Price",
      key: "sellingPrice",
      render: (row: any) => <p>{row?.sellingPrice?.toLocaleString()}</p>,
    },
    {
      title: "Action",
      render: (row: any) => (
        <div className="relative">
          <More
            size="16"
            color="#1A1A1A"
            onClick={(event) => handleActionClick(event, row.productId)}
            className="cursor-pointer"
          />
        </div>
      ),
    },
  ];

  return (
    <div className="my-15 bg-white border border-[#dedede] font-sora rounded-3xl mb-8">
      <InventoryTableHead
        data={category}
        searchTerm={searchTerm}
        selectedCategory={selectedCategory}
        selectedStatus={selectedStatus}
        onSearchChange={setSearchTerm}
        onCategoryChange={setSelectedCategory}
        onStatusChange={setSelectedStatus}
      />
      {isError ? (
        <div className="text-center py-10 mb-5">
          Error loading data, refresh
        </div>
      ) : (
        <div className="relative">
          {isLoading ? (
            <div className="text-center py-10 flex justify-center">
              <img
                className="animate-spin w-[50px] h-[50px]"
                src={logo}
                alt="logo"
              />
            </div>
          ) : (
            <>
              {filteredProducts && filteredProducts.length > 0 ? (
                <>
                  <div className="hidden md:block">
                    <Table
                      rows={paginatedRows}
                      headers={headers}
                      showHead={true}
                      allowRowClick
                      onRowClick={(row) => handleRowClick(row.productId)}
                    />
                  </div>
                  {paginatedRows.map((product: any, index: any) => (
                    <div key={index} className="md:hidden mt-4 px-5">
                      <ProductCard
                        productName={product.productName}
                        productImage={product.productImage}
                        productSKU={product.SKU}
                        status={product.status}
                        currentStock={product.stockLevel}
                        onClick={() => handleRowClick(product.productId)}
                      />
                    </div>
                  ))}
                  <div className="w-full  px-5">
                    <Pagination />
                  </div>
                </>
              ) : (
                <div className="flex px-5 flex-col items-center justify-center text-center my-[70px]">
                  {filteredProducts ? (
                    <div className="flex flex-col items-center justify-center text-center mt-8 space-y-4">
                      <img
                        src={AiemptyLogo}
                        alt="No Data"
                        className="w-16 h-16"
                      />
                      <p className="text-sm font-sora text-[#5B5B5B]">
                        You currently have no stock in inventory Add New
                        Products using the button above
                      </p>
                    </div>
                  ) : (
                    <p className="mt-6 font-sora text-base font-medium">
                      Product list is currently empty, proceed to add products
                    </p>
                  )}
                </div>
              )}

              <div className="p-4 hidden items-center gap-4 justify-end text-[#5A5A5A] text-[14px] ">
                <p>0 items</p>
                 
                 <button className="py-3 px-3.5 border rounded-2xl">Previous</button>
                 <button className="py-3 px-3.5 border rounded-2xl">Next</button>
              </div>
            </>
          )}

          <div className="mt-6 hidden mb-4 items-center text-[#5A5A5A]  p-4 justify-between">
            <p>0 items</p>
            <div className="flex gap-4">
              <div className="py-3 border border-[#DEDEDE] rounded-2xl px-3">
                <img src={rightArrowIcon} alt="" />
              </div>
              <div className="py-3 border border-[#DEDEDE] rounded-2xl px-3">
                <img src={leftArrowIcon} alt="" />
              </div>
            </div>
          </div>
        </div>
      )}

      {selectedRow && modalPosition && (
        <DropModal
          productId={selectedRow}
          position={modalPosition}
          onViewProduct={handleViewProduct}
          onEditProduct={handleEditProduct}
          onAdjustProduct={handleAdjustProduct}
          onRestockProduct={handleRestockProduct}
          onDeleteProduct={handleDeleteProduct}
          onClose={() => {
            setSelectedRow(null);
            setModalPosition(null);
          }}
        />
      )}
      {viewProductModal && (
        <ProductDetails
          onClose={() => setViewProductModal(null)}
          isOpen={viewProductModal}
          viewingId={viewProductModal}
          openEditProduct={handleEditProduct}
          openRestockProduct={handleRestockProduct}
          openAdjustProduct={handleAdjustProduct}
          openDeleteProduct={handleDeleteProduct}
          productDetails={selectedProduct}
          isLoading={detailsIsLoading}
          isError={detailsError}
        />
      )}
      {editProductModal && (
        <EditProduct
          onClose={() => setEditProductModal(null)}
          isOpen={editProductModal}
          isError={detailsError}
          productDetails={selectedProduct}
          refetch={refetch}
          productRefetch={productRefetch}
        />
      )}
      {adjustProductModal && (
        <AdjustProduct
          onClose={() => setAdjustProductModal(null)}
          isOpen={adjustProductModal}
          viewingId={adjustProductModal}
          productDetails={selectedProduct}
          isError={detailsError}
          refetch={refetch}
          productRefetch={productRefetch}
        />
      )}
      {restockProductModal && (
        <RestockProduct
          onClose={() => setRestockProductModal(null)}
          isOpen={restockProductModal}
          viewingId={restockProductModal}
          productDetails={selectedProduct}
          isError={detailsError}
          refetch={refetch}
          productRefetch={productRefetch}
        />
      )}
      {deleteProductModal && (
        <DeleteProduct
          onClose={deleteModalClose}
          isOpen={deleteProductModal}
          viewingId={deleteProductModal}
          productDetails={selectedProduct}
          isError={detailsError}
          refetch={refetch}
        />
      )}
    </div>
  );
};

export default TableInventory;
