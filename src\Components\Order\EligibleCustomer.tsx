import { SearchNormal1, Trash } from "iconsax-react";
import { useMemo, useState } from "react";
import usePagination from "../../Constants/usePagination";
import { useMutation } from "react-query";
import { orderServices } from "../../utils/Services/Order";
import { useUserAuthStore } from "../../store/auth";
import { toast } from "react-toastify";
import ConfirmationModal from "../../Constants/ConfirmationModal";

/* eslint-disable @typescript-eslint/no-explicit-any */
interface Eligible {
  closeModal: () => void;
  isOpen: boolean;
  customer?: any;
  promoCode: any;
  refetch:any
}
const EligibleCustomer: React.FC<Eligible> = ({
  closeModal,
  isOpen,
  customer,
  promoCode,
  refetch
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const handleSearchChange = (event: any) => {
    setSearchTerm(event.target.value);
  };

  const { page, limit, Pagination } = usePagination({
    page: 1,
    limit: 8,
    total: customer.length,
  });
  const filteredCustomers = useMemo(() => {
    return customer.filter((customer: any) => {
      const matchesSearch = customer.customerName
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

      return matchesSearch;
    });
  }, [customer, searchTerm]);

  const paginatedRows = filteredCustomers?.slice(
    (page - 1) * limit,
    page * limit
  );

  const [confrimDelete, setConfirmDelete] = useState<boolean>(false);
  const [selectedId, setSelectedId] = useState<string | null>(null);

  const toggleDelete = (id: string | null) => {
    setSelectedId(id);
    setConfirmDelete((prev) => !prev);
  };
  const user = useUserAuthStore((state) => state.user);

  const mutatation = useMutation(orderServices.removeProductCustomer);
  const handleRemove = async () => {
    if (!selectedId) return;

    const payload = {
      vendorId: user.vendorId,
      customerId: selectedId,
      promoCode: promoCode,
    };
    try {
      mutatation.mutate(payload, {
        onSuccess: (response) => {
          toast.success(response?.data?.message);

          refetch()
          setConfirmDelete(false);
          setSelectedId(null);
        },

        onError: (error: any) => {
          toast.error(error?.response?.data?.error || error?.message);
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  // const [isdropDown, setIsdropDown] = useState<boolean>(false)

  // const handleDropDown = () => {
  //   setIsdropDown((prev)=>!prev)
  // }

  return (
    <div className="fixed font-sora  bg-black/10 top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-3 md:p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? "translate-x-0 " : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="overflow-y-auto  flex flex-col gap-8 h-full scrollbar-none hidescroll ">
            <div className="md:pl-6 flex flex-col gap-4">
              <div>
                <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                  Eligible Customers
                </h2>
                <p className=" text-sm text-[#919191] font-sora ">
                  See and manage all eligible customers for holiday mega sales
                </p>
              </div>
            </div>

            <div>
              <div className="w-full md:pl-0 mt-[8px] flex flex-col md:flex-row justify-between gap-4">
                <div className=" flex-1 flex w-full items-center gap-[10px] py-2  px-2 border border-gray-300 text-[#ABABAB] text-xs rounded-2xl bg-[#fcfcfc] ">
                  <SearchNormal1 size="20" color="#ABABAB" />
                  <input
                    type="text"
                    id="searchInput"
                    placeholder="Search"
                    value={searchTerm}
                    className="bg-[#fcfcfc]  outline-none "
                    onChange={handleSearchChange}
                  />
                </div>
                {/* <div className="flex-1 relative">
                  <button
                    onClick={handleDropDown}
                    className="border w-full h-[49px] justify-center border-gray-300 gap-2 rounded-2xl px-3 py-2 text-base flex items-center cursor-pointer bg-[#4f4cd8] text-primary-baseWhite font-semibold"
                  >
                    Add Eligible Customer <ArrowDown2 size={16} />
                  </button>
                  {isdropDown && (
                    <ul className="absolute w-full bg-white border border-gray-300 rounded-lg shadow-lg mt-1 z-10 max-h-[200px] overflow-y-auto">
                      {customerlist.map((customer:any) => (
                        <li
                          key={customer.id}
                          className="px-4 py-2 cursor-pointer hover:bg-gray-100 text-gray-700"
                        >
                          {customer.customerName}
                        </li>
                      ))}
                    </ul>
                  )}
                </div> */}
              </div>
            </div>

            {/* list */}
            <div className="flex flex-col gap-4">
              <h4 className="text-[18px] text-[#2A2A2A] font-sora leading-7  font-bold">
                List of All Eligible Customers
              </h4>
              {/* customer list */}

              {filteredCustomers && filteredCustomers.length > 0 ? (
                <>
                  {paginatedRows?.map((i: any, index: any) => (
                    <div  key={index}>
                      <div className="pb-4 mb-4 border-b-[0.5px] border-primary-neutral200  w-full flex justify-between items-center">
                        <p className="text-[14px] text-[#5B5B5B] font-sora">
                          {i?.customerName}
                        </p>
                        <button
                          onClick={() => toggleDelete(i.customerId)}
                          className="hidden md:inline border-[2px] border-[#DC3545] text-[#DC3545] font-sora text-[14px] py-[8px] px-[12px] rounded-3xl"
                        >
                          Remove Customer
                        </button>
                        <button
                          onClick={() => toggleDelete(i.customerId)}
                          className="inline md:hidden border-[2px] border-[#DC3545] text-[#DC3545] font-sora text-[14px] py-[8px] px-[12px] rounded-3xl"
                        >
                          <Trash />
                        </button>
                      </div>
                    </div>
                  ))}
                  <div className="px-5 bottom-0 fixed w-full">
                    <Pagination />
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center text-center my-[70px]">
                  <p className="mt-6 text-base font-medium">
                    Ooops!! There is no customer
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      {confrimDelete && (
        <ConfirmationModal
          isOpen={confrimDelete}
          title="Are you sure you want to remove this customer?"
          text="This action will remove the customer "
          cancelButtonText="No , Cancel"
          actionButtonText={
            mutatation.isLoading ? "Removing Customer" : "Remove Customer"
          }
          onCancelClick={() => toggleDelete(null)}
          onActionClick={handleRemove}
          actionButtonStyle="bg-[#DC3545]"
          actionButtonDisabled={mutatation.isLoading}
        />
      )}
    </div>
  );
};

export default EligibleCustomer;
