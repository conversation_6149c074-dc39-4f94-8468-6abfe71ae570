/* eslint-disable @typescript-eslint/no-explicit-any */
import { SearchNormal1 } from 'iconsax-react';
import React, { useState } from 'react';
import Filter from '../../../Constants/Filter';

interface TrackTableheadProps {
  onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFilterChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
}

const TrackTablehead: React.FC<TrackTableheadProps> = ({
  onSearch,
  onFilterChange,
}) => {
  const [dropDown, setDropDown] = useState<any>(false);
  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange(e); 
    setDropDown(false);
  };
  return (
    <div className="flex w-full gap-4 flex-col md:flex-row justify-between items-start p-5">
      <div className="md:w-[626px]">
        <h1 className="text-lg text-[#393939] mb-2 font-semibold ">
          Stock Movement History
        </h1>
        <p className="text-sm text-[#919191] font-normal font-sora mb-2.5">
          Track all your stock movement over time{' '}
        </p>
      </div>

      <div className="w-full md:w-[442px] flex flex-col md:flex-row justify-between gap-2.5">
        <div className="flex w-full gap-2 items-center">
          <div className="flex w-full items-center gap-[10px] py-2  px-2 border border-gray-300 text-[#ABABAB] text-xs rounded-2xl bg-[#fcfcfc] ">
            <SearchNormal1 size="20" color="#ABABAB" />
            <input
              type="text"
              id="searchInput"
              placeholder="Search"
              className="bg-[#fcfcfc] outline-none "
              onChange={onSearch}
            />
          </div>

          <div className="md:hidden">
            <Filter
              category="category"
              onFilterChange={handleFilterChange}
              stat1="In"
              stat2="Out"
              setDropDown={setDropDown}
              dropDown={dropDown}
            />
          </div>
        </div>
        <select
          id="categoryFilter"
          onChange={handleFilterChange}
          className="py-2 hidden md:block px-4 border text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none w-full">
          <option value="" className="">
            Filter by Category
          </option>
          <option value="In">in</option>
          <option value="Out">out</option>
        </select>
      </div>
    </div>
  );
};
export default TrackTablehead;
