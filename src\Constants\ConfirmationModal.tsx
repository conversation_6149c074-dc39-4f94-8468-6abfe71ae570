interface ModalProps {
  isOpen: boolean;
  title: string;
  text: string;
  actionButtonText: string;
  cancelButtonText?: string;
  onActionClick: () => void;
  onCancelClick?: () => void;
  actionButtonDisabled?: boolean;
  actionButtonStyle?: string;
}

const ConfirmationModal: React.FC<ModalProps> = ({
  isOpen,
  title,
  text,
  actionButtonText,
  cancelButtonText,
  onActionClick,
  onCancelClick,
  actionButtonDisabled = false,
  actionButtonStyle = 'bg-[#28A745]',
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 p-4 md:p-0 flex items-center justify-center bg-black bg-opacity-50 z-50 font-sora">
      <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-[507px] ">
        <h2 className="text-xl font-semibold text-[#2A2A2A]">{title}</h2>
        <p className="mt-4 text-[#5B5B5B]">{text}</p>
        <div className="mt-10 flex gap-4 items-center">
          {cancelButtonText && onCancelClick && (
            <button
              type="button"
              onClick={onCancelClick}
              className="border border-[#DCDCDC] rounded-2xl text-[#2A2A2A] py-2 font-semibold md:text-base text-[12px] w-full">
              {cancelButtonText}
            </button>
          )}
          <button
            type="button"
            onClick={onActionClick}
            disabled={actionButtonDisabled}
            className={`${actionButtonStyle} py-2 rounded-2xl text-[#FCFCFC] md:text-base text-[12px] font-bold border w-full ${
              actionButtonDisabled ? 'opacity-50 cursor-not-allowed' : ''
            }  `}>
            {actionButtonText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
