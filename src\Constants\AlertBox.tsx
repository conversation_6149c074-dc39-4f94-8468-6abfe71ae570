
import { useEffect, useState } from "react";
// Define the AlertSettingKeys type as a union of string literals
type AlertSettingKeys =
  | "lowStockEnabled"
  | "outOfStockEnabled"
  | "highStockEnabled"
  | "sensitivityEnabled";
interface alert {
  Title: string;
  content: string;
  nameAlert: AlertSettingKeys;
  isToggled: boolean;
  onToggle: (alertType: AlertSettingKeys , newState:boolean) => void; // Function to handle the toggle action
}
const AlertBox: React.FC<alert> = ({
  Title,
  content,
  onToggle,
  nameAlert,
  isToggled: initialToggled,
}) => {

    useEffect(() => {
      setIsToggled(initialToggled); // Update local state if the prop changes
    }, [initialToggled]);
  
  
  const [isToggled, setIsToggled] = useState<boolean>(initialToggled);
  const handleToggle = () => {
    const newToggleState = !isToggled;
    setIsToggled(newToggleState);
    onToggle(nameAlert, newToggleState); // Notify parent about the change
  };

  return (
    <div className="rounded-2xl flex flex-col gap-4 border-primary-neutral200 border-[1px] p-4 md:w-[460px]">
      <div className="flex justify-between items-center">
        <p className="text-primary-neutralt1 font-sora text-[16px] leading-[25.6px]">
          {Title}
        </p>
        <div
          className={`w-10 h-6 flex items-center bg-gray-300 rounded-full p-1 cursor-pointer ${
            isToggled ? "bg-purple-600" : "bg-gray-300"
          }`}
          onClick={handleToggle}
        >
          {/* Circle inside the toggle */}
          <div
            className={`bg-white w-4 h-4 rounded-full shadow-md transform ${
              isToggled ? "translate-x-5" : "translate-x-0"
            } transition`}
          ></div>
        </div>
      </div>
      <p className="text-[#9B9B9B] font-sora text-[14px] md:w-[414px]">
        {content}
      </p>
    </div>
  );
};

export default AlertBox;
