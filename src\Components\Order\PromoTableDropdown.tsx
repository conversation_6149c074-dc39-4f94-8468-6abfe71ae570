/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef } from 'react';

interface ModalProps {
  promoCode: any;
  position: { top: number };
  onClose: () => void;
  onEditPromo: (row: any) => void;
  onDeactivatePromo: (row: any) => void;
  onDeletePromo: (row: any) => void;
}

const PromoTableDropdown: React.FC<ModalProps> = ({
  promoCode,
  position,
  onClose,
  onEditPromo,
  onDeactivatePromo,
  onDeletePromo,
}) => {
  const modalRef = useRef<HTMLDivElement | null>(null);

  const handleOverlayClick = (event: any) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      onClose();
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleOverlayClick);
    return () => {
      document.removeEventListener('mousedown', handleOverlayClick);
    };
  }, []);

  return (
    <div
      ref={modalRef}
      className="absolute bg-white rounded-2xl font-sora border border-gray-300 shadow-lg  w-[190px]   lg:left-[80%] "
      style={{ top: `${position.top}px` }}>
      <div className="cursor-pointer">
        <p
          onClick={() => onEditPromo(promoCode)}
          className="py-2 px-4 text-[#5B5B5B]  rounded-t-2xl font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          Edit
        </p>
        <p
          onClick={() => onDeactivatePromo(promoCode)}
          className="py-2 px-4 text-[#5B5B5B] font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          Deactivate
        </p>
        <p
          onClick={() => onDeletePromo(promoCode)}
          className="py-2 px-4 text-[#5B5B5B] font-normal rounded-b-2xl hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          Delete
        </p>
      </div>
    </div>
  );
};

export default PromoTableDropdown;
