/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ArrowDown,
  ArrowUp,
  Box,
  DollarCircle,
  InfoCircle,
} from 'iconsax-react';
import AnalyticCard from '../../Constants/AnalyticCard';
import DateHead from './DateHead';
import Progressbar from '../../Constants/Progressbar';
import { useEffect } from 'react';

const FinancialHealth = ({
  data,
  isLoading,
  isError,
  formattedEndDate,
  formattedStartDate,
}: any) => {
  useEffect(() => {
    document.title = 'Financial Health - Analytics';
  });
  return (
    <div>
      <DateHead />
      {isError ? (
        <h2 className="flex justify-center items-center py-16">
          An Error Occurred, Please Refresh
        </h2>
      ) : (
        <>
          {' '}
          <div className="flex flex-col lg:flex-row items-center gap-6 -mt-7">
            <AnalyticCard
              title="Gross Profit "
              churnRate={
                '₦' + data?.grossProfitData?.GrossProfit?.toLocaleString()
              }
              percentageChange={data?.grossProfitData?.grossProfitChange || 0}
              description={`Total revenue after deducting product costs between Your average fulfilment time of your customers orders from  ${formattedStartDate} to ${formattedEndDate}`}
              hasMaxWidth={true}
              icon={<DollarCircle size="16" color="#1976D2" variant="Bold" />}
              backgroundColor="#E8F2FB"
              isLoading={isLoading}
              isPositive={data?.grossProfitData?.grossProfitChange < 0}
            />
            <AnalyticCard
              title="Net Profit"
              churnRate={
                '₦' + data?.netProfitData?.currentNetProfit?.toLocaleString() ||
                'N/A'
              }
              percentageChange={data?.netProfitData?.netProfitChange || 0}
              description={`Your average fulfilment time of your customers orders from  ${formattedStartDate} to ${formattedEndDate} `}
              hasMaxWidth={true}
              icon={<DollarCircle size="16" color="#1976D2" variant="Bold" />}
              backgroundColor="#E8F2FB"
              isLoading={isLoading}
              isPositive={data?.netProfitData?.netProfitChange < 0}
            />
            <AnalyticCard
              title="Profit Margin"
              churnRate={data?.profitMarginData?.currentProfitMargin?.toLocaleString()}
              percentageChange={data?.profitMarginData?.profitMarginChange || 0}
              isPositive={data?.profitMarginData?.profitMarginChange < 0}
              description={`Your average fulfilment time of your customers orders from  ${formattedStartDate} to ${formattedEndDate}`}
              hasMaxWidth={true}
              icon={<DollarCircle size="16" color="#1976D2" variant="Bold" />}
              backgroundColor="#E8F2FB"
              isLoading={isLoading}
            />
          </div>
          <div className="mt-6 flex flex-col lg:flex-row items-center lg:items-start justify-center  gap-6">
            <div className="border rounded-xl py-4 px-6 max-w-[448px] w-full">
              <div className="flex gap-4 items-center">
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                ) : (
                  <div className="bg-[#E8F2FB] p-1.5 rounded-full">
                    <Box size="16" color="#4F4CD8" variant="Bold" />
                  </div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                ) : (
                  <p className="font-semibold text-lg text-[#2A2A2A]">
                    Total Inflow
                  </p>
                )}
              </div>
              <div className="flex my-6 justify-between">
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                ) : (
                  <p className="font-semibold text-[#1E1B39] text-2xl">
                    {' '}
                    ₦ {data?.cashflowData?.totalFlow?.toLocaleString()}
                  </p>
                )}
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 rounded-3xl w-[40px] "></div>
                ) : (
                  <p
                    className={`flex items-center rounded-3xl  text-[10px] p-1 ${
                      data?.cashflowData?.inflowPercentageChange <= 0
                        ? 'border-[#DC3545] border'
                        : 'border-[#AEEBBC] border'
                    } `}>
                    <span
                      className={`${
                        data?.cashflowData?.inflowPercentageChange <= 0
                          ? 'text-[#DC3545]'
                          : 'text-[#28A745]'
                      }`}>
                      {data?.cashflowData?.inflowPercentageChange || 0} %
                    </span>
                    {data?.cashflowData?.inflowPercentageChange <= 0 ? (
                      <ArrowDown className="text-[#DC3545]" size="12" />
                    ) : (
                      <ArrowUp className="text-[#28A745]" size="12" />
                    )}
                  </p>
                )}
              </div>
              <Progressbar
                itemName="Inflow"
                level={data?.cashflowData?.inflow.toLocaleString()}
                progressPercentage={100}
                barColor="#AEEBBC"
                isLoading={isLoading}
              />
              <Progressbar
                itemName="Outflow"
                level={data?.cashflowData?.outflow.toLocaleString()}
                progressPercentage={50}
                barColor="#E87883"
                isLoading={isLoading}
              />
            </div>
            <div className="border rounded-xl py-4 px-6  h-fit  max-w-[448px] w-full">
              <div className="flex gap-4 items-center">
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                ) : (
                  <div className="bg-[#E6E5F9] p-1.5 rounded-full">
                    <Box size="16" color="#1976D2" variant="Bold" />
                  </div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                ) : (
                  <p className="font-semibold text-lg text-[#2A2A2A]">
                    Cost Of Current Stock
                  </p>
                )}
              </div>
              <div className="flex my-6 justify-between">
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                ) : (
                  <p className="font-semibold text-[#1E1B39] text-2xl">
                    ₦ {data?.costofcurrentstock?.StockValue?.toLocaleString()}
                  </p>
                )}
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 rounded-3xl w-[40px] "></div>
                ) : (
                  <p
                    className={`flex items-center rounded-3xl  text-[10px] p-1 ${
                      data?.costofcurrentstock?.percentageChange < 0
                        ? 'border-[#DC3545] border'
                        : 'border-[#AEEBBC] border'
                    } `}>
                    <span
                      className={`${
                        data?.costofcurrentstock?.percentageChange < 0
                          ? 'text-[#DC3545]'
                          : 'text-[#28A745]'
                      }`}>
                      {data?.costofcurrentstock?.percentageChange} %
                    </span>
                    {data?.costofcurrentstock?.percentageChange < 0 ? (
                      <ArrowDown className="text-[#DC3545]" size="12" />
                    ) : (
                      <ArrowUp className="text-[#28A745]" size="12" />
                    )}
                  </p>
                )}
              </div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] "></div>
              ) : (
                <div className="text-[#9B9B9B] text-sm font-normal flex gap-2 items-center">
                  <InfoCircle color="#9A99E9" />
                  <span>
                    This shows the total cost of all the products you currently
                    have in stock based.
                  </span>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};
export default FinancialHealth;
