import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App';
import { ToastContainer } from 'react-toastify';
import { QueryClient, QueryClientProvider } from 'react-query';
import { useUserAuthStore } from './store/auth';
// import { unregister } from './service-worker-registration'
import { WebSocketProvider } from './webSocketProvider';
import {  register } from './serviceWorkerRegistration';

// unregister();
register({
  onSuccess: (registration: ServiceWorkerRegistration) => {
    console.log('Service Worker registration successful:', registration);
  },
});
const vendorId = useUserAuthStore.getState().user.vendorId;

// Create a meta tag for vendorId and append it to the <head>
const vendorMeta = document.createElement('meta');
vendorMeta.name = 'vendor-id';
vendorMeta.content = vendorId || 'null'; // Assign default value if undefined
document.head.appendChild(vendorMeta); // Append to <head>
console.log('Meta tag created with vendorId:', vendorMeta);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 60 * 24, // 1 day in milliseconds      refetchOnMount: false,
      cacheTime: 1000 * 60 * 60 * 24,
      refetchOnReconnect: true,
      retry: 3,
    },
  },
});
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <WebSocketProvider vendorId="{vendorId}">
        <App />
        <ToastContainer />
      </WebSocketProvider>
    </QueryClientProvider>
  </StrictMode>
);
