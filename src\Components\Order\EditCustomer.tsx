/* eslint-disable @typescript-eslint/no-explicit-any */

import Dropdown from '../../Constants/DropDown';
import { useState, useEffect, useRef } from 'react';
import InputField from '../../Constants/InputField';
import { orderServices } from './../../utils/Services/Order';
import { useMutation, useQuery } from 'react-query';
import { useUserAuthStore } from '../../store/auth';
import { toast } from 'react-toastify';
import { Notifications } from '../../utils/Services/Notification';

interface CreateOrderProps {
  closeModal: () => void;
  isOpen: boolean;
  profileData: any;
  refetch: any;
  refetchTable:any
}

interface AddCustomerProps {
  vendorId: string;
  userEmail: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  billingAddress: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  shippingAddress: string;
  shippingCity: string;
  shippingState: string;
  shippingZipCode: string;
  country: string;
  saveAddress: string;
  socialMediaHandle: string;
  firstName: string;
  lastName: string;
  itemId: string;
}

const EditCustomer: React.FC<CreateOrderProps> = ({
  closeModal,
  isOpen,
  profileData,
  refetch,
  refetchTable
}) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('no-scroll');
    } else {
      document.body.classList.remove('no-scroll');
    }

    return () => {
      document.body.classList.remove('no-scroll');
    };
  }, [isOpen]);
  const user = useUserAuthStore((state) => state.user);

  const [edit, setEdit] = useState<AddCustomerProps>({
    itemId: '',
    vendorId: user.vendorId,
    userEmail: user.userEmail,
    customerId: '',
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    billingAddress: '',
    billingCity: '',
    billingState: profileData?.billingAddresses?.[0]?.billingState || '',
    country: profileData?.shippingAddresses?.[0]?.shippingCountry || '',
    billingZipCode: '',
    shippingAddress: '',
    shippingCity: '',
    shippingState: profileData?.shippingAddresses?.[0]?.shippingState || '',
    shippingZipCode: '',
    saveAddress: 'Yes',
    socialMediaHandle: '',
    firstName: '',
    lastName: '',
  });
  const editCustomer = useMutation(orderServices.editCustomer);

  useEffect(() => {
    if (profileData) {
      setEdit((prev) => ({
        ...prev,
        itemId: profileData.itemId || prev.itemId,
        customerId: profileData.customerId || prev.customerId,
        customerName: profileData.customerName || prev.customerName,
        customerEmail: profileData.customerEmail || prev.customerEmail,
        customerPhone: profileData.customerPhone || prev.customerPhone,
        billingAddress:
          profileData?.billingAddresses?.[0]?.billingAddress ||
          prev.billingAddress,
        billingCity:
          profileData?.billingAddresses?.[0]?.billingCity || prev.billingCity,
        billingState: profileData.billingState || prev.billingState,
        billingZipCode:
          profileData?.billingAddresses?.[0]?.billingZipCode ||
          prev.billingZipCode,
        shippingAddress:
          profileData?.shippingAddresses?.[0]?.shippingAddress ||
          prev.shippingAddress,
        country: profileData.country || prev.country,
        shippingCity:
          profileData?.shippingAddresses?.[0]?.shippingCity ||
          prev.shippingCity,
        shippingState:
          profileData?.shippingAddresses?.[0]?.shippingState ||
          prev.shippingState,
        shippingZipCode:
          profileData?.shippingAddresses?.[0]?.shippingZipCode ||
          prev.shippingZipCode,
        firstName: profileData.firstName || prev.firstName,
        lastName: profileData.lastName || prev.lastName,
        socialMediaHandle:
          profileData.socialMediaHandle || prev.socialMediaHandle,
      }));
    }
  }, [profileData]);
  const handleSelect = (selectedValue: string, type: 'state' | 'country') => {
    if (type === 'state') {
      setEdit((prev) => ({ ...prev, billingState: selectedValue }));
    } else if (type === 'country') {
      setEdit((prev) => ({ ...prev, country: selectedValue }));
    }
  };
  const handleShippingState = (
    selectedValue: string,
    type: 'state' | 'country'
  ) => {
    if (type === 'state') {
      setEdit((prev) => ({ ...prev, shippingState: selectedValue }));
    } else if (type === 'country') {
      setEdit((prev) => ({ ...prev, country: selectedValue }));
    }
  };

  const statesOfNigeria = [
    'Abia',
    'Adamawa',
    'Akwa Ibom',
    'Anambra',
    'Bauchi',
    'Bayelsa',
    'Benue',
    'Borno',
    'Cross River',
    'Delta',
    'Ebonyi',
    'Edo',
    'Ekiti',
    'Enugu',
    'Gombe',
    'Imo',
    'Jigawa',
    'Kaduna',
    'Kano',
    'Katsina',
    'Kebbi',
    'Kogi',
    'Kwara',
    'Lagos',
    'Nasarawa',
    'Niger',
    'Ogun',
    'Ondo',
    'Osun',
    'Oyo',
    'Plateau',
    'Rivers',
    'Sokoto',
    'Taraba',
    'Yobe',
    'Zamfara',
    'Federal Capital Territory (FCT)',
  ];

  // State to track the toggle switch state
  const [isToggled, setIsToggled] = useState<boolean>(false);

  // Function to handle the toggle
  const handleToggle = () => {
    setIsToggled((prev) => {
      const newToggleState = !prev;

      // If toggling to 'true', copy billing to shipping
      if (newToggleState) {
        setEdit((prevAdd) => ({
          ...prevAdd,
          shippingAddress: prevAdd.billingAddress,
          shippingCity: prevAdd.billingCity,
          shippingState: prevAdd.billingState,
          shippingZipCode: prevAdd.billingZipCode,
        }));
      }

      return newToggleState;
    });
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setEdit((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

   const { refetch: notificationRefetch } = useQuery(
     ["notifications", user.vendorId],
     () => Notifications.getNotification(user.vendorId),
     {
       enabled: !!user.vendorId,
       onSuccess: () => {},
       onError: (error: any) => {
         console.log("notification error:", error);
       },
     }
   );

  const handleSubmit = async () => {
    const payload = {
      itemId: edit.itemId,
      vendorId: edit.vendorId,
      userEmail: edit.userEmail,
      customerId: edit.customerPhone,
      customerName: edit.customerName,
      customerEmail: edit.customerEmail,
      customerPhone: edit.customerPhone,
      billingAddress: edit.billingAddress,
      billingCity: edit.billingCity,
      billingState: edit.billingState,
      billingZipCode: edit.billingZipCode,
      shippingAddress: isToggled ? edit.billingAddress : edit.shippingAddress,
      shippingCity: isToggled ? edit.billingCity : edit.shippingCity,
      shippingState: isToggled ? edit.billingState : edit.shippingState,
      shippingZipCode: isToggled ? edit.billingZipCode : edit.shippingZipCode,
      country: edit.country,
      saveAddress: 'Yes',
      socialMediaHandle: edit.socialMediaHandle,
      firstName: edit.firstName,
      lastName: edit.lastName,
    };

   const allFieldsFilled = Object.entries(payload)
     .filter(
       ([key]) =>
         key !== "billingZipCode" &&
         key !== "shippingZipCode" &&
         key !== "customerEmail"
     ) // Exclude these keys
     .every(
       ([, value]) => value !== "" && value !== null && value !== undefined
     );

    if (!allFieldsFilled) {
      toast.error('Please fill all fields before submitting.');
      return;
    }
    try {
      const response: any = await editCustomer.mutateAsync(payload);
      if (response) {
        toast.success(response?.message || 'Customer Updated Successfully');
        notificationRefetch();
        refetchTable()
        refetch();
        closeModal();
      } else {
        throw new Error('No response returned from API');
      }
    } catch (error) {
      console.error(error);
      toast.error('Failed to add customer');
    }
  };
  const modalRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen]);

  return (
    <div
      ref={modalRef}
      tabIndex={-1}
      onKeyDown={(e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
        }
      }}
      className="fixed font-sora  bg-black/10 top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-1 md:p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? 'translate-x-0 ' : 'translate-x-full'
        }`}>
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]">
            &times;
          </span>
          <div className="overflow-y-auto h-screen scrollbar-none hidescroll ">
            <div className="pl-6 ">
              <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                Edit Customer
              </h2>
              <p className=" text-sm text-[#919191] font-sora ">
                Provide your customer information below
              </p>
            </div>
            <div>
              <div className="p-6 rounded-2xl flex flex-col gap-6 last:mb-[200px]">
                <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                  Customer Profile
                </h4>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Name
                  </p>
                  <InputField
                    id="customer-name"
                    placeholder="Enter customer's name"
                    onChange={handleChange}
                    value={edit.customerName}
                    name="customerName"
                    readOnly={true}
                  />
                </div>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Phone Number
                  </p>
                  <InputField
                    id="customer-phone;"
                    placeholder="************"
                    onChange={handleChange}
                    value={edit.customerPhone}
                    name="customerPhone"
                  />
                </div>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Email (Optional)
                  </p>
                  <InputField
                    id="customer-email"
                    placeholder="Enter customer's email"
                    onChange={handleChange}
                    value={edit.customerEmail}
                    name="customerEmail"
                  />
                </div>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Social Media Handle
                  </p>
                  <InputField
                    id="customer-social"
                    placeholder="Enter any of the customer social media handle"
                    onChange={handleChange}
                    name="socialMediaHandle"
                    value={edit.socialMediaHandle}
                  />
                </div>
                <div className="flex justify-between items-center">
                  <h4 className="font-semibold font-sora text-[#2a2a2a] md:text-base">
                    Customer Billing Address
                  </h4>
                  <div className="flex items-center md:gap-2">
                    <p className="text-[12px] text-primary-neutralt2">
                      same as shipping address
                    </p>
                    <div
                      className={`w-10 h-6 flex items-center bg-gray-300 rounded-full p-1 cursor-pointer ${
                        isToggled ? 'bg-purple-600' : 'bg-gray-300'
                      }`}
                      onClick={handleToggle}>
                      {/* Circle inside the toggle */}
                      <div
                        className={`bg-white w-4 h-4 rounded-full shadow-md transform ${
                          isToggled ? 'translate-x-5' : 'translate-x-0'
                        } transition`}></div>
                    </div>
                  </div>
                </div>

                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Select Country
                  </p>
                  <Dropdown
                    placeholder="Select Country"
                    options={['Nigeria']}
                    onSelect={(selectedCountry) =>
                      handleSelect(selectedCountry, 'country')
                    }
                    value={edit.country}
                  />
                </div>

                <div className="p-0 flex flex-col md:flex-row items-center gap-2.5 relative last:border-b-0 ">
                  <div className="w-full">
                    <p className="text-[14px] font-sora mb-[5px] font-normal text-[#5b5b5b]">
                      First Name
                    </p>
                    <InputField
                      id="first-name"
                      placeholder="Enter First Name"
                      className="w-full"
                      onChange={handleChange}
                      name="firstName"
                      value={edit.firstName}
                      readOnly={true}
                    />
                  </div>
                  <div className="w-full">
                    <p className="text-[14px] font-sora mb-[5px] font-normal text-[#5b5b5b]">
                      Last Name
                    </p>
                    <InputField
                      id="last-name"
                      placeholder="Enter Last Name"
                      className="w-full"
                      onChange={handleChange}
                      value={edit.lastName}
                      name="lastName"
                      readOnly={true}
                    />
                  </div>
                </div>
                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    Address
                  </p>
                  <InputField
                    id="address"
                    placeholder="Enter Street Address"
                    onChange={handleChange}
                    value={edit.billingAddress}
                    name="billingAddress"
                  />
                </div>

                <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                  <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                    City
                  </p>
                  <InputField
                    id="city"
                    placeholder="Enter City Name"
                    onChange={handleChange}
                    value={edit.billingCity}
                    name="billingCity"
                  />
                </div>
                <div className="p-0 flex flex-col md:flex-row items-center gap-2.5 relative last:border-b-0 ">
                  <div className="w-full">
                    <p className="text-[14px] font-sora mb-3 font-normal text-[#5b5b5b]">
                      State
                    </p>
                    <Dropdown
                      placeholder="Select State"
                      onSelect={(selectedState) =>
                        handleSelect(selectedState, 'state')
                      }
                      options={statesOfNigeria}
                      value={edit.billingState}
                    />
                  </div>
                  <div className="w-full">
                    <p className="text-[14px] font-sora mb-3 font-normal text-[#5b5b5b]">
                      Zip Code
                    </p>
                    <InputField
                      id="zip-code"
                      placeholder="Enter zip code"
                      className="w-full"
                      onChange={handleChange}
                      value={edit.billingZipCode}
                      name="billingZipCode"
                    />
                  </div>
                </div>
                {!isToggled && (
                  <div>
                    <div className="flex justify-between items-center">
                      <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                        Customer Shipping Address
                      </h4>
                    </div>
                    <div className="p-0 flex mt-4  flex-col gap-2.5 relative last:border-b-0 ">
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        Address
                      </p>
                      <InputField
                        placeholder="Enter Street Address"
                        onChange={handleChange}
                        value={edit.shippingAddress}
                        name="shippingAddress"
                      />
                    </div>

                    <div className="p-0 mt-4 flex flex-col gap-2.5 relative last:border-b-0 ">
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        City
                      </p>
                      <InputField
                        id="city"
                        placeholder="Enter City Name"
                        onChange={handleChange}
                        value={edit.shippingCity}
                        name="shippingCity"
                      />
                    </div>
                    <div className="p-0 flex flex-col md:flex-row  mt-4 items-center gap-2.5 relative last:border-b-0 mb-[140px]">
                      <div className="w-full">
                        <p className="text-[14px] mb-3 font-sora font-normal text-[#5b5b5b]">
                          State
                        </p>
                        <Dropdown
                          placeholder="Select State"
                          onSelect={(selectedState) =>
                            handleShippingState(selectedState, 'state')
                          }
                          options={statesOfNigeria}
                          value={edit.shippingState}
                        />
                      </div>
                      <div className="w-full ">
                        <p className="text-[14px] font-sora mb-3 font-normal text-[#5b5b5b]">
                          Zip Code
                        </p>
                        <InputField
                          id="zip-code"
                          placeholder="Enter zip code"
                          className="w-full"
                          onChange={handleChange}
                          value={edit.shippingZipCode}
                          name="shippingZipCode"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2  md:p-10">
              <div className="flex w-full px-7.5 gap-2.5">
                <button
                  onClick={closeModal}
                  className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2">
                  Back
                </button>
                <button
                  className={`${
                    editCustomer.isLoading ? 'opacity-50' : ''
                  }  bg-[#4f4cd8] w-full text-[#fcfcfc] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 h-[49px] rounded-2xl cursor-pointer flex-2`}
                  onClick={handleSubmit}
                  disabled={editCustomer.isLoading}>
                  {editCustomer.isLoading ? 'Editing ...' : 'Edit Customer'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default EditCustomer;
