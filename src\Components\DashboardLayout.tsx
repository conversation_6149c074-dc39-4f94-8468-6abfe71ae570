import SecondHeader from "./SecondHeader";
import Sidebar from "./Sidebar";
import Topbar from "./Topbar";
import { Outlet } from "react-router-dom";
import { Messages2, Send } from "iconsax-react";
import { useState, useEffect ,useRef } from "react";
import golden from "../assets/golden.svg";
import blue from "../assets/blue.svg";
import stars from "../assets/stars.svg";
import burtons from "../assets/Male Memojis.svg";
import burtonStar from "../assets/starsBurtons.svg";
import venStar from "../assets/venStar.svg";
import burtonsChatComb from "../assets/burtonsComb.svg";
import smiley from "../assets/smiley.svg";
import onlineBurtons from "../assets/onlineBurtons.svg";
import venComb from '../assets/venComb.svg'
import venComb2 from '../assets/venComb2.svg'
import '../calender.css'
import HelpPage from "./Help/HelpPage";
import ConfirmLogout from "./ConfirmLogout";
import { subscribeToPushNotifications, isPushNotificationSupported } from "../utils/pushNotificationService";
import { useUserAuthStore } from "../store/auth";
import { toast } from "react-toastify";
import DraggableChatButton from "../utils/Dragable";

// Chat Screen Component
const ChatScreen = ({
  assistant,
  onClose,
}: {
  assistant: string;
  onClose: () => void;
}) => {
  const [messages, setMessages] = useState([
    {
      sender: assistant,
      content: `Hello! I'm ${assistant}, and I'm excited to be your business manager. Let's get started on making your business operations smooth and efficient. Whether you need help adding products, adjusting inventory, or analyzing your sales data. What do you need?`,
      timestamp: new Date().toLocaleString(),
    },
  ]);

  const [inputMessage, setInputMessage] = useState("");
  const messagesEndRef = useRef<HTMLDivElement | null>(null);

  // Function to scroll to the bottom of the messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Auto-scroll every time the messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (inputMessage.trim() === "") return;

    const newMessage = {
      sender: "User",
      content: inputMessage,
      timestamp: new Date().toLocaleString(),
    };

    // Add user's message to the chat
    setMessages((prevMessages) => [...prevMessages, newMessage]);
    setInputMessage(""); // Clear input field

    // Simulate assistant's response after a short delay
    setTimeout(() => {
      const assistantResponse = {
        sender: assistant,
        content: `I received your message: "${inputMessage}". How can I assist further?`,
        timestamp: new Date().toLocaleString(),
      };
      setMessages((prevMessages) => [...prevMessages, assistantResponse]);
    }, 1000);
  };

  return (
    <div className="fixed right-0 top-0 h-full md:w-[580px] bg-[#1D1D23] z-[999999999999999] flex flex-col justify-between">
      <div className="relative h-full w-full">
        {/* Background elements */}
        <div className="absolute bottom-0 right-0">
          {assistant === "Burtons" ? (
            <img src={golden} alt="honeycomb" />
          ) : (
            <img src={venComb2} alt="honeycomb" />
          )}
        </div>
        <div className="absolute">
          {assistant === "Burtons" ? (
            <img src={burtonsChatComb} alt="honeycomb" />
          ) : (
            <img src={venComb} alt="honeycomb" />
          )}
        </div>

        <div className="px-[16px] h-full py-[20px] flex flex-col gap-4">
          {/* Close button */}
          <div className="cursor-pointer">
            <h1
              onClick={onClose}
              className="text-primary-baseWhite font-sora font-semibold text-[20px] text-right"
            >
              X
            </h1>
          </div>

          {/* Chat Container */}
          <div
            className={`px-[24px] py-[16px] backdrop-blur-[5px] min-h-[620px] ${
              assistant === "Burtons"
                ? "bg-primary-purple/10"
                : "bg-[#FF99001A]/10"
            } z-30 flex flex-col gap-4 p-6 rounded-2xl`}
          >
            {/* Chat Header */}
            <div className="p-4 border-[1px] border-primary-neutral500 rounded-3xl text-white">
              <div className="flex items-center gap-3">
                <img
                  src={assistant === "Burtons" ? burtons : burtons}
                  alt={assistant}
                  className="w-10 h-10"
                />
                <div>
                  <h2 className="text-[16px] leading-[19.2px] text-primary-baseWhite font-semibold">
                    {assistant}
                  </h2>
                  <p className="text-[12px] font-sora leading-[19.2px] text-primary-neutral300">
                    Always Available
                  </p>
                </div>
              </div>
            </div>

            {/* Chat Messages */}
            <div className="flex-grow overflow-y-auto scrollbar-hidden p-4">
              {messages.map((message, index) => (
                <div
                  key={index}
                  className={`mb-4 gap-2 items-start flex ${
                    message.sender === "User" ? "justify-end" : "justify-start"
                  }`}
                >
                  {/* Optional: Avatar for Assistant */}
                  <div>
                    {message.sender !== "User" && (
                      <img src={onlineBurtons} alt="assistant-avatar" />
                    )}
                  </div>

                  {/* Chat bubble for each message */}
                  <div
                    className={`max-w-[362px] px-[16px] py-[8px] text-primary-neutral100 font-sora text-[14px] gap-2 ${
                      message.sender === "User"
                        ? "text-primary-baseWhite border-[1px] border-primary-neutral500 rounded-except-tr"
                        : assistant === "Burtons"
                        ? "bg-primary-purple leading-[22.4px] bg-opacity-40 rounded-except-tl"
                        : "bg-[#FF990040] leading-[22.4px] bg-opacity-40 rounded-except-tl"
                    }`}
                  >
                    <p>{message.content}</p>
                    <span className="text-xs text-gray-400 block mt-1">
                      {message.timestamp}
                    </span>
                  </div>
                </div>
              ))}
              {/* Empty div to scroll to the bottom */}
              <div ref={messagesEndRef}></div>
            </div>

            {/* Input Field */}
            <div className="mt-auto ">
              <div className="flex gap-3 items-center border-[0.1px] border-primary-neutral500 rounded-3xl p-4 w-full">
                <div>
                  <img src={smiley} alt="smiley" />
                </div>
                <input
                  placeholder="Start Typing..."
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
                  className="bg-transparent outline-none w-full font-sora text-[14px] font-normal text-primary-neutral100"
                />
                <Send
                  onClick={handleSendMessage}
                  className="ml-auto cursor-pointer text-primary-neutral100"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};


// Dashboard Layout
const DashboardLayout = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [chatOpen, setChatOpen] = useState(false);
  const [selectedAssistant, setSelectedAssistant] = useState<
    "Burtons" | "Ven" | null
    >(null);
    const [openLogout, setOpenLogout] = useState<boolean>(false);

    const handleLogout = () => {
      setOpenLogout((prev) => !prev);
    };

  const togglePanel = () => {
    setIsOpen(!isOpen);
    setChatOpen(false); // Close chat when toggling panel
  };
  const [showHelp, setShowHelp] = useState<boolean>(false)

  const handleHelp = () => {
    setShowHelp((prev)=>!prev)
  }

  const openChat = (assistant: "Burtons" | "Ven") => {
    setSelectedAssistant(assistant);
    setChatOpen(true);
  };

  const vendorId = useUserAuthStore((state) => state.user.vendorId);
  const subscriptionAttemptedRef = useRef(false);

  // Handle push notification subscription
  useEffect(() => {
    const subscribe = async () => {
      // Only proceed if we have a vendor ID and haven't attempted subscription yet
      if (!vendorId || subscriptionAttemptedRef.current) return;
      
      // Immediately mark as attempted to prevent concurrent calls
      subscriptionAttemptedRef.current = true;

      try {
        console.log("Attempting to set up push notifications for vendor:", vendorId);

        // Check if push notifications are supported
        if (!isPushNotificationSupported()) {
          console.log("Push notifications not supported in this browser");
          return;
        }

        // Wait for service worker to be ready with a timeout
        const maxWaitTime = 5000; // 5 seconds
        const startTime = Date.now();

        let serviceWorkerReady = false;
        while (!serviceWorkerReady && (Date.now() - startTime < maxWaitTime)) {
          if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            serviceWorkerReady = true;
          } else {
            await new Promise(resolve => setTimeout(resolve, 200));
          }
        }

        if (!serviceWorkerReady) {
          console.warn("Service worker not ready within timeout period");
          return;
        }

        // Now we can proceed with subscription
        const swRegistration = await navigator.serviceWorker.ready;
        const existingSubscription = await swRegistration.pushManager.getSubscription();

        if (!existingSubscription) {
          console.log("No existing subscription found, subscribing...");
          const result = await subscribeToPushNotifications(vendorId);
          console.log("Subscription result:", result ? "Success" : "Failed");
          if (result) {
            toast.success("Push notifications enabled!");
          } else {
            toast.warn("Failed to subscribe to push notifications.");
          }
        } else {
          console.log("User already has an active push subscription");
        }
      } catch (error) {
        console.error("Error during push notification setup:", error);
        toast.error("Error setting up push notifications.");
      }
    };

    subscribe();
  }, [vendorId]);

  const talkToUs = () => {
    const phone = "+2349014588261";
    const url = `https://wa.me/${phone}`;
    window.open(url, "_blank");
  };

  return (
    <div className="flex bg-[#FCFCFC]">
      {/* Chat-bot Button */}

      <div
        // onClick={togglePanel}
        onClick={handleHelp}
        className="bg-[#FF9900] hidden cursor-pointer rounded-full w-30 h-30 z-[9999999] fixed bottom-20 right-[30px] p-4 md:hidden items-center justify-center"
      >
        <Messages2 size="32" />
      </div>
      {/* <div onClick={talkToUs} className="fixed animate-bounce cursor-pointer bottom-5 md:bottom-20 right-5 md:right-5 z-[9999999] ">
        <p className="shadow-md shadow-gray-200 font-sora text-[12px] border-[#075E54] border-[1px] rounded-[32px] flex gap-2 items-center py-1 px-2 bg-[#fff]"> <span className="hidden md:block">Chat with us </span><Whatsapp color="#128C7E" variant="Bold" size={32}/></p>

      </div> */}
      <DraggableChatButton talkToUs={talkToUs}/>

      {/* Overlay */}
      <div
        onClick={togglePanel}
        className={`fixed top-0 right-0 h-full w-full bg-black bg-opacity-30 transform transition-transform duration-500 ease-in-out ${
          isOpen
            ? "translate-x-0 z-[99999999]"
            : "translate-x-full z-[999999999]"
        }`}
      ></div>

      {/* Chat-bot Panel or Chat Screen */}
      <div
        className={`w-full md:w-[580px] h-full  fixed right-0  transform transition-transform duration-500 ease-in-out ${
          isOpen
            ? "translate-x-0 z-[999999999999999999]"
            : "translate-x-full z-[999999999999999]"
        }`}
      >
        {/* Assistant Selection Panel */}
        {!chatOpen ? (
          <div className="bg-[#1D1D23] overflow-y-auto scrollbar-hidden w-full h-full relative">
            {/* Honeycomb Background */}
            <div className="absolute">
              <img src={blue} alt="honeycomb" />
            </div>
            <div className="absolute bottom-0 right-0">
              <img src={golden} alt="honeycomb" />
            </div>

            <div className="px-[16px] py-[20px] flex flex-col gap-5">
              {/* Close Button */}
              <div onClick={togglePanel} className="cursor-pointer">
                <h1 className="text-primary-baseWhite font-sora font-semibold text-[20px] text-right">
                  X
                </h1>
              </div>

              {/* Assistant Choices */}
              <div className="backdrop-blur-[5px] bg-white/10 z-30 text-center flex flex-col gap-2 lg:gap-4 items-center p-3 lg:p-6 rounded-2xl">
                <img src={stars} alt="stars" />
                <h4 className="text-[20px] lg:text-[32px] font-sora font-bold text-primary-baseWhite lg:leading-[38.4px]">
                  Choose Your Assistant
                </h4>
                <p className="max-w-[436px] text-[14px] font-sora font-light text-primary-baseWhite leading-[22.4px]">
                  Welcome! Please choose your virtual assistant to guide you
                  through our platform and help manage your business
                  efficiently.
                </p>
              </div>

              {/* Burtons Option */}
              <div className="backdrop-blur-[5px] bg-primary-purple/20 z-30 text-center flex flex-col gap-2 lg:gap-4 items-center p-3 lg:p-6 rounded-2xl">
                <div className="flex flex-col md:flex-row gap-3 justify-between w-full">
                  <div className="flex items-center gap-4">
                    <img src={burtons} alt="burtons" />
                    <div className="text-left">
                      <h5 className="font-sora text-primary-baseWhite text-[24px] leading-[28.8px]">
                        Burtons
                      </h5>
                      <p className="text-primary-neutral300 text-[12px] font-sora">
                        Business Guru AI
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => openChat("Burtons")}
                    className="text-primary-neutral100 flex gap-3 items-center border-[1px] py-[8px] px-[16px] border-primary-neutral500 rounded-3xl font-sora"
                  >
                    Hire Burtons <img src={burtonStar} alt="" />
                  </button>
                </div>
                <p className="text-left text-[16px] font-sora font-light text-primary-baseWhite">
                  Hi there! I'm Burtons, your business-savvy virtual manager.
                  With my analytical mindset and organized approach, I'll help
                  you streamline your operations and boost your business
                  performance.
                </p>
              </div>

              {/* Ven Option */}
              <div className="backdrop-blur-[5px] bg-[#FF99001A]/20 z-30 text-center flex flex-col gap-2 lg:gap-4 items-center p-3 lg:p-6 rounded-2xl">
                <div className="flex flex-col md:flex-row gap-4 justify-between w-full">
                  <div className="flex items-center gap-4">
                    <img src={burtons} alt="burtons" />
                    <div className="text-left">
                      <h5 className="font-sora text-primary-baseWhite text-[24px] leading-[28.8px]">
                        Ven
                      </h5>
                      <p className="text-primary-neutral300 text-[12px] font-sora">
                        Business Expert AI
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => openChat("Ven")}
                    className="min-w-[180px] py-[8px] md:justify-center text-primary-neutral100 flex gap-3 items-center border-[1px] px-[16px] border-primary-neutral500 rounded-3xl font-sora"
                  >
                    Hire Ven <img src={venStar} alt="" />
                  </button>
                </div>
                <p className="text-left text-[16px] font-sora font-light text-primary-baseWhite">
                  Hello! I'm Ven, your dedicated business manager with a knack
                  for detail and a flair for strategy. I excel at identifying
                  growth opportunities and providing practical solutions to
                  everyday challenges.
                </p>
              </div>
            </div>
          </div>
        ) : (
          // Chat Screen
          <ChatScreen assistant={selectedAssistant!} onClose={togglePanel} />
        )}
      </div>

      {/* Sidebar and Other Components */}
      <div className="hidden md:block">
        <Sidebar logoutControll={handleLogout} />
      </div>
      <div className="flex flex-col w-full">
        <div className="hidden md:block">
          <Topbar />
        </div>
        <div>
          {/* Small screen header content */}
          <SecondHeader />
        </div>
        <div className="md:ml-[270px] mt-44 overflow-hidden md:mr-[20px] md:mt-[30px] max-w-full">
          <Outlet />
        </div>
      </div>
      {showHelp && <HelpPage isOpen={showHelp} closeModal={handleHelp} />}

      {openLogout && (
        <ConfirmLogout isOpen={openLogout} closeModal={handleLogout} />
      )}
    </div>
  );
};

export default DashboardLayout;
