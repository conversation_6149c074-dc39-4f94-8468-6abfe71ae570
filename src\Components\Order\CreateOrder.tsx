/* eslint-disable @typescript-eslint/no-explicit-any */
import Dropdown from "../../Constants/DropDown";
import DateInputPicker from "../../Constants/DateInputPicker";
import { useState, useEffect, useRef } from "react";
import InputField from "../../Constants/InputField";
import AddCustomer from "./AddCustomer";
import AddProduct from "../Inventory/AddProduct";
import ContactDropdown from "../../Constants/ContactDropdown";
import {
  AddCircle,
  ArrowDown2,
  ArrowUp2,
  Minus<PERSON>ir<PERSON>ce,
  Trash,
} from "iconsax-react";
import { FormatPrice } from "../../utils/FormatPrice";
import PreviewOrder from "./PreviewOrder";
import { useQuery } from "react-query";
import { inventoryServices } from "./../../utils/Services/InventoryServices";
import { useUserAuthStore } from "../../store/auth";
import CreateDelivery from "../../Constants/CreateDelivery";
import { orderServices } from "./../../utils/Services/Order";
import { toast } from "react-toastify";
import thumbnail from '../../assets/video_thumbnail.jpg'

interface CreateOrderProps {
  closeModal: () => void;
  isOpen: boolean;
  refetch: any;
}

interface OrderDetails {
  vendorId: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  subDomain: string;
  cartProducts: any[];
  vendorEmail: string;
  billingAddress: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  country: string;
  paymentMethod: string;
  channel: string;
  promoCode: string;
  deliveryFee: number;
  orderDate: Date;
  paymentStatus: string;
  amountPaid: number;
  isVendorCreated: "No";
  saveAddress: string;
  socialMediaHandle: string;
  firstName: string;
  lastName: string;
}

const CreateOrder: React.FC<CreateOrderProps> = ({
  closeModal,
  isOpen,
  refetch,
}) => {
  const user = useUserAuthStore((state) => state.user);
  const [addCustomerPanel, setAddCustomerPanel] = useState<boolean>(false);
  const [addDeliveryPanel, setAddDeliveryPanel] = useState<boolean>(false);
  const [addProduct, setAddProduct] = useState<boolean>(false);
  const [isMoreDetails, setIsMoreDetails] = useState<number | null>(null);
  const [previewOrder, setPreviewOrder] = useState<boolean>(false);
  const tabs = ["Not Yet Paid", "Paid", "Installment"];
  const [activeTab, setActiveTab] = useState("Not Yet Paid");
  const [allData, setAllData] = useState<any>([]);
  const [orderData, setOrderData] = useState<OrderDetails | null>(null);
  const [orderDetail, setOrderDetail] = useState<OrderDetails>({
    vendorId: user.vendorId,
    vendorEmail: user.userEmail,
    customerId: "",
    customerName: "",
    customerEmail: "",
    customerPhone: "",
    subDomain: "",
    cartProducts: [],
    billingCity: "",
    billingAddress: "",
    billingState: "",
    billingZipCode: "",
    country: "",
    paymentMethod: "",
    channel: "",
    promoCode: "",
    deliveryFee: 0,
    orderDate: new Date(),
    paymentStatus: "Not Yet Paid",
    amountPaid: 0,
    isVendorCreated: "No",
    saveAddress: "",
    socialMediaHandle: "",
    firstName: "",
    lastName: "",
  });

  const [deliveryFeeData, setDeliveryFeeData] = useState<any>([]);
  const [quantities, setQuantities] = useState<{ [key: string]: number }>({});

  // Manage scroll lock when any modal is open
  useEffect(() => {
    if (isOpen || addCustomerPanel || addDeliveryPanel) {
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    return () => {
      document.body.classList.remove("no-scroll");
    };
  }, [isOpen, addCustomerPanel, addDeliveryPanel]);

  // Function to open AddCustomer modal
  const handleCustomer = () => {
    setAddCustomerPanel(true);
  };
  const handleAddProduct = () => {
    setAddProduct(true);
  };

  const handleSelectProduct = (product: any) => {
    const selectedProduct = allData.find((p: any) => p.productName === product);

    if (selectedProduct) {
      if (selectedProduct.status === "Out of Stock") {
        toast.error("This product is out of stock and cannot be picked.");
        return;
      }
      setOrderDetail((prev) => ({
        ...prev,
        cartProducts: [
          ...prev.cartProducts,
          {
            product_id: selectedProduct.productId,
            product_name: selectedProduct.productName,
            discountedPrice: selectedProduct.discountedPrice,
            sellingPrice: selectedProduct.sellingPrice,
            discountPercentage: selectedProduct.discountPercentage,
            discountAmount: selectedProduct.discountAmount,
            quantity: 1,
          },
        ],
      }));
    }
  };

  const updateProductQuantity = (productId: string, delta: number) => {
    setOrderDetail((prevOrderDetail) => {
      const updatedProducts = prevOrderDetail.cartProducts
        .map((product) => {
          if (product.product_id === productId) {
            const newQuantity = product.quantity + delta;

            if (newQuantity > 0) {
              setQuantities((prevQuantities) => ({
                ...prevQuantities,
                [productId]: newQuantity,
              }));

              return {
                ...product,
                quantity: newQuantity,
              };
            }
            // If new quantity is less than 1, we return null to indicate removal
            return null;
          }
          return product;
        })
        .filter((product) => product !== null); // Remove products with null value

      return { ...prevOrderDetail, cartProducts: updatedProducts };
    });

    setQuantities((prevQuantities) => {
      const newQuantities = { ...prevQuantities };
      if (newQuantities[productId] + delta < 1) {
        delete newQuantities[productId];
      }
      return newQuantities;
    });
  };

  const handleIncreaseQuantity = (productId: string) => {
    updateProductQuantity(productId, 1);
  };

  const handleDecreaseQuantity = (productId: string) => {
    updateProductQuantity(productId, -1);
  };

  const handleDelivery = () => {
    setAddDeliveryPanel(true);
  };

  const handleDateChange = (date: Date) => {
    setOrderDetail((prevOrderDetail) => ({
      ...prevOrderDetail,
      orderDate: date,
    }));
  };

  // Function to close AddCustomer modal
  const closeCustomerPanel = () => {
    setAddCustomerPanel(false);
    setAddDeliveryPanel(false);
  };

  const closeAddProduct = () => {
    setAddProduct(false);
  };

  // show order details
  const handleMoreDetails = (productId: number) => {
    setIsMoreDetails((prevId) => (prevId === productId ? null : productId));
  };

  // close Preview
  const closePreview = () => {
    setPreviewOrder(false);
  };

  // Other CreateOrder logic
  const handleSelect = (value: string) => {
    console.log("Selected:", value);
  };

  const handleChannelSelect = (value: string) => {
    setOrderDetail((prev) => ({
      ...prev,
      channel: value,
    }));
  };

  const handlePaid = (value: string) => {
    setOrderDetail((prev) => ({
      ...prev,
      paymentMethod: value,
    }));
  };

  // Function to handle tab switching
  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
    setOrderDetail((prevDetails) => ({
      ...prevDetails,
      paymentStatus: tab,
    }));
  };

  useEffect(() => {
    setActiveTab("Not Yet Paid");
    setOrderDetail((prevDetails) => ({
      ...prevDetails,
      paymentStatus: "Not Yet Paid",
    }));
  }, []);

  const handleAmountPaid = (amount: string) => {
    const parsedAmount = parseFloat(amount);
    if (!isNaN(parsedAmount)) {
      setOrderDetail((prevDetails) => ({
        ...prevDetails,
        amountPaid: parsedAmount,
      }));
    } else {
      console.error("Invalid amount entered");
    }
  };

  const { data, refetch: refetchproduct } = useQuery(
    ["products", user.vendorId],

    () => inventoryServices.getAllProducts(user.vendorId),
    {
      enabled: !!user.vendorId,
    }
  );
  useEffect(() => {
    if (data) {
      setAllData(data.products || []);
      console.log('all data:', data.products);
    }
  }, [data]);
  console.log(data);
  const { data: fee, refetch: fetchDelivery } = useQuery(
    ["deliveryFee", user.vendorId],
    () => orderServices.getDeliveryFee(user.vendorId),
    {
      enabled: !!user.vendorId,
    }
  );

  useEffect(() => {
    if (fee) {
      setDeliveryFeeData(fee.deliveryFees || []);
    }
  }, [fee]);

  const handleDeliveryChange = (data: string) => {
    const selected = deliveryFeeData.find(
      (x: any) =>
        `${x.deliveryTitle} - ₦ ${Number(x?.deliveryValue)?.toLocaleString()}` ===
        data
    );
    if (selected) {
      setOrderDetail((prev) => ({
        ...prev,
        deliveryFee: selected.deliveryValue,
      }));
    }
  };

  const handleCustomerSelect = (customerData: any) => {
    const [firstName, ...lastNameArray] = customerData.customerName.split(" ");
    const lastName = lastNameArray.join(" ");
    const shippingAddresses = customerData.shippingAddresses?.map(
      (address: any) => ({
        shippingCity: address.shippingCity,
        shippingState: address.shippingState,
        shippingZipCode: address.shippingZipCode,
        shippingAddress: address.shippingAddress,
      })
    );

    setOrderDetail((prev) => ({
      ...prev,
      customerId: customerData.customerId,
      customerName: customerData.customerName,
      customerEmail: customerData.emailAddress,
      customerPhone: customerData.phoneNumber,
      billingAddress: customerData.address,
      country: customerData.country,
      billingZipCode: customerData.zipCode,
      billingState: customerData.state,
      billingCity: customerData.city,
      shippingAddresses: shippingAddresses || [],
      firstName,
      lastName,
    }));
  };

  const handlePreview = () => {
    setOrderData(orderDetail);
    setPreviewOrder(true);
  };

  const modalRef = useRef<HTMLDivElement | null>(null);
  // Focus on the modal when it opens
  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen]);

  const isSaveDisabled =
    !orderDetail.customerId ||
    !orderDetail.customerName ||
    !orderDetail.customerPhone ||
    !orderDetail.billingAddress ||
    orderDetail.cartProducts.length === 0;

  return (
    <div
      ref={modalRef}
      tabIndex={-1}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          e.preventDefault();
        }
      }}
      className="fixed inset-0 z-50 font-sora bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm transition-transform duration-500 ease-in-out"
    >
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white z-30 p-1 md:p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        {/* Close button for CreateOrder */}
        <div>
          <span
            onClick={closeModal}
            className="text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]"
          >
            &times;
          </span>

          {/* Ensure this div takes full height minus the close button */}
          <div className="h-[calc(100vh-80px)] overflow-y-auto scrollbar-none hidescroll">
            <div className="pl-6">
              <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold">
                Create New Order
              </h2>
              <p className="text-sm text-[#919191] font-sora">
                Provide the Information below to create an order
              </p>
            </div>

            {/* Customer Section */}
            <div className="p-6 rounded-2xl flex flex-col gap-3">
              <div className="w-full">
                <ContactDropdown onCustomerSelect={handleCustomerSelect} />
              </div>

              <div className="flex items-center  justify-between">
                <p className=" text-[12px] md:text-[14px] text-primary-neutralt2">
                  Is the buyer a new customer?
                </p>
                <button
                  onClick={handleCustomer}
                  className="text-primary-purple text-[12px] md:text-[14px] font-semibold"
                >
                  + Add New Customer
                </button>
              </div>
            </div>

            {/* Date Section */}
            <div className="p-6 flex flex-col gap-6">
              <h4 className="font-semibold font-sora text-base">
                When are they buying?
              </h4>
              <div className="flex flex-col gap-2.5">
                <p className="text-[14px] font-sora text-[#5b5b5b]">Date</p>
                <DateInputPicker
                  selectedDate={orderDetail.orderDate}
                  onDateChange={handleDateChange}
                />
              </div>
            </div>

            {/* Product Section */}
            <h4 className="font-semibold font-sora text-base p-6">
              What are they buying?
            </h4>
            <div className="md:p-6 p-4 flex bg-[#F5F5F5] rounded-2xl flex-col gap-3">
              <p className="font-sora text-[14px] text-primary-neutralt2">
                Product
              </p>
              <Dropdown
                onSelect={handleSelectProduct}
                options={
                  allData.map((product: any) => product.productName) || []
                } // Use just the product name
                placeholder="Select product from your inventory"
                border="bg-[#EAF7ED]"
              />
              <div className="flex items-center justify-between">
                <p className="md:text-[14px] text-[12px] text-primary-neutralt2">
                  Is this a new product?
                </p>
                <button
                  onClick={handleAddProduct}
                  className="text-primary-purple text-[12px] md:text-[14px] font-semibold"
                >
                  + Add New Product
                </button>
              </div>

              {orderDetail.cartProducts.length > 0 && (
                <div className="bg-primary-baseWhite  p-4 md:p-6 rounded-2xl flex flex-col gap-3">
                  {orderDetail.cartProducts.map((product, index) => (
                    <div
                      key={index}
                      className="border-b-[1px] last:border-none justify-between pb-4 items-start gap-4 flex flex-col"
                    >
                      <div className="flex flex-col md:flex-row gap-4 md:gap-0 items-start w-full md:justify-between">
                        <div className="flex items-center gap-2">
                          <div className="flex-shrink-0">
                            <img
                              className="w-[100px] h-[100px] object-contain rounded-3xl"
                              src={
                                allData.find(
                                  (p: any) => p.productId === product.product_id
                                )?.productImage
                              }onError={(e: any) => {
                                e.target.src = thumbnail; // Fallback to thumbnail on error
                              }}
                              alt="productImage"
                            />
                          </div>
                          <div className="flex flex-col gap-3">
                            <p className="text-[#919191] font-sora text-[12px]">
                              Product
                            </p>
                            <h4 className="text-[16px] w-[162px] md:w-full  truncate font-sora text-primary-neutralt1">
                              {product.product_name}
                            </h4>
                            {isMoreDetails === product.product_id ? (
                              ""
                            ) : (
                              <div className="flex w-[120px] items-center justify-evenly py-1 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                                <button
                                  type="button"
                                  onClick={() =>
                                    handleDecreaseQuantity(product.product_id)
                                  }
                                >
                                  {product.quantity <= 1 ? (
                                    <Trash
                                      size={22}
                                      className="text-primary-neutralt1"
                                    />
                                  ) : (
                                    <MinusCirlce className="text-primary-neutralt1" />
                                  )}
                                </button>

                                <p className="text-[14px] font-sora text-primary-neutralt1">
                                  {quantities[product.productId] ??
                                    product.quantity}
                                </p>
                                <button
                                    onClick={() =>
                                      
                                    handleIncreaseQuantity(product.product_id)
                                  }
                                  type="button"
                                  className="z-10 cursor-pointer"
                                >
                                  <AddCircle className="  text-primary-neutralt1 " />
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex md:flex-col justify-between w-full md:h-[100px] items-end text-right">
                          {isMoreDetails === product.product_id ? (
                            ""
                          ) : (
                            <div className="flex w-full gap-1 md:gap-1 md:flex-col">
                              <h4 className="font-sora text-primary-neutralt2 text-[16px] font-semibold">
                                ₦
                                {FormatPrice(
                                  product.discountedPrice || 0
                                  // *
                                  //   (quantities[product.product_id] || 1)
                                )}
                              </h4>
                              <p className="text-[14px] text-[#DC3545] line-through">
                                ₦{" "}
                                {FormatPrice(
                                  product.sellingPrice || 0
                                  // *
                                  //   (quantities[product.product_id] || 1)
                                )}
                              </p>
                            </div>
                          )}

                          <div
                            className={`${
                              isMoreDetails === product.product_id
                                ? " mt-auto self-end"
                                : "mt-auto  flex justify-end  self-end"
                            }`}
                          >
                            {isMoreDetails === product.product_id ? (
                              <ArrowUp2
                                onClick={() =>
                                  handleMoreDetails(product.product_id)
                                }
                                className=" cursor-pointer text-primary-neutralt1 text-right"
                              />
                            ) : (
                              <ArrowDown2
                                onClick={() =>
                                  handleMoreDetails(product.product_id)
                                }
                                className="cursor-pointer text-primary-neutralt1 text-right"
                              />
                            )}
                          </div>
                        </div>
                      </div>
                      {isMoreDetails === product.product_id && (
                        <div className="flex flex-col w-full gap-5">
                          <h4 className="text-[16px] font-sora font-semibold text-primary-neutralt1">
                            Pricing and Quantity
                          </h4>
                          <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                            <p className="font-sora text-primary-neutralt2 text-[14px]">
                              Quantity
                            </p>
                            <div className="flex w-[210px]  justify-between p-4 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                              <button
                                type="button"
                                disabled={product.quantity <= 1}
                                onClick={() =>
                                  handleDecreaseQuantity(product.product_id)
                                }
                              >
                                <MinusCirlce
                                  className={` ${
                                    product.quantity <= 1
                                      ? "text-primary-neutral300 cursor-not-allowed"
                                      : "text-primary-neutralt1 cursor-pointer"
                                  }`}
                                />
                              </button>
                              <p className="text-[14px] font-sora text-primary-neutralt1">
                                {product.quantity}
                              </p>
                              <AddCircle
                                className="text-primary-neutralt1 cursor-pointer"
                                onClick={() =>
                                  handleIncreaseQuantity(product.product_id)
                                }
                              />
                            </div>
                          </div>
                          <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                            <p className="font-sora text-primary-neutralt2 text-[14px]">
                              Selling Price
                            </p>
                            <div className="flex w-[210px]  p-4 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                              <p className="font-sora text-primary-neutralt2 ">
                                ₦{" "}
                                {FormatPrice(
                                  product.discountedPrice
                                  // *
                                  //   quantities[product.product_id]
                                )}
                              </p>
                            </div>
                          </div>
                          <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                            <p className="font-sora text-primary-neutralt2 text-[14px]">
                              Discounted Price
                            </p>
                            <div className="flex w-[210px] items-center  justify-between p-4 border-[1px] rounded-3xl border-primary-neutral300 ">
                              <p className="font-sora text-[#DC3545] ">
                                ₦ {FormatPrice(product.discountAmount)}
                              </p>
                              <div className="flex items-center gap-3">
                                {" "}
                                <p className="font-sora text-[#DC3545] ">
                                  {product.discountPercentage || 0}%
                                </p>
                                {/* <p className="font-sora text-[18px] text-primary-neutralt1">
                                  X
                                </p> */}
                              </div>
                            </div>
                          </div>
                          {/* <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                            <p className="font-sora text-primary-neutralt2 text-[14px]">
                              Delivery
                            </p>
                            <div className="flex w-[210px]  p-4 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                              <p className="font-sora text-primary-neutralt2 ">
                                ₦ {FormatPrice(orderDetail.deliveryFee)}
                                {orderDetail.deliveryFee === 0 && '(free)'}
                              </p>
                            </div>
                          </div> */}
                          <div className="flex w-full pb-4  border-primary-neutral300 justify-between items-center">
                            <p className="font-sora text-primary-neutralt2 text-[14px]">
                              Total
                            </p>
                            <div className="self-end">
                              <p className="font-sora text-[16px] text-primary-neutralt2 ">
                                ₦{" "}
                                {FormatPrice(
                                  product.discountedPrice * product.quantity +
                                    orderDetail.deliveryFee
                                )}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
              <div>
                <p className="mb-3">Delivery Cost</p>
                <Dropdown
                  onSelect={handleDeliveryChange}
                  options={
                    deliveryFeeData.map(
                      (d: any) =>
                        `${d?.deliveryTitle} - ₦ ${Number(
                          d?.deliveryValue
                        )?.toLocaleString()}`
                    ) || []
                  }
                  placeholder="select delivery fee"
                  border="bg-[#EAF7ED]"
                  refetchDelivery={fetchDelivery}
                />

                <div className="flex items-center justify-between mt-4">
                  <span className="md:text-[14px] text-[12px] text-primary-neutralt2">
                    Do you want to set new cost
                  </span>
                  <button
                    onClick={handleDelivery}
                    className="text-primary-purple text-[12px] md:text-[14px] font-semibold"
                  >
                    + Set New Delivery Fee
                  </button>
                </div>
              </div>
            </div>

            {/* Channel Section */}
            <div className="p-6 flex flex-col gap-3">
              <h4 className="font-semibold font-sora text-base">
                Where are they buying from?
              </h4>
              <p className="font-sora text-[14px] text-primary-neutralt2">
                Channel
              </p>
              <Dropdown
                options={[
                  "WhatsApp",
                  "Instagram",
                  "Telegram",
                  "Facebook",
                  "TikTok",
                  "Instore",
                  "Twitter (X)",
                  "Others",
                ]}
                onSelect={handleChannelSelect}
                placeholder="Select channel e.g Instagram"
              />
            </div>

            {/* Payment Method Section */}
            <div className="p-6 flex flex-col gap-6 mb-[130px]">
              <h4 className="font-semibold font-sora text-base">
                How are they paying?
              </h4>
              <div className="w-full flex gap-2 p-1 border rounded-3xl justify-between">
                {tabs.map((tab) => (
                  <button
                    key={tab}
                    className={`flex-1 px-4 py-2 text-center text-[12px] md:text-[14px] rounded-full transition ${
                      activeTab === tab
                        ? tab === "Not Yet Paid"
                          ? "bg-[#FFF2DF] text-[#FF9900] font-semibold"
                          : tab === "Paid"
                          ? "bg-[#EAF7ED] text-[#28A745] font-semibold"
                          : "bg-[#E6E5F9] text-primary-purple font-semibold"
                        : "text-primary-neutralt2 bg-primary-baseWhite"
                    }`}
                    onClick={() => handleTabClick(tab)}
                  >
                    {tab}
                  </button>
                ))}
              </div>

              {/* Conditional Rendering for Paid or Installment */}
              {activeTab === "Paid" && (
                <Dropdown
                  options={["Cash", "Bank Transfer", "POS", "Crypto", "Others"]}
                  onSelect={handlePaid}
                  placeholder="Select how you were paid"
                />
              )}
              {activeTab === "Installment" && (
                <>
                  <Dropdown
                    options={[
                      "Cash",
                      "Bank Transfer",
                      "POS",
                      "Crypto",
                      "Others",
                    ]}
                    onSelect={handleSelect}
                    placeholder="Select how you were paid"
                  />
                  <InputField
                    id="amount-paid"
                    placeholder="Enter the amount you were paid"
                    className="w-full mt-2"
                    onChange={(e) => handleAmountPaid(e.target.value)}
                  />
                </>
              )}
            </div>
          </div>

          {/* Bottom Buttons */}
          <div className="fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2 md:p-10">
            <div className="flex w-full gap-2.5">
              <button
                onClick={closeModal}
                className="bg-transparent text-[#2a2a2a] text-sm font-semibold border px-12 h-[49px] rounded-2xl"
              >
                Back
              </button>
              <button
                disabled={isSaveDisabled}
                onClick={handlePreview}
                className={`bg-[#4f4cd8] w-full text-white text-sm font-semibold border px-6 h-[49px] rounded-2xl ${
                  isSaveDisabled ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                Save & Preview
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* AddCustomer Modal */}
      {addCustomerPanel && (
        <AddCustomer
          closeModal={closeCustomerPanel}
          isOpen={addCustomerPanel}
        />
      )}

      {/* addproduct panel */}
      {addProduct && (
        <AddProduct
          closeModal={closeAddProduct}
          isOpen={addProduct}
          refetch={refetchproduct}
        />
      )}

      {addDeliveryPanel && (
        <CreateDelivery
          closeModal={closeCustomerPanel}
          isOpen={addDeliveryPanel}
        />
      )}

      {/* previewPage */}
      {previewOrder && orderData && (
        <PreviewOrder
          closeModal={closePreview}
          allClose={closeModal}
          isOpen={previewOrder}
          orderDetail={orderData}
          allData={allData}
          refetch={refetch}
        />
      )}
    </div>
  );
};

export default CreateOrder;
