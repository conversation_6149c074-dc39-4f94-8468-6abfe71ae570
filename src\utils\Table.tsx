/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { useState } from "react";
import upDown from "../assets/chevrons-up-down.svg";

interface HeaderType {
  title: string;
  key?: string; // Make 'key' optional
  align?: string;
  render?: (row: any) => React.ReactNode;
}

interface Props {
  rows?: any[];
  headers?: HeaderType[];
  showHead?: boolean;
  onRowClick?: (x?: any) => void;
  allowRowClick?: boolean;
}

export const Table: React.FC<Props> = ({
  headers,
  rows = [],
  onRowClick = () => null,
  allowRowClick = false,
}) => {
  const [sortConfig, setSortConfig] = useState<{
    key?: string;
    direction?: "asc" | "desc";
  }>({});

  // Sorting function
  const sortedRows = React.useMemo(() => {
    if (!sortConfig.key) return rows;

    const sortedArray = [...rows].sort((a, b) => {
      const aValue = a[sortConfig.key!];
      const bValue = b[sortConfig.key!];

      // Handle different types (e.g., string, number)
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortConfig.direction === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (aValue < bValue) {
        return sortConfig.direction === "asc" ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === "asc" ? 1 : -1;
      }
      return 0;
    });
    return sortedArray;
  }, [rows, sortConfig]);

  // Handle header click to sort
  const handleSort = (key?: string) => {
    if (!key) return; // If no key, skip sorting

    let direction: "asc" | "desc" = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  return (
    <div className="overflow-x-auto">
      <table className="inventory-table  w-full border-collapse min-w-[900px]">
        <thead>
          <tr className="hover:bg-[#f9f9f9] ">
            {headers?.map((header, index) => (
              <th
                key={index}
                className={`bg-[#f1f4f9] truncate text-xs font-normal text-[#919191] text-left p-3 border-y border-gray-300 ${
                  header.key ? "cursor-pointer" : ""
                }`}
                onClick={() => handleSort(header.key)}
              >
                <div className="flex  items-center gap-2.5">
                  <span>{header.title}</span>
                  {header.key && ( // Only show sorting icon if 'key' is defined
                    <img
                      src={
                        sortConfig.key === header.key
                          ? sortConfig.direction === "asc"
                            ? upDown
                            : upDown
                          : upDown
                      }
                      alt="sort button"
                      className="cursor-pointer"
                    />
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>

        <tbody>
          {sortedRows?.map((row: any, rowIndex: number) => (
            <tr
              key={rowIndex}
              onClick={allowRowClick ? () => onRowClick(row) : undefined}
              className="hover:bg-[#f9f9f9] cursor-pointer "
            >
              {headers?.map((header, headerIndex) => {
                const cellContent = header?.render
                  ? header.render(row)
                  : row[header.key || ""];
                const cellClassNames = `p-3 border-b text-[#5B5B5B] font-normal text-xs border-[#eee] ${
                  header?.align === "center" ? "text-center" : ""
                }`;

                return (
                  <td key={headerIndex} className={cellClassNames}>
                    {cellContent}
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default Table;