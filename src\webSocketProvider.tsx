import React, { createContext, ReactNode, useEffect, useState } from "react";
import useWebSocket, { ReadyState } from "react-use-websocket";
import { useUserAuthStore } from "./store/auth";
import { useQueryClient } from "react-query";

interface IWebSocketContext {
  lastMessage: MessageEvent | null;
  readyState: ReadyState;
}

export const WebSocketContext = createContext<IWebSocketContext | undefined>(
  undefined
);

interface WebSocketProviderProps {
  children: ReactNode;
  vendorId: string;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({
  children,
  vendorId,
}) => {
  const queryclient = useQueryClient();

  const { lastMessage, readyState } = useWebSocket("wss://api.huxxle.com/pg", {
    // Add any options here if needed.
    onOpen: () => console.log("WebSocket connected"),
    onClose: () => console.log("WebSocket disconnected"),
    shouldReconnect: () => {
      console.log("Attempting to reconnect...");
      // Always attempt to reconnect
      return true;
    },
    onMessage(event) {
      console.log("Message received:", event.data);
      try {
        const vendorId = useUserAuthStore.getState().user.vendorId;

        const message = JSON.parse(event.data);
        if (message.data.vendorId === vendorId) {
          switch (message.type) {
            case "inventory-update":
              queryclient.invalidateQueries("product_details");
              queryclient.invalidateQueries("notifications");
              queryclient.invalidateQueries("allproducts");
              queryclient.invalidateQueries("products");
              queryclient.invalidateQueries("overviewstats");
              queryclient.invalidateQueries("StockDetails")
              queryclient.invalidateQueries("details"); 
              console.log("product updated succesfully:", message.data);
              // e.g. call a function to refetch orders
              break;
            case "order-update":
              // Refresh product list
              queryclient.invalidateQueries("notifications");
              queryclient.invalidateQueries("orderTable");
              queryclient.invalidateQueries("orderStats");
              queryclient.invalidateQueries("orderDetail");
              queryclient.invalidateQueries("StockDetails")
              queryclient.invalidateQueries("details"); 
              queryclient.invalidateQueries("allproducts");
              queryclient.invalidateQueries("products");
              queryclient.invalidateQueries("overviewstats");

              console.log("order changed:", message.data);
              break;
            case "customer-update":
              // Refresh customer list
              queryclient.invalidateQueries("getCustomers");
              queryclient.invalidateQueries("profileData");
              queryclient.invalidateQueries("customerProfile");
              console.log("Customer changed:", message.data);
              break;
            case "profile-update":
              // Refresh profile list
              console.log("Profile changed:", message.data);
              break;
            case "promo-update":
              // Refresh promo list
              queryclient.invalidateQueries("promoDetail");
              queryclient.invalidateQueries("getPromoData");
              console.log("Promo changed:", message.data);
              break;
            case "delivery-fee":
              // Refresh delivery fee list
              queryclient.invalidateQueries("deliveryFee");
              console.log("Delivery fee changed:", message.data);
              break;
            case "review-update":
              // Refresh review list
              console.log("Review changed:", message.data);
              break;
            default:
              break;
          }
        }
      } catch (error) {
        console.log("Error parsing WebSocket message:", error);
      }
    },
  });
  // Local state to hold only the messages that match the vendorId
  const [filteredMessage, setFilteredMessage] = useState<MessageEvent | null>(
    null
  );

  // Check every new message to see if it matches the vendorId
  useEffect(() => {
    if (lastMessage) {
      try {
        const parsedMessage = JSON.parse(lastMessage.data);
        // Assume the message has a structure like { data: { vendorId: string, ... } }
        if (parsedMessage.data && parsedMessage.data.vendorId === vendorId) {
          setFilteredMessage(lastMessage);
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    }
  }, [lastMessage, vendorId]);

  const value: IWebSocketContext = { lastMessage: filteredMessage, readyState };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};
