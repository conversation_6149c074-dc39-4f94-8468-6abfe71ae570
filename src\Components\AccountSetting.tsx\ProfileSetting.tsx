import honeyComb from '../../assets/pattern_2.svg';
import honeyCom from '../../assets/pattern_3.svg';
import ActiveState from './ActiveState';
const ProfileSetting = () => {
  return (
    <div className="md:mt-[70px] font-sora md:border p-6 rounded-2xl mb-7">
      <div className="bg-gradient-to-r from-[#4f4cd8] to-[#2a2872] relative p-6 rounded-2xl">
        <h2 className="font-semibold text-lg sm:text-2xl text-[#FCFCFC]">
          Profile & Settings
        </h2>
        <p className="text-[#B4B2EE] font-normal text-sm">
          Manage your profile and set your account preferences here
        </p>
        <img
          src={honeyComb}
          alt="honeycomb"
          className="absolute top-1 right-0 h-[97px] hidden sm:block"
        />
        <img
          src={honeyCom}
          alt="honeycomb"
          className="absolute top-1 right-0 h-[97px] sm:hidden"
        />
      </div>
      <ActiveState />
    </div>
  );
};
export default ProfileSetting;
