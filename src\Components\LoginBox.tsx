// import { useEffect } from "react";
import Logo from "../assets/Logo.svg";
// import Message from "../assets/messages.svg";
import AuthForm from "./AuthForm";
import NewRight from "./NewRight";
// import Honey<PERSON>omb from "./HoneyComb";
// import { Link } from "react-router-dom";

const LoginBox = () => {
  // useEffect(() => { document.title = 'Log in / Sign Up - Huxxle' }, []);
  return (
    <div className="fixed inset-0 flex flex-col h-full md:flex-row  p-5 mt-0 lg:mt-0 md:justify-center gap-2 w-full  overflow-auto">
      <div className="flex-1 h-full">
        <div className="flex flex-col h-full">
          {/* Left Section */}
          <div className="">
            <div className="flex p-2 justify-center md:justify-start">
              <img
                src={Logo}
                className="w-fit mb-[50px] md:mb-[20px] flex"
                alt="KataJere"
              />
            </div>
            <div className="flex justify-center items-center h-full">
              <AuthForm />

            </div>
            {/* <div className="mt-6">
              <div className="flex justify-between md:hidden gap-5">
                <Link to='https://huxxle.com/terms-of-use' target="blank" className="border-[1px] flex justify-center font-sora text-[#5B5B5B] rounded-lg py-3 border-primary-neutral300 flex-1">
                  Terms of Use
                </Link>
                <Link to='https://huxxle.com/privacy-policy' target="blank" className="border-[1px] flex justify-center font-sora text-[#5B5B5B] rounded-lg py-3 border-primary-neutral300 flex-1">
                  Privacy Policy
                </Link>
              </div>
            </div> */}
            {/* <div className="md:mt-auto  mt-5 flex justify-center md:justify-start">
              <p className="flex font-sora gap-1 text-primary-neutral1000 text-[12px] md:text-[14px]">
                <img src={Message} alt="chat" />
                Do you need help? Reach out to{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary-purple"
                >
                  <EMAIL>
                </a>
              </p>
            </div> */}
          </div>
          {/* Right Section */}
        
          {/* <HoneyComb /> */}
        </div>
      </div>
      <div className="w-full h-full lg:block flex-1 hidden"><NewRight/></div>
    </div>
  );
};

export default LoginBox;
