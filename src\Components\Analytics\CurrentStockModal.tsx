/* eslint-disable @typescript-eslint/no-explicit-any */
import { InsightTable } from '../../utils/InsightTable';

const CurrentStockModal = ({ isOpen, onClose, data }: any) => {
  console.log(data)
  const rows = data?.currentStockLevel ?? []
  console.log(rows)
  const headers = [
    {
      title: 'Product Name',
      render: (row: any) => (
        <p className="text-[#5B5B5B] text-xs font-bold ">{row.productName}</p>
      ),
    },
    {
      title: 'Stock Level',
      render: (row: any) => (
        <p className="text-[#5B5B5B] text-xs font-normal">{row.stockLevel}</p>
      ),
    },
    {
      title: 'Reorder Level',
      render: (row: any) => (
        <p className="text-[#5B5B5B] font-normal text-xs ">{row.reorderLevel}</p>
      ),
    },
  ];

  // const rows = [
  //   {
  //     id: 1,
  //     product: "Men's Denim Jacket",
  //     no: '1200',
  //     totalRevenue: '2000',
  //     action: 'Offer Premium',
  //     percent: '16%',
  //   },
  //   {
  //     id: 2,
  //     product: "Women's Summer Dress",
  //     no: '600',
  //     totalRevenue: '1200',
  //     action: 'Send Loyalty Offer',
  //     percent: '10%',
  //   },
  //   {
  //     id: 3,
  //     product: 'Unisex Hoodie',
  //     no: '400',
  //     totalRevenue: '900',
  //     action: 'Reconnect',
  //     percent: '5%',
  //   },
  //   {
  //     id: 4,
  //     product: "Men's Formal Shirt",
  //     no: '400',
  //     totalRevenue: '600',
  //     action: 'Reconnect',
  //     percent: '4%',
  //   },
  //   {
  //     id: 5,
  //     product: "Women's Casual Top",
  //     no: '400',
  //     totalRevenue: '400',
  //     action: 'Reconnect',
  //     percent: '2%',
  //   },
  // ];
  return (
    <div className="fixed font-sora  top-0 left-0 w-full h-full  bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-screen md:w-[580px] bg-[#FCFCFC] backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={onClose}
            className=" text-4xl font-bold cursor-pointer my-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="px-2 sm:px-10 overflow-y-auto h-screen scrollbar-none hidescroll  pb-32">
            <h4 className="text-[#181818] font-semibold text-2xl">
              Current Stock Levels{" "}
            </h4>
            <p className="text-[#919191] font-normal text-sm mb-6">
              Detailed stock levels for each product{" "}
            </p>
            <div className="overflow-x-auto max-w-[240px] sm:max-w-full ">
              {rows.length > 0 ? (
                <InsightTable rows={rows} headers={headers} showHead={true} />
              ) : (
                <p className="text-[#919191] font-normal text-sm mb-6">
                  Insufficient data to generate insights.
                </p>
              )}{" "}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default CurrentStockModal;
