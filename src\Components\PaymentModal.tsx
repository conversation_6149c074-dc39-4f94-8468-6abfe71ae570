/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
import { IoIosCheckmarkCircleOutline } from 'react-icons/io';
import { PiWarningCircle } from 'react-icons/pi';
import { FaRegCircleXmark } from 'react-icons/fa6';
import DeclinePaymentModal from './Order/DeclinePaymentModal';
import { orderServices } from '../utils/Services/Order';
import { useMutation } from 'react-query';
import { toast } from 'react-toastify';
import { useUserAuthStore } from '../store/auth';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: any;
  imageSrc: string;
  refetch: any;
  refetchTable: any;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  order,
  imageSrc,
  refetch,
  refetchTable
}) => {
  const [isDeclineModalOpen, setIsDeclineModalOpen] = useState(false);
  const user = useUserAuthStore((state) => state.user);
  const mutation = useMutation((data: any) => orderServices.editOrder(data));
  console.log('order from py', order);
  const handleDeclineClick = () => {
    setIsDeclineModalOpen(true);
  };

  const handleDeclineClose = () => {
    setIsDeclineModalOpen(false);
  };

  const handleConfirm = () => {
    const confirmData = {
      vendorId: user.vendorId,
      orderId: order?.orderId,
      vendorEmail: user.userEmail,
      editEntireOrder: true,
      paymentStatus: 'Paid',
      amountPaid: order?.amountPaid,
    };
    mutation.mutate(confirmData, {
      onSuccess: async () => {
        toast.success('Confirmed Successfully');
        refetch();
        refetchTable();
        onClose();
      },
      onError: (error: any) => {
        if (error?.response?.status === 429) {
          toast.error(error?.response?.data?.message);
        } else {
          toast.error(
            error?.response ? error?.response?.data?.error : error?.message
          );
        }
      },
    });
  };

  return (
    <div
      className={`fixed top-0 left-0 w-full h-full bg-[rgba(0,0,0,0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
      <div className="fixed top-0 right-0 md:w-[580px] w-full pb-24 bg-white h-full overflow-y-auto z-30 p-5">
        <div className="flex justify-end items-center">
          <span onClick={onClose} className="text-3xl font-bold cursor-pointer">
            &times;
          </span>
        </div>

        <h2 className="mt-5 text-xl font-semibold">Proof Of Payment</h2>

        <p className="text-sm text-gray-500 mt-2">
          See and manage the details of your orders.
        </p>

        <div className="mt-5 bg-[#F5F5F5] p-4 rounded-lg">
          <div className="bg-[#FAF2F2] px-4 py-3 rounded-xl">
            <div className="flex flex-col md:flex-row items-start gap-3">
              <div className="bg-white rounded-full flex items-center justify-center w-10 h-10">
                <PiWarningCircle
                  style={{
                    backgroundColor: '#DC3545',
                    borderRadius: '100%',
                    color: '#fff',
                    width: '24px',
                    height: '24px',
                  }}
                />
              </div>
              <p className="text-[#2A2A2A] font-bold text-base">
                Please Check Your Account Balance
              </p>
            </div>

            <p className="text-[14px] text-[#2A2A2A] md:px-14 mt-2">
              Don’t just look at this proof of payment and confirm payment.
              Check your mobile app to see that you received the money.
            </p>
          </div>

          <div className="mt-4 flex justify-center items-center">
            <img
              src={imageSrc}
              alt="Proof of Payment"
              className="w-[300px] h-auto rounded-lg border"
            />
          </div>

          <div className="flex mt-10 flex-col md:flex-row md:gap-4 mb-4 gap-2">
            <button
              onClick={handleConfirm}
              disabled={mutation.isLoading}
              className={`w-full py-3 bg-[#28A745] rounded-2xl justify-center items-center text-white font-semibold flex gap-2  hover:bg-green-600 ${
                mutation.isLoading ? ' opacity-50 cursor-not-allowed ' : ' '
              }`}>
              <IoIosCheckmarkCircleOutline />
              {mutation.isLoading ? 'Confirming . . .' : '   Confirm Payment'}
            </button>
            <button
              onClick={handleDeclineClick}
              className="w-full py-3 text-[#DC3545] font-semibold flex gap-2 justify-center items-center rounded-2xl border-[#F3BCC1] border">
              <FaRegCircleXmark />
              Decline Payment
            </button>
          </div>
        </div>
      </div>
      {isDeclineModalOpen && (
        <DeclinePaymentModal
          isOpen={isDeclineModalOpen}
          onClose={handleDeclineClose}
          closePaymentModal={onClose}
          order={order}
          refetch={refetch}
          refetchTable ={refetchTable}
        />
      )}
    </div>
  );
};

export default PaymentModal;
