// Service Worker for handling push notifications

self.addEventListener('install', event => {
  self.skipWaiting();
});

self.addEventListener('activate', event => {
  return self.clients.claim();
});

// Handle incoming push notifications
self.addEventListener('push', event => {
  console.log('[Service Worker] Push received');
  
  // Parse the notification data
  let data = {};
  if (event.data) {
    try {
      data = event.data.json();
    } catch (e) {
      console.error('Error parsing push data:', e);
      data = {
        title: 'New Notification',
        body: event.data.text(),
        icon: '/logo192.png'
      };
    }
  }

  // Ensure we have some default values if properties are missing
  const title = data.title || 'New Notification';
  const options = {
    body: data.body || 'You have a new notification',
    icon: data.icon || '/logo192.png',
    badge: data.badge || '/badge.png',
    data: data.data || {},
    actions: data.actions || [],
    tag: data.tag || 'default-tag', // Group similar notifications
    renotify: data.renotify || false
  };

  event.waitUntil(
    self.registration.showNotification(title, options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
  console.log('[Service Worker] Notification click received');

  const notification = event.notification;
  const action = event.action;
  notification.close();

  // Handle notification click based on action or default behavior
  if (action === 'explore') {
    // Navigate to specific URL if action is explore
    event.waitUntil(
      clients.openWindow(notification.data.url || 'https://vendor.huxxle.com')
    );
  } else {
    // Default click behavior
    event.waitUntil(
      clients.openWindow(notification.data.url || 'https://vendor.huxxle.com')
    );
  }
});
