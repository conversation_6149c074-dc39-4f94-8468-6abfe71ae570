{"name": "katajere1-0", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.7", "axios": "^1.7.7", "blueimp-load-image": "^5.16.0", "chart.js": "^4.4.4", "crypto": "^1.0.1", "dotenv": "^16.4.5", "iconsax-react": "^0.0.8", "jwt-decode": "^4.0.0", "katajere1-0": "file:", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-date-range": "^2.0.1", "react-datepicker": "^7.3.0", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-icons": "^5.3.0", "react-image-crop": "^11.0.7", "react-microsoft-clarity": "^2.0.0", "react-query": "^3.39.3", "react-router-dom": "^6.26.1", "react-toastify": "^10.0.5", "react-use-websocket": "^4.13.0", "recharts": "^2.15.0", "workbox-webpack-plugin": "^7.3.0", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/blueimp-load-image": "^5.16.6", "@types/chart.js": "^2.9.41", "@types/node": "^22.9.0", "@types/react": "^18.3.3", "@types/react-date-range": "^1.4.9", "@types/react-dom": "^18.3.0", "@types/serviceworker": "^0.0.122", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.43", "rollup-plugin-visualizer": "^5.12.0", "tailwindcss": "^3.4.10", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^6.2.1", "vite-plugin-pwa": "^0.21.1", "vite-plugin-static-copy": "^2.2.0"}}