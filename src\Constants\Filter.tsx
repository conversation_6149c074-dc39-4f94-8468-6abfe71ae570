/* eslint-disable @typescript-eslint/no-explicit-any */

import { useRef, useEffect } from "react";
import { IoFilter } from "react-icons/io5";
import { useLocation } from "react-router-dom";

interface FilterProps {
  customClassName?: string;
  statuses?: string;
  selectedStatus?: string;
  stat1?: string;
  stat2?: string;
  stat3?: string;
  stat4?: string;
  onStatusChange?: (value: string) => void;
  onCategoryChange?: (value: string) => void;
  data?: any;
  selectedCategory?: string;
  category?: string;
  onFilterChange?: any;
  handleOrderStatusChange?: any;
  handleOrderChannel?: any;
  selectedChannel?: string;
  onCustomerChange?: any;
  dropDown?: any;
  setDropDown?: any;
}

const Filter: React.FC<FilterProps> = ({
  customClassName,
  statuses,
  selectedStatus,
  selectedCategory,
  stat1,
  stat2,
  stat3,
  stat4,
  onStatusChange,
  onCategoryChange,
  category,
  data,
  onFilterChange,
  handleOrderStatusChange,
  handleOrderChannel,
  selectedChannel,
  onCustomerChange,
  dropDown,
  setDropDown,
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const location = useLocation();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setDropDown(false);
      }
    };

    document.addEventListener('mousedown', handleOutsideClick);
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [setDropDown]);

  const handleClick = () => {
    setDropDown(true);
  };

  const handleStatusChange = (value: string) => {
    onStatusChange?.(value);
    setDropDown(false); // Close dropdown when a value is selected
  };

  const handleCategoryChange = (value: string) => {
    onCategoryChange?.(value);
    setDropDown(false); // Close dropdown when a value is selected
  };

  return (
    <div className="flex relative">
      <button
        onClick={handleClick}
        className={`flex items-center gap-2 text-sm font-semibold px-4 py-3 border border-[#ababab] rounded-2xl bg-white hover:bg-gray-100 ${customClassName}`}
      >
        <IoFilter size={16} color="#2A2A2A" />
      </button>

      {dropDown && (
        <div
          ref={dropdownRef}
          className="absolute w-[300px]  -left-[280px] top-[50px] p-4 z-50 bg-primary-baseWhite rounded-2xl border-[1px]"
        >
          {location.pathname.includes("/inventory") &&
            !location.pathname.includes("/inventory/trackstock") && (
              <div className="flex-col flex space-y-5">
                <div>
                  <p className="font-sora text-[16px] text-primary-neutralt1">
                    Filter by {statuses}
                  </p>
                  <select
                    id="statusFilter"
                    className="py-2 w-fit text-[8px] px-4 border mt-[8px] text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none"
                    value={selectedStatus}
                    onChange={(e) => handleStatusChange?.(e.target.value)}
                  >
                    <option value="">Filter by Status</option>
                    <option value={stat1}>{stat1}</option>
                    <option value={stat2}>{stat2}</option>
                    <option value={stat3}>{stat3}</option>
                  </select>
                </div>
                <div>
                  <p className="font-sora text-[16px] text-primary-neutralt1">
                    Filter by {category}
                  </p>
                  <select
                    id="categoryFilter"
                    className="py-2 w-fit text-[8px] px-4 border mt-[8px] text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none"
                    value={selectedCategory}
                    onChange={(e) => handleCategoryChange?.(e.target.value)}
                  >
                    <option value="">Filter by Category</option>
                    {data?.map((categoryItem: any) => (
                      <option key={categoryItem} value={categoryItem}>
                        {categoryItem}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}
          {location.pathname.includes("/inventory/trackstock") && (
            <div>
              <p className="font-sora text-[16px] text-primary-neutralt1">
                Filter by {category}
              </p>
              <select
                id="categoryFilter"
                className="py-2 w-fit text-[8px] px-4 border mt-[8px] text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none"
                onChange={onFilterChange}
              >
                <option value="">Filter by Category</option>
                <option value={stat1}>{stat1}</option>
                <option value={stat2}>{stat2}</option>
                <option value={stat3}>{stat3}</option>
              </select>
            </div>
          )}

          {/* show for Order */}
          {location.pathname.includes("/order") &&
            !location.pathname.includes("/order/promo") &&
            !location.pathname.includes("/order/customer") && (
              <div className="flex-col flex space-y-5">
                <div>
                  <p className="font-sora text-[16px] text-primary-neutralt1">
                    Filter by {statuses}
                  </p>
                  <select
                    id="statusFilter"
                    className="py-2 w-fit text-[8px] px-4 border mt-[8px] text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none"
                    value={selectedStatus}
                    onChange={handleOrderStatusChange}
                  >
                    <option value="">Filter by Status</option>
                    <option value={stat1}>{stat1}</option>
                    <option value={stat2}>{stat2}</option>
                    <option value={stat3}>{stat3}</option>
                    <option value={stat4}>{stat4}</option>
                  </select>
                </div>
                <div>
                  <p className="font-sora text-[16px] text-primary-neutralt1">
                    Filter by {category}
                  </p>
                  <select
                    id="categoryFilter"
                    className="py-2 w-fit text-[8px] px-4 border mt-[8px] text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none"
                    value={selectedChannel}
                    onChange={handleOrderChannel}
                  >
                    <option value="">Filter by Channel</option>
                    {data?.map((categoryItem: any) => (
                      <option key={categoryItem} value={categoryItem}>
                        {categoryItem}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}

          {/* show for promo */}

          {location.pathname.includes("/order/promo") && (
            <div>
              <p className="font-sora text-[16px] text-primary-neutralt1">
                Filter by {statuses}
              </p>
              <select
                id="statusFilter"
                className="py-2 w-fit text-[8px] px-4 border mt-[8px] text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none"
                value={selectedStatus}
                onChange={(e) => handleStatusChange?.(e.target.value)}
              >
                <option value="">Filter by Status</option>
                <option value={stat1}>{stat1}</option>
                <option value={stat2}>{stat2}</option>
                <option value={stat3}>{stat3}</option>
              </select>
            </div>
          )}

          {/* show for customer */}

          {location.pathname.includes("/order/customer") && (
            <div>
              <p className="font-sora text-[16px] text-primary-neutralt1">
                Filter by {statuses}
              </p>
              <select
                className="py-2 w-fit text-[8px] px-4 border mt-[8px] text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none"
                onChange={onCustomerChange}
                value={selectedStatus}
              >
                <option value="">Filter by Status</option>
                <option value={stat1}>{stat1}</option>
                <option value={stat2}>{stat2}</option>
                <option value={stat3}>{stat3}</option>
              </select>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Filter;
