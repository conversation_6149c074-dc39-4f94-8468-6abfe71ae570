interface ProgressbarProps {
  itemName: string;
  level?: number | string;
  units?: string;
  title?: string;
  progressPercentage: number;
  barColor?: string;
  dotColor?: string;
  isLoading?: boolean;
}

const Progressbar: React.FC<ProgressbarProps> = ({
  itemName,
  level,
  units,
  title,
  progressPercentage,
  barColor = '#B4B2EE',
  dotColor = '#D9D9D9',
  isLoading
}) => {
  return (
    <div className="mt-6">
      {isLoading ? (
        <div className="rounded-2xl animate-pulse bg-slate-200 h-[36px]"></div>
      ) : (
        <div
          className="h-[36px]"
          style={{
            width: `${progressPercentage || 0}%`,
            backgroundColor: barColor,
          }}></div>
      )}
      {isLoading ? (
        <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[120px] my-4"></div>
      ) : (
        <p className="font-bold text-sm text-[#2A2A2A] mt-4">{itemName}</p>
      )}
      {isLoading ? (
        <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[160px]"></div>
      ) : (
        <p className="flex items-center gap-1 text-[#5B5B5B]">
          {title}
          <span
            className={`${
              title ? 'h-1.5 w-1.5 block rounded-full ' : 'hidden '
            }`}
            style={{ backgroundColor: dotColor }}></span>
          <span className="font-semibold">
            {level} {units}
          </span>
        </p>
      )}
    </div>
  );
};

export default Progressbar;
