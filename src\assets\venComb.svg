<svg width="362" height="319" viewBox="0 0 362 319" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ddd_651_12185)">
<path d="M215.047 209.419H153.366L122.525 156.002L153.366 102.584H215.047L245.887 156.002L215.047 209.419ZM154.986 206.612H213.426L242.646 156.002L213.426 105.391H154.986L125.766 156.002L154.986 206.612Z" fill="#FFF2DF"/>
<path d="M215.047 209.419H153.366L122.525 156.002L153.366 102.584H215.047L245.887 156.002L215.047 209.419ZM154.986 206.612H213.426L242.646 156.002L213.426 105.391H154.986L125.766 156.002L154.986 206.612Z" stroke="#FF9900"/>
</g>
<g filter="url(#filter1_ddd_651_12185)">
<path d="M305.138 261.433H243.457L212.616 208.015L243.457 154.598H305.138L335.978 208.015L305.138 261.433ZM245.077 258.626H303.517L332.737 208.015L303.517 157.405H245.077L215.857 208.015L245.077 258.626Z" fill="#FFF2DF"/>
<path d="M305.138 261.433H243.457L212.616 208.015L243.457 154.598H305.138L335.978 208.015L305.138 261.433ZM245.077 258.626H303.517L332.737 208.015L303.517 157.405H245.077L215.857 208.015L245.077 258.626Z" stroke="#FF9900"/>
</g>
<g filter="url(#filter2_ddd_651_12185)">
<path d="M124.957 261.433H63.276L32.4355 208.016L63.276 154.598H124.957L155.797 208.016L124.957 261.433ZM64.8967 258.626H123.336L152.556 208.016L123.336 157.405H64.8967L35.6765 208.016L64.8967 258.626Z" fill="#FFF2DF"/>
<path d="M124.957 261.433H63.276L32.4355 208.016L63.276 154.598H124.957L155.797 208.016L124.957 261.433ZM64.8967 258.626H123.336L152.556 208.016L123.336 157.405H64.8967L35.6765 208.016L64.8967 258.626Z" stroke="#FF9900"/>
</g>
<g filter="url(#filter3_ddd_651_12185)">
<path d="M34.8652 209.419H-26.8158L-57.6562 156.002L-26.8158 102.584H34.8652L65.7056 156.002L34.8652 209.419ZM-25.195 206.612H33.245L62.4647 156.002L33.245 105.391H-25.195L-54.4148 156.002L-25.195 206.612Z" fill="#FFF2DF"/>
<path d="M34.8652 209.419H-26.8158L-57.6562 156.002L-26.8158 102.584H34.8652L65.7056 156.002L34.8652 209.419ZM-25.195 206.612H33.245L62.4647 156.002L33.245 105.391H-25.195L-54.4148 156.002L-25.195 206.612Z" stroke="#FF9900"/>
</g>
<g filter="url(#filter4_ddd_651_12185)">
<path d="M124.957 157.406H63.276L32.4355 103.988L63.276 50.571H124.957L155.797 103.988L124.957 157.406ZM64.8967 154.599H123.336L152.556 103.988L123.336 53.378H64.8967L35.677 103.988L64.8967 154.599Z" fill="#FFF2DF"/>
<path d="M124.957 157.406H63.276L32.4355 103.988L63.276 50.571H124.957L155.797 103.988L124.957 157.406ZM64.8967 154.599H123.336L152.556 103.988L123.336 53.378H64.8967L35.677 103.988L64.8967 154.599Z" stroke="#FF9900"/>
</g>
<g filter="url(#filter5_ddd_651_12185)">
<path d="M124.957 53.3778H63.276L32.4355 -0.0394936L63.276 -53.4568H124.957L155.797 -0.0394936L124.957 53.3778ZM64.8967 50.5709H123.336L152.556 -0.0394936L123.336 -50.6498H64.8967L35.677 -0.0394936L64.8967 50.5709Z" fill="#FFF2DF"/>
<path d="M124.957 53.3778H63.276L32.4355 -0.0394936L63.276 -53.4568H124.957L155.797 -0.0394936L124.957 53.3778ZM64.8967 50.5709H123.336L152.556 -0.0394936L123.336 -50.6498H64.8967L35.677 -0.0394936L64.8967 50.5709Z" stroke="#FF9900"/>
</g>
<defs>
<filter id="filter0_ddd_651_12185" x="96.9482" y="81.0845" width="174.517" height="157.834" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_651_12185" result="effect2_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_651_12185" result="effect3_dropShadow_651_12185"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_651_12185" result="shape"/>
</filter>
<filter id="filter1_ddd_651_12185" x="187.039" y="133.098" width="174.517" height="157.834" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_651_12185" result="effect2_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_651_12185" result="effect3_dropShadow_651_12185"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_651_12185" result="shape"/>
</filter>
<filter id="filter2_ddd_651_12185" x="6.8584" y="133.098" width="174.517" height="157.835" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_651_12185" result="effect2_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_651_12185" result="effect3_dropShadow_651_12185"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_651_12185" result="shape"/>
</filter>
<filter id="filter3_ddd_651_12185" x="-83.2334" y="81.0845" width="174.517" height="157.834" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_651_12185" result="effect2_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_651_12185" result="effect3_dropShadow_651_12185"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_651_12185" result="shape"/>
</filter>
<filter id="filter4_ddd_651_12185" x="6.8584" y="29.071" width="174.517" height="157.834" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_651_12185" result="effect2_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_651_12185" result="effect3_dropShadow_651_12185"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_651_12185" result="shape"/>
</filter>
<filter id="filter5_ddd_651_12185" x="6.8584" y="-74.9568" width="174.517" height="157.834" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_651_12185" result="effect2_dropShadow_651_12185"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.6 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_651_12185" result="effect3_dropShadow_651_12185"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_651_12185" result="shape"/>
</filter>
</defs>
</svg>
