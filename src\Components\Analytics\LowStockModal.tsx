/* eslint-disable @typescript-eslint/no-explicit-any */
import { InsightTable } from "../../utils/InsightTable";

const LowStockModal = ({ isOpen, onClose, data }: any) => {
  const rows = data?.LowStockAlert ?? [];

   const headers = [
     {
       title: 'Product Name',
       render: (row: any) => (
         <p className="text-[#5B5B5B] text-xs font-bold ">{row.productName}</p>
       ),
     },
     {
       title: 'Current Stock',
       render: (row: any) => (
         <p className="text-[#5B5B5B] text-xs font-normal">
           {row.currentStock}
         </p>
       ),
     },
     {
       title: 'Status',
       render: (row: any) => (
         <button
           type="button"
           className="text-[#FFC107] bg-[#FFF9E6] px-4 py-1 rounded-3xl font-normal text-xs ">
           {row.status}
         </button>
       ),
     },
   ];

 

  
   return (
     <div className="fixed font-sora  top-0 left-0 w-full h-full  bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
       <div
         className={`fixed top-0 right-0 h-screen md:w-[580px] bg-[#FCFCFC] backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
           isOpen ? "translate-x-0" : "translate-x-full"
         }`}
       >
         <div>
           <span
             onClick={onClose}
             className=" text-4xl font-bold cursor-pointer my-4 flex justify-end z-[999999]"
           >
             &times;
           </span>
           <div className="px-2 sm:px-10 overflow-y-auto h-screen scrollbar-none hidescroll  pb-32">
             <h4 className="text-[#181818] font-semibold text-2xl">
               Low Stock Alerts{" "}
             </h4>
             <p className="text-[#919191] font-normal text-sm mb-6">
               Critical stock levels for your products{" "}
             </p>
             <div className="overflow-x-auto max-w-[240px] sm:max-w-full ">
               {rows.length > 0 ? (
                 <InsightTable rows={rows} headers={headers} showHead={true} />
               ) : (
                 <p className="text-[#919191] font-normal text-sm mb-6">
                   Insufficient data to generate insights.
                 </p>
               )}{" "}
             </div>
           </div>
         </div>
       </div>
     </div>
   );
}
export default LowStockModal