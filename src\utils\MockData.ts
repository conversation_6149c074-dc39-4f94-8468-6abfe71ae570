// Import your assets
import speaker from "../assets/jbl-charge-4-ifa-2018-product-photos-1.jpg";
import mug from "../assets/coffee mug.jpeg";
import nintendo from "../assets/nintendo2.png";
import sweater from "../assets/sweater 3.jpg";
import gauntlet from "../assets/thanos2.jpg";
import product1 from "../assets/prod1.svg";
import product2 from "../assets/prod2.svg";
import product3 from "../assets/prod3.svg";

// Define the type for the product data
type Product = {
  id: number;
  image: string;
  name: string;
  status: string;
  price: number;
  total: number;
  SKU: string;
};

type salesData = {
  dateTime: string;
  totalSales: number;
};

// Export the mock data with type safety
export const Data: Product[] = [
  {
    id: 1,
    image: mug,
    name: "Coffee Mug",
    status: "not yet paid",
    price: 2500,
    total: 2,
    SKU: "MJ12345",
  },
  {
    id: 2,
    image: speaker,
    name: "<PERSON><PERSON> Speaker",
    status: "paid",
    price: 4500,
    total: 1,
    SKU: "UH12695",
  },
  {
    id: 3,
    image: sweater,
    name: "Sweater",
    status: "paid",
    price: 16500,
    total: 5,
    SKU: "S92345",
  },
  {
    id: 4,
    image: nintendo,
    name: "Nintendo Switch",
    status: "cancelled",
    price: 22500,
    total: 12,
    SKU: "MJ12345",
  },
  {
    id: 5,
    image: gauntlet,
    name: "Gauntlet",
    status: "paid",
    price: 12500,
    total: 1,
    SKU: "MJ12345",
  },
];

export const products = [
  {
    id: 1,
    image: product1,
    name: "Sample Product",
    status: "not yet paid",
    price: 2500,
    total: 2,
    category:'Sneakers',
    SKU: "MJ12345",
    description: "This is a sample product description for the mock data.",
  },
  {
    id: 2,
    image: product2,
    name: "Sample Product",
    status: "not yet paid",
    price: 25000,
    total: 2,
    category:'Sneakers',
    SKU: "MJ12345",
  },
  {
    id: 3,
    image: product3,
    name: "Sample Product",
    status: "not yet paid",
    price: 5000,
    total: 2,
    category:'sneakers',
    SKU: "MJ12345",
  },
];

export const salesOverTime: salesData[] = [
  {
    dateTime: "2024-07-23 01:17",
    totalSales: 55000,
  },
  {
    dateTime: "2024-07-23 03:27",
    totalSales: 71000,
  },
  {
    dateTime: "2024-07-23 18:08",
    totalSales: 12000,
  },
  {
    dateTime: "2024-07-23 18:16",
    totalSales: 30000,
  },
  {
    dateTime: "2024-07-23 21:45",
    totalSales: 42000,
  },
  {
    dateTime: "2024-07-24 00:23",
    totalSales: 83000,
  },
  {
    dateTime: "2024-07-24 16:55",
    totalSales: 44600,
  },
  {
    dateTime: "2024-07-24 17:00",
    totalSales: 120100,
  },
  {
    dateTime: "2024-07-24 23:19",
    totalSales: 186150,
  },
  {
    dateTime: "2024-07-25 23:39",
    totalSales: 464300,
  },
  {
    dateTime: "2024-08-18 15:59",
    totalSales: 662800,
  },
];

interface CustomerReview {
  image: string;
  name: string;
  business: string;
  comment: string;
  stars: number;
}

const customerReviews: CustomerReview[] = [
  {
    image: "https://via.placeholder.com/150", // Placeholder image or use a link to an actual image
    name: "John Doe",
    business: "Doe's Bakery",
    comment:
      "Great service and excellent quality. Highly recommend to anyone who needs fresh and tasty treats.",
    stars: 5,
  },
  {
    image: "https://via.placeholder.com/150",
    name: "Jane Smith",
    business: "Smith Consulting",
    comment:
      "The team was very professional and helped us grow our business. I am very satisfied.",
    stars: 4,
  },
  {
    image: "https://via.placeholder.com/150",
    name: "David Johnson",
    business: "Tech Innovators",
    comment:
      "Amazing support and fantastic product! I am very impressed with the whole experience.",
    stars: 5,
  },
  {
    image: "https://via.placeholder.com/150",
    name: "Emily Davis",
    business: "Davis Florals",
    comment:
      "Beautiful website with easy navigation. It was a pleasure working with this team!",
    stars: 4,
  },
  {
    image: "https://via.placeholder.com/150",
    name: "Michael Brown",
    business: "Brown & Co. Financial",
    comment:
      "Professional and timely service. They really helped with managing our financial needs.",
    stars: 4,
  },
  {
    image: "https://via.placeholder.com/150",
    name: "Sarah Wilson",
    business: "Wilson's Cafe",
    comment:
      "The entire process was smooth and their solutions are tailored to our specific needs. We’re happy with the results.",
    stars: 5,
  },
  {
    image: "https://via.placeholder.com/150",
    name: "Chris Lee",
    business: "Lee's Gym",
    comment:
      "Friendly staff and outstanding support. The team really went above and beyond to help us.",
    stars: 5,
  },
  {
    image: "https://via.placeholder.com/150",
    name: "Anna Martinez",
    business: "Martinez Organic Market",
    comment:
      "The work was delivered on time and exceeded our expectations. We love working with them.",
    stars: 4,
  },
  {
    image: "https://via.placeholder.com/150",
    name: "Robert Clark",
    business: "Clark's Auto Repairs",
    comment:
      "They provided us with an excellent solution for our marketing. We have seen a lot more foot traffic!",
    stars: 5,
  },
  {
    image: "https://via.placeholder.com/150",
    name: "Laura Thompson",
    business: "Thompson Photography",
    comment:
      "The best experience I’ve had in a while. They helped me showcase my work beautifully!",
    stars: 5,
  },
];

export default customerReviews;
