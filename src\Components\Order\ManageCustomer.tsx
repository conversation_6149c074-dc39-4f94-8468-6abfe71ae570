import { People, Profile2User, ProfileAdd, ProfileTick } from 'iconsax-react';
import { useState, useEffect } from 'react';
import CreateOrder from './CreateOrder';
import OrderReport from './OrderReport';
import AddCustomer from './AddCustomer';
import CustomerTable from './CustomerTable';
import { RouteNames } from '../../utils/RouteNames';
import { Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import { orderServices } from './../../utils/Services/Order';
import { useUserAuthStore } from '../../store/auth';
import { FormatPrice } from '../../utils/FormatPrice';
import CardLoader from '../../Constants/CardLoader';

const ManageCustomer = () => {
  const [createOrderPanel, setCreateOrderPanel] = useState<boolean>(false);
  const [generateReportModal, setGenerateReportModal] =
    useState<boolean>(false);
  const [addCustomer, setAddCustomer] = useState<boolean>(false);
  useEffect(() => {
    document.title = 'Manage Customers';
  }, []);
  const user = useUserAuthStore((state) => state.user);

  const handleReportModal = () => {
    setGenerateReportModal((prev) => !prev);
  };

  const handleAddCustomer = () => {
    setAddCustomer((prev) => !prev);
  };

  const handleCreateOrder = () => {
    setCreateOrderPanel((prev) => !prev);
  };

  const { data, isError, isLoading, refetch } = useQuery(["getCustomers",user.vendorId], () =>
    orderServices.getCustomers(user.vendorId)
  );

 

  const productStats = [
    {
      id: 'totalCustomers',
      title: 'Total Customers',
      value: data?.totalCustomers || 0,
      bgColor: '#e0f7fa',
      icon: People,
      iconColor: '#00796b',
    },
    {
      id: 'newCustomers',
      title: 'New Customers',
      value: data?.newCustomers || 0,
      bgColor: '#ffe0b2',
      icon: ProfileAdd,
      iconColor: '#f57c00',
    },
    {
      id: 'returningCustomers',
      title: 'Returning Customers',
      value: data?.returningCustomers || 0,
      bgColor: '#ffccbc',
      icon: ProfileTick,
      iconColor: '#e64a19',
    },
    {
      id: 'avgSpendPerCustomer',
      title: 'Avg Spend Per Customer',
      value: data?.avgSpendPerCustomer || 0,
      bgColor: '#c8e6c9',
      icon: Profile2User,
      iconColor: '#388e3c',
    },
  ];

  return (
    <div className="md:mt-[70px] w-full p-4 md:p-0">
      <div className="flex flex-col w-full md:flex-row justify-between gap-4 my- font-sora ">
        <div className="w-full">
          <h1 className="text-2xl font-bold m-0">Customers</h1>
          <Link
            to={RouteNames.order}
            className="track-stock-subheader text-[#7b7b7b] text-sm cursor-pointer">
            Order Management &gt;&gt;{' '}
            <span className="text-[#4f4cd8]">Manage Customers</span>
          </Link>
        </div>
        <div className="">
          <div className="w-full md:w-[180px]">
            <button
              onClick={handleAddCustomer}
              className="border w-full h-[49px] justify-center border-gray-300 gap-2 rounded-2xl px-3 py-2 text-base flex items-center cursor-pointer bg-[#4f4cd8] text-primary-baseWhite font-semibold">
              <ProfileAdd size={16} /> Add Customers
            </button>
          </div>
        </div>
      </div>

      <div className="my-8  flex lg:justify-between overflow-x-scroll md:overflow-hidden md:flex-wrap gap-2 lg:gap-y-0 lg:flex-nowrap">
        {isError ? (
          <div className="text-center w-full py-20">
            Sorry, An error occured fetching the cards. Kindly refresh the page
          </div>
        ) : (
          <>
            {productStats?.map((stat) => (
              <div key={stat.id} className=" flex-1 min-w-[256px]  ">
                {isLoading ? (
                  <CardLoader />
                ) : (
                  <div className="border-[1px]  border-primary-neutral200 rounded-[16px] bg-primary-baseWhite p-6 relative">
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex items-center gap-1">
                        <div
                          className={`p-1 rounded-lg`}
                          style={{ backgroundColor: stat.bgColor }}>
                          <stat.icon color={stat.iconColor} variant="Bold" />
                        </div>
                        <span className="text-sm font-normal">
                          {stat.title}
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-start items-center mt-5">
                      <span className="text-2xl font-bold">
                        {FormatPrice(stat.value)}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </>
        )}
      </div>

      <div>
        <CustomerTable
          customerData={data}
          customerIsError={isError}
          customerIsLoading={isLoading}
          refetchTable={refetch}
        />
      </div>
      {createOrderPanel && (
        <CreateOrder
          closeModal={handleCreateOrder}
          isOpen={createOrderPanel}
          refetch={refetch}
        />
      )}

      {generateReportModal && (
        <OrderReport
          closeModal={handleReportModal}
          isOpen={generateReportModal}
        />
      )}
      {addCustomer && (
        <AddCustomer closeModal={handleAddCustomer} isOpen={addCustomer} />
      )}
    </div>
  );
};
export default ManageCustomer;
