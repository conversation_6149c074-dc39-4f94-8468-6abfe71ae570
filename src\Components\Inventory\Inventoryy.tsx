import { ArrowRight, Box, DocumentDownload, Notification } from "iconsax-react";
import honeyComb from "../../assets/honeyComb.png";
import TableInventory from "./TableInventory";
import { useState, useEffect, useRef } from "react";
import AddProduct from "./AddProduct";
import GenerateReport from "./GenerateReport";
import { Link } from "react-router-dom";
import { RouteNames } from "../../utils/RouteNames";
import { useUserAuthStore } from "../../store/auth";
import { useDateStore } from "../../store/date";
import { useQuery } from "react-query";
import { StatsService } from "../../utils/Services/StatsServices";
import CardLoader from "../../Constants/CardLoader";
import { inventoryServices } from "../../utils/Services/InventoryServices";

const Inventoryy = () => {
  

  const [addProductModal, setAddProductModal] = useState<boolean>(false);
  const [generateReportModal, setGenerateReportModal] =
    useState<boolean>(false);
  const user = useUserAuthStore((state) => state.user);
  const { startDates, endDates } = useDateStore();
  const formattedStartDate = startDates
    ? new Date(startDates).toISOString().split("T")[0]
    : undefined;
  const formattedEndDate = endDates
    ? new Date(endDates).toISOString().split("T")[0]
    : undefined;
  const {
    data: stat,
    isError: statError,
    isLoading: statIsLoading,
    refetch: refetchStats,
  } = useQuery(
    ["overviewstats", user.vendorId, formattedStartDate, formattedEndDate],
    () =>
      StatsService.OverviewStats({
        vendorId: user.vendorId,
        startDate: formattedStartDate,
        endDate: formattedEndDate,
      }),
    {
      enabled: !!user.vendorId,
    }
    );
  
  
  const {
    data,
    isError,
    isLoading,
    refetch: refetchInventory,
  } = useQuery(["allproducts",user.vendorId], () =>
    inventoryServices.getAllProducts(user.vendorId)
  );
  const products = data?.products;
  const category = data?.allCategories;

  const handleAddProductModal = () => {
    setAddProductModal((prev) => !prev);
  };
  const handleReportModal = () => {
    setGenerateReportModal((prev) => !prev);
  };
  // Wrapper function to call both `refetchInventory` and `refetchStats`
  const handleRefetch = async () => {
    try {
      await refetchInventory(); // Wait for inventory refetch to complete
      await refetchStats(); // Then refetch stats data
    } catch (error) {
      console.error("Error during refetch:", error);
    }
  };
  const productStats = [
    {
      id: 1,
      title: "Total Available Products",
      value: stat?.data?.availableProducts?.toLocaleString(),
      iconColor: "#4F4CD8",
      bgColor: "#E6E5F9",
    },
    {
      id: 2,
      title: "In Stock Products",
      value: stat?.data?.inStockProducts?.toLocaleString(),
      iconColor: "#28a745",
      bgColor: "#e0f3e5",
    },
    {
      id: 3,
      title: "Low Stock Products",
      value: stat?.data?.runningLowProducts?.toLocaleString(),
      iconColor: "#ffc107",
      bgColor: "#fff3cd",
    },
    {
      id: 4,
      title: "Out of Stock Products",
      value: stat?.data?.outOfStockProducts?.toLocaleString(),
      iconColor: "#dc3545",
      bgColor: "#f8d7da",
    },
  ];
  useEffect(() => {
    if (addProductModal || generateReportModal) {
      document.body.classList.add("no-scroll");
      if (addProductModal) {
        document.title = "Add Product - Huxxle"; // Set the title when adding a product
      }
      else {
        document.title = "Generate Report - Huxxle"; // Set the title when adding a product
      }
    } else {
      document.body.classList.remove("no-scroll");
      document.title = "Inventory"; // Set the default title
    }

    return () => {
      document.body.classList.remove("no-scroll");
      document.title = "Inventory"; // Reset title on cleanup
    };
  }, [addProductModal, generateReportModal]);
     const originalThemeColor = useRef<string | null>(null);
   useEffect(()=>{
  const meta = document.querySelector<HTMLMetaElement>(
        'meta[name="theme-color"]'
      );
      if (!meta) return;
  
      // on first ever run, capture the original content
      if (originalThemeColor.current === null) {
        originalThemeColor.current = meta.content;
      }
  
      // toggle between white (modal open) and the saved original
      meta.content = addProductModal || generateReportModal  ? "#FFFFFF" : originalThemeColor.current;
   },[addProductModal,generateReportModal])

  return (
    <div className="p-4 md:p-0 flex flex-col gap-[20px]">
      <div className="flex flex-col md:flex-row justify-between gap-5 font-sora ">
        <div className="md:w-[308px] w-full">
          <h1 className="text-2xl font-bold m-0">Inventory Mangement</h1>
          <p className="text-[14px] text-[#7e8494]">
            Manage all your products and restocks here
          </p>
        </div>
        <div className="flex flex-col gap-3 w-full md:w-[368px] md:justify-between md:flex-row items-center">
          <div className="w-full ">
            <button
              onClick={handleAddProductModal}
              className="flex-1 h-[50px] w-full hover:bg-[#6866DE] duration-300  border justify-center border-gray-300 rounded-2xl px-3 py-2 text-[13px] md:text-[14px] flex items-center cursor-pointer bg-[#4f4cd8] text-white font-bold"
            >
              + Add New Product
            </button>
          </div>
          <div className="w-full">
            <button
              onClick={handleReportModal}
              className="flex-1 h-[50px] w-full gap-[4px] border  justify-center border-gray-300 rounded-2xl px-3 py-2 text-[13px] md:text-[14px] flex items-center cursor-pointer bg-white font-bold"
            >
              <DocumentDownload size="16" color="#1a1a1a" />
              Inventory Reports
            </button>
          </div>
        </div>
      </div>

      <div className="my-8  flex lg:justify-between overflow-x-scroll scrollbar-hide md:overflow-hidden md:flex-wrap gap-2 lg:gap-y-0 lg:flex-nowrap">
        {statError ? (
          <div className="text-center w-full py-20">
            Sorry, An error occured fetching the cards. Kindly refresh the page
          </div>
        ) : (
          <>
            {productStats?.map((stat) => (
              <div key={stat.id} className=" flex-1 min-w-[256px]  ">
                {statIsLoading ? (
                  <CardLoader />
                ) : (
                  <div className="border-[1px] border-primary-neutral200 rounded-[16px] bg-primary-baseWhite p-6 relative">
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex items-center gap-2">
                        <div
                          className={`p-1 rounded-lg`}
                          style={{ backgroundColor: stat.bgColor }}
                        > 
                          <Box
                            size="15"
                            color={stat.iconColor}
                            variant="Bold"
                          />
                        </div>
                        <span className="text-sm font-normal">
                          {stat.title}
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-start items-center mt-5">
                      <span className="text-2xl font-bold">{stat.value}</span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </>
        )}
      </div>

      <div className="flex flex-col lg:flex-row justify-between gap-7 mb-8 flex-wrap">
        <div className="p-6 bg-gradient-to-r from-[#4f4cd8] to-[#2a2872] relative rounded-xl flex-1 w-full">
          <h4 className="text-white text-2xl leading-[28.8px]">
            Track Stock Movement
          </h4>
          <p className="text-[#cdccf4] text-sm leading-[22.4px] mt-4 max-w-[450px] w-full font-normal">
            Stay on top of your inventory with our intuitive stock movement
            tracking feature. Easily monitor incoming and outgoing stock to
            ensure your business runs smoothly.
          </p>
          <Link
            to={RouteNames.trackStock}
            className="bg-[#ff9900] w-full  text-white border-none px-3 py-3 rounded-2xl flex md:w-[180px] mt-4 text-sm font-semibold cursor-pointer justify-between items-center hover:bg-[#ee8f00]"
          >
            Track Now
            <ArrowRight size="16" color="#fff" />
          </Link>
          <img
            src={honeyComb}
            alt="honeycomb"
            className="absolute top-1 right-0"
          />
        </div>
        <div className="p-6 border border-[#4f4cd8] rounded-xl flex-1 w-full">
          <div className="flex justify-between items-center">
            <h4 className="text-2xl">Set Up Alerts</h4>
            <Notification size="22" color="#4F4CD8" variant="Bold" />{" "}
          </div>
          <p className="text-sm text-[#5b5b5b] font-normal  max-w-[448px] w-full leading-[22.4px] mt-4">
            Never miss a critical update with our robust inventory alert system.
            Customize alerts to notify you about low stock levels, upcoming
            reorders, and stockouts.
          </p>
          <Link
            to={RouteNames.alert} 
            className="text-[#2a2a2a] w-full border border-[#4f4cd8] px-4 py-3 rounded-xl flex md:w-[180px] mt-4 text-sm font-semibold cursor-pointer justify-between items-center"
          >
            Manage Alerts
            <ArrowRight size="16" color="#2A2A2A" />
          </Link>
        </div>
      </div>
      <div>
        <TableInventory
          products={products}
          category={category}
          refetch={handleRefetch}
          isError={isError}
          isLoading={isLoading}
        />
      </div>

      {addProductModal && (
        <AddProduct
          closeModal={handleAddProductModal}
          isOpen={addProductModal}
          refetch={handleRefetch}
        />
      )}
      {generateReportModal && (
        <GenerateReport
          closeModal={handleReportModal}
          isOpen={generateReportModal}
          title='Inventory'
        />
      )}
    </div>
  );
};
export default Inventoryy;
