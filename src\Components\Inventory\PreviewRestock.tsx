/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQuery } from 'react-query';
import { toast } from 'react-toastify';
import { inventoryServices } from '../../utils/Services/InventoryServices';
import { useUserAuthStore } from '../../store/auth';
import { Notifications } from '../../utils/Services/Notification';
import { useEffect, useRef } from 'react';

const PreviewRestock = ({
  onBack,
  productDetails,
  oldStockPrice,
  productId,
  itemID,
  productRefetch,
  onClose,
  refetch,
  stockRefetch,
  oldStock
}: any) => {
   const modalRef = useRef<HTMLDivElement>(null); // Create a reference to the modal
   
     useEffect(() => {
       if (modalRef.current) {
         modalRef.current.scrollTo({ top: 0, behavior: 'smooth' }); // Scroll the modal container to the top
       }
     }, []);
  const user = useUserAuthStore((state) => state.user);

  let latestStockValue

    if (oldStockPrice) {
       latestStockValue =
        parseInt(oldStockPrice || "0", 10) +
        parseInt(productDetails.stockLevel || "0", 10);
    } else {
       latestStockValue =
        parseInt(oldStock || "0", 10) +
        parseInt(productDetails.stockLevel || "0", 10);
    }
   
  
  

  const mutation = useMutation((formData: FormData) =>
    inventoryServices.editProduct(formData)
  );

   const { refetch: notificationRefetch } = useQuery(
      ["notifications", user.vendorId],
      () => Notifications.getNotification(user.vendorId),
      {
        enabled: !!user.vendorId,
        onSuccess: () => {},
        onError: (error: any) => {
          toast.error("Failed to fetch notifications");
          console.log("notification error:", error);
        },
      }
    );
  const restockProduct = () => {
    const formData = new FormData();
    formData.append("productId", productId);
    formData.append("userEmail", user.userEmail);
    formData.append("vendorId", user.vendorId);
    formData.append("stockLevel", latestStockValue.toString());
    formData.append('editAction', 'PRODRESTOCK');
    formData.append('movementType', 'in')
    formData.append('movementReason','Product Restock')
    formData.append("itemID", itemID);
    if (productDetails.purchasePrice) {
      formData.append("purchasePrice", productDetails.purchasePrice);
    }
    if (productDetails.sellingPrice) {
      formData.append("sellingPrice", productDetails.sellingPrice);
    }
    mutation.mutate(formData, {
      onSuccess: (response) => {
        if (productRefetch) {
          productRefetch();
        }
        if (stockRefetch) {
          stockRefetch()
        }
        toast.success(response?.data?.message);
        notificationRefetch();
        refetch();
        onClose();
      },
      onError: (error: any) => {
        toast.error(error?.response?.data?.error || error?.message);
      },
    });
  };
  return (
    <>
      <div ref={modalRef} className="pl-3  ">
        <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
          Preview Restock Details
        </h2>
        <p className=" text-sm text-[#919191] font-sora ">
          Kindly review your entry and confirm{" "}
        </p>
      </div>
      <div className="p-6 bg-[#f5f5f5] rounded-2xl mt-5 flex flex-col gap-6 mb-60 ">
        <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
          Restock Details
        </h4>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Order Quantity
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {productDetails.stockLevel}
          </p>
        </div>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            New Stock Level
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {latestStockValue.toLocaleString()}
          </p>
        </div>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Purchase price per unit
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            ₦ <span>{productDetails.purchasePrice.toLocaleString()}</span>
          </p>
        </div>

        <div className="  flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Selling price per unit
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            ₦ <span> {productDetails.sellingPrice.toLocaleString()}</span>
          </p>
        </div>
      </div>
      <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2 md:p-10">
        <div className="flex w-full px-7.5 gap-2.5">
          <button
            type="button"
            onClick={onBack}
            className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2"
          >
            Back
          </button>
          <button
            type="button"
            disabled={mutation.isLoading}
            onClick={restockProduct}
            className={`${
              mutation.isLoading ? "opacity-50 cursor-auto" : ""
            } w-full text-[#fcfcfc] bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 h-[49px] rounded-2xl cursor-pointer flex-2 `}
          >
            {mutation.isLoading
              ? "  Restocking Product ..."
              : " Restock Product"}
          </button>
        </div>
      </div>
    </>
  );
};
export default PreviewRestock;
