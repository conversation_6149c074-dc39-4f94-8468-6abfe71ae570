import kyc from '../../assets/kyc.png';
import credit from '../../assets/credit.png';
import { useState } from 'react';
import BankDetails from './BankDetails';
import { useUserAuthStore } from '../../store/auth';
const PaymentSettings = () => {
  const { bank } = useUserAuthStore((state) => state);

  const hasBankDetails =
    bank &&
    bank.account_number?.trim() &&
    bank.bank_name?.trim() &&
    bank.account_name?.trim();
  const [addBankModal, setAddBankModal] = useState<boolean>(false);
  const handleAddBank = () => {
    setAddBankModal((prev) => !prev);
  };
  return (
    <div className="mt-6 flex items-center justify-center w-full">
      <div
        style={{ boxShadow: '0px 5px 50px 0px #1A1A1A14' }}
        className="max-w-[540px] w-full  py-6 md:px-6 rounded-2xl">
        {!addBankModal && !hasBankDetails ? (
          <>
            <div className="flex justify-between  bg-[#FFD99F] p-4 md:p-6 rounded-2xl mb-6">
              <div className="flex items-center gap-2">
                <img src={kyc} alt="kyc" />
                <div>
                  <h2 className="font-bold text-sm md:text-base text-[#1A1A1A]">
                    Complete your Kyc form{' '}
                  </h2>
                  <p className="font-normal text-[#5B5B5B] text-xs md:text-sm max-w-[308px]">
                    Link your account details for easy business process.
                  </p>
                </div>
              </div>
              <span className="text-lg font-bold cursor-pointer flex justify-end p-2 md:p-4">
                &times;
              </span>
            </div>{' '}
            <div className="flex flex-col justify-center items-center pt-12 text-center">
              <img src={credit} alt="image" />
              <h4 className="font-bold text-base md:text-lg text-[#5B5B5B] mt-8">
                Add payment Information
              </h4>
              <p className="text-[#5B5B5B] font-light text-xs md:text-sm px-4 md:px-0 max-w-[265px] ">
                No payment information has been added!{' '}
              </p>
              <button
                type="button"
                onClick={handleAddBank}
                className="text-[#4F4CD8] text-sm font-semibold mt-7">
                link Account
              </button>
            </div>
          </>
        ) : (
          <BankDetails />
        )}
      </div>
    </div>
  );
};
export default PaymentSettings;
