/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ArrowRight,
  // ArrowUp,
  MoneyRecive,
} from 'iconsax-react';
import AnalyticButton from '../../Constants/AnalyticButton';
import { useState } from 'react';
import ByProductModal from './ByProductModal';
import ByCategoryModal from './ByCategoryModal';

const ProductChart = ({
  data,
  isLoading,
  formattedEndDate,
  formattedStartDate,
}: any) => {
  const [salesByProductModal, setSalesByProductModal] =
    useState<boolean>(false);
  const [salesByCategoryModal, setSalesByCategoryModal] =
    useState<boolean>(false);
  const handleSalesByProduct = () => {
    setSalesByProductModal((prev) => !prev);
  };
  const handleSalesByCategory = () => {
    setSalesByCategoryModal((prev) => !prev);
  };

  const topThreeProducts = data?.SalesByProduct;

  const topThreeCategories = data?.SalesByCategory;

  return (
    <div className="w-full font-sora ">
      <div className="border rounded-xl py-4 px-6">
        <div className="flex gap-4 items-center">
          {isLoading ? (
            <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
          ) : (
            <div className="bg-[#E8F2FB] p-1.5 rounded-full">
              <MoneyRecive size="16" color="#1976D2" variant="Bold" />
            </div>
          )}
          {isLoading ? (
            <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
          ) : (
            <p className="font-semibold text-lg text-[#2A2A2A]">
              Sales By Product{' '}
            </p>
          )}
        </div>
        <div className="flex my-6 justify-between">
          {/* <p className="font-semibold text-[#1E1B39] text-2xl">₦ 15,538,345</p>
          <p className="flex items-center rounded-3xl text-[#28A745] text-[10px] p-1 border border-[#AEEBBC]">
            <span> 16%</span> <ArrowUp size="12" />
          </p> */}
        </div>
        {isLoading ? (
          <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] max-w-[320px]"></div>
        ) : (
          <p className="text-[#9B9B9B] text-sm font-normal">
            Here are your top 3 best-selling products for {formattedStartDate}{' '}
            to {formattedEndDate}
          </p>
        )}

        <div className="mt-6">
          {topThreeProducts?.slice(0, 3).map((item: any) => {
            const maxTotalRevenue = Math.max(
              ...topThreeProducts.map((item: any) => item.totalRevenue)
            );
            const percentage = (item.totalRevenue / maxTotalRevenue) * 100;

            return (
              <div className="mt-6" key={item.productName}>
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[36px]"></div>
                ) : (
                  <div
                    className="h-[36px] bg-[#A7CAEC]"
                    style={{
                      width: `${percentage}%`,
                    }}></div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px] my-4"></div>
                ) : (
                  <p className="font-bold text-sm text-[#2A2A2A] mt-4">
                    {item?.productName}
                  </p>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px] my-3"></div>
                ) : (
                  <p className="flex items-center gap-1 text-[#5B5B5B]">
                    Total Revenue
                    <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9]"></span>
                    <span className="font-semibold">
                      ₦ {item?.totalRevenue?.toLocaleString()}
                    </span>
                  </p>
                )}
              </div>
            );
          })}
        </div>
        {/* <div className="mt-6">
          <div
            className=" bg-[#A7CAEC]  h-[36px] "
            style={{ width: `${womenDem || 0}%` }}></div>
          <p className="font-bold text-sm text-[#2A2A2A] mt-4">
            {data?.topThreeProducts[0]?.productName}
          </p>
          <p className="flex items-center gap-1 text-[#5B5B5B]">
            Total Revenue{' '}
            <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9] ">
              {' '}
            </span>{' '}
            <span className="font-semibold">₦{data?.topThreeProducts[0]?.totalRevenue}</span>
          </p>
        </div>
        <div className="mt-6">
          <div
            className=" bg-[#A7CAEC]  h-[36px] "
            style={{ width: `${unisex || 0}%` }}></div>
          <p className="font-bold text-sm text-[#2A2A2A] mt-4">
            {data?.topThreeProducts[1]?.productName}
          </p>
          <p className="flex items-center gap-1 text-[#5B5B5B]">
            Total Revenue{' '}
            <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9] ">
              {' '}
            </span>{' '}
            <span className="font-semibold">₦{data?.topThreeProducts[1]?.totalRevenue}</span>
          </p>
        </div>
        <div className="mt-6">
          <div
            className=" bg-[#A7CAEC]  h-[36px] "
            style={{ width: `${menDem || 0}%` }}></div>
          <p className="font-bold text-sm text-[#2A2A2A] mt-4">
            {data?.topThreeProducts[2]?.productName}
          </p>
          <p className="flex items-center gap-1 text-[#5B5B5B]">
            Total Revenue{' '}
            <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9] ">
              {' '}
            </span>{' '}
            <span className="font-semibold">₦{data?.topThreeProducts[2]?.totalRevenue }</span>
          </p>
        </div> */}
        {isLoading ? (
          <div className="rounded-2xl animate-pulse bg-slate-200 h-[36px] max-w-[100px]"></div>
        ) : (
          <AnalyticButton
            title="View More"
            image={ArrowRight}
            onClick={handleSalesByProduct}
          />
        )}
      </div>
      <div className="border rounded-xl py-4 px-6 mt-6">
        <div className="flex gap-4 items-center">
          {isLoading ? (
            <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
          ) : (
            <div className="bg-[#E8F2FB] p-1.5 rounded-full">
              <MoneyRecive size="16" color="#1976D2" variant="Bold" />
            </div>
          )}
          {isLoading ? (
            <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px] my-4"></div>
          ) : (
            <p className="font-semibold text-lg text-[#2A2A2A]">
              Sales By Category{' '}
            </p>
          )}
        </div>
        <div className="flex my-6 justify-between">
          {/* <p className="font-semibold text-[#1E1B39] text-2xl">₦ 15,538,345</p>
          <p className="flex items-center rounded-3xl text-[#28A745] text-[10px] p-1 border border-[#AEEBBC]">
            <span> 16%</span> <ArrowUp size="12" />
          </p> */}
        </div>
        {isLoading ? (
          <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] max-w-[320px]"></div>
        ) : (
          <p className="text-[#9B9B9B] text-sm font-normal">
            Here are your top 3 best-selling products for {formattedStartDate}{' '}
            to {formattedEndDate}
          </p>
        )}

        <div className="mt-6">
          {topThreeCategories?.slice(0, 3).map((item: any, index: any) => {
            const maxTotalRevenueForCategory = Math.max(
              ...topThreeCategories.map((item: any) => item.totalRevenue)
            );
            const percentage =
              (item.totalRevenue / maxTotalRevenueForCategory) * 100;

            return (
              <div className="mt-6" key={index}>
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[36px]"></div>
                ) : (
                  <div
                    className="h-[36px] bg-[#A7CAEC]"
                    style={{
                      width: `${percentage}%`,
                    }}></div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px] my-4"></div>
                ) : (
                  <p className="font-bold text-sm text-[#2A2A2A] mt-4">
                    {item?.category}
                  </p>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px] my-3"></div>
                ) : (
                  <p className="flex items-center gap-1 text-[#5B5B5B]">
                    Total Revenue
                    <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9]"></span>
                    <span className="font-semibold">
                      ₦ {item?.totalRevenue?.toLocaleString()}
                    </span>
                  </p>
                )}
              </div>
            );
          })}
        </div>
        {/* <div className="mt-6">
          <div
            className=" bg-[#A7CAEC]  h-[36px] "
            style={{ width: `${womenDem || 0}%` }}></div>
          <p className="font-bold text-sm text-[#2A2A2A] mt-4">
            {data?.topThreeCategories[0]?.category}
          </p>
          <p className="flex items-center gap-1 text-[#5B5B5B]">
            Total Revenue{' '}
            <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9] ">
              {' '}
            </span>{' '}
            <span className="font-semibold">
              {data?.topThreeCategories[0]?.totalRevenue}
            </span>
          </p>
        </div>
        <div className="mt-6">
          <div
            className=" bg-[#A7CAEC]  h-[36px] "
            style={{ width: `${unisex || 0}%` }}></div>
          <p className="font-bold text-sm text-[#2A2A2A] mt-4">
            ₦{data?.topThreeCategories[1]?.category}
          </p>
          <p className="flex items-center gap-1 text-[#5B5B5B]">
            Total Revenue{' '}
            <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9] ">
              {' '}
            </span>{' '}
            <span className="font-semibold">
              ₦{data?.topThreeCategories[1]?.totalRevenue}
            </span>
          </p>
        </div>
        <div className="mt-6">
          <div
            className=" bg-[#A7CAEC]  h-[36px] "
            style={{ width: `${menDem || 0}%` }}></div>
          <p className="font-bold text-sm text-[#2A2A2A] mt-4">
            {data?.topThreeCategories[2]?.category}
          </p>
          <p className="flex items-center gap-1 text-[#5B5B5B]">
            Total Revenue{' '}
            <span className="h-1.5 w-1.5 block rounded-full bg-[#D9D9D9] ">
              {' '}
            </span>{' '}
            <span className="font-semibold">
              ₦{data?.topThreeCategories[2]?.totalRevenue}
            </span>
          </p>
        </div> */}
        {isLoading ? (
          <div className="rounded-2xl animate-pulse bg-slate-200 h-[36px] max-w-[100px]"></div>
        ) : (
          <AnalyticButton
            title="View More"
            image={ArrowRight}
            onClick={handleSalesByCategory}
          />
        )}
      </div>
      {salesByProductModal && (
        <ByProductModal
          isOpen={salesByProductModal}
          onClose={handleSalesByProduct}
          data={data}
        />
      )}
      {salesByCategoryModal && (
        <ByCategoryModal
          isOpen={salesByCategoryModal}
          onClose={handleSalesByCategory}
          data={data}
        />
      )}
    </div>
  );
};
export default ProductChart;
