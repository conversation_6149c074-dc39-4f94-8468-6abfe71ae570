/* eslint-disable @typescript-eslint/no-explicit-any */

import { BoxAdd, BoxRemove, BoxTick, DocumentDownload } from "iconsax-react";
import DatePicker from "../../../utils/DatePicker";
import HomeButtons from "../../../Constants/HomeButtons";
import TrackStockTable from "./TrackStockTable";
import { TrackStockServices } from "../../../utils/Services/TrackStockServices";
import { useQuery, useMutation } from "react-query";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../../store/auth";
import { useDateStore } from "../../../store/date";
import React from "react";
import { useEffect } from "react";
import CardLoader from "../../../Constants/CardLoader";
import { inventoryServices } from "../../../utils/Services/InventoryServices";
import { Link } from "react-router-dom";
import { RouteNames } from "../../../utils/RouteNames";

interface Payload {
  startDate?: string;
  endDate?: string;
}



const TrackStock: React.FC<Payload> = () => {
  const userDetails = useUserAuthStore((state) => state.user);
  const businessDetails = useUserAuthStore((state) => state.business);
  const { startDates, endDates } = useDateStore();
  const formattedStartDate = startDates
    ? new Date(startDates).toISOString().split("T")[0]
    : undefined;
  const formattedEndDate = endDates
    ? new Date(endDates).toISOString().split("T")[0]
    : undefined;
  
   useEffect(() => {
     document.title = "Track Stock - Inevntory";
   }, []);

  const stockReportMutation = useMutation(inventoryServices.stockReport);
  const downloadFormat = "csv";

    const handleReport = async () => {
      const payload = {
        businessName: businessDetails.business_name,
        startDate: formattedStartDate || "", // fallback to an empty string or a default value
        endDate: formattedEndDate || "", // fallback to an empty string or a default value
        userEmail: userDetails.userEmail,
        vendorId: userDetails.vendorId,
        format: downloadFormat,
      };

     const handleResponse = (response: any) => {
       if (response && response.data) {
         const { downloadUrl, message } = response.data;

         if (downloadUrl) {
           // Automatically trigger the download
           const isMobile = /iPhone|iPad|iPod|Android/i.test(
             navigator.userAgent
           ); // Detect mobile device
           const fileName = "report"; // Default file name

           if (isMobile) {
             // For mobile devices, use window.location to download if link click doesn't work
             const anchor = document.createElement("a");
             anchor.href = downloadUrl;
             anchor.download = fileName;

             // Create a click event and dispatch it
             const event = new MouseEvent("click", {
               bubbles: true,
               cancelable: true,
               view: window,
             });

             anchor.dispatchEvent(event);

             // Toast for mobile download
             toast.success("Report downloaded successfully.");
           } else {
             // For desktop, create a link and trigger a click event
             const link = document.createElement("a");
             link.href = downloadUrl;
             link.download = fileName;
             link.target = "_blank"; // Ensure it opens in a new tab for desktop
             link.click(); // Trigger download for desktop browsers
             toast.success("Report downloaded successfully.");
           }
         } else {
           // Show the message from the response
           toast.info(message || "No data found for the given period.");
         }
       }
     };


      try {
        stockReportMutation.mutate(payload, {
          onSuccess: handleResponse,
          onError: (error) => {
            console.error("Error generating sales report:", error);
            toast.error("Failed to generate sales report.");
          },
        });
      } catch (error) {
        console.log(error);
      }
    };


  const { data, isLoading, error } = useQuery(
    ["StockDetails", formattedStartDate, formattedEndDate],
    () =>
      TrackStockServices.GetStockTracker({
        vendorId: userDetails.vendorId,
        startDate: formattedStartDate,
        endDate: formattedEndDate,
      }),
    {
      enabled:
        !!userDetails.vendorId && !!formattedStartDate && !!formattedEndDate, // Only run query if email exists
      onSuccess: (data) => {
        // Log the response data to the console
        console.log("Track Stock Response:", data);
      },
      onError: (error: any) => {
        toast.error("Failed to load stats");
        console.error("Error fetching stats:", error);
      },
    }
  );
    if (error) return <div>Error loading data</div>;

  const trackDetails = data?.data
  const tableData = data?.data?.tableData
  console.log('table:', tableData)
 
  return (
    <div className="md:mt-[70px] p-4 md:p-0">
      <div className="flex flex-col md:flex-row gap-2 md:justify-between w-full ">
        <div className="header-col1 md:w-[626px]">
          <h1 className="text-2xl font-bold">Track Stock Mangement</h1>
          <Link to={RouteNames.inventory} className="track-stock-subheader text-[#7b7b7b] text-sm cursor-pointer">
            Inventory Management &gt;&gt;{" "}
            <span className="text-[#4f4cd8]"> Stock Management</span>
          </Link>
        </div>
        <div className="flex md:w-[560px] w-full md:flex-row flex-col items-center gap-4">
          <div className="w-full">
            <DatePicker />
          </div>
          <div className="w-full">
            <HomeButtons
              title={
                stockReportMutation.isLoading
                  ? "Downloading File"
                  : "Download csv"
              }
              textColor="text-primary-neutralst1"
              bgColor="bg-primary-baseWhite"
              border="border-[1px]"
              borderColor="border-primary-neutral200"
              image={DocumentDownload}
              onClick={handleReport}
            />
          </div>
        </div>
      </div>

      <div className="mt-8  flex lg:justify-between overflow-x-scroll scrollbar-hide md:overflow-hidden md:flex-wrap gap-2 lg:gap-y-0 lg:flex-nowrap">
        {isLoading ? (
          <CardLoader />
        ) : (
          <div className="border-[1px] flex-1 min-w-[256px] border-primary-neutral200 rounded-[16px] bg-primary-baseWhite px-[24px] py-[16px]">
            <div className="flex  items-center gap-2">
              <BoxAdd
                variant="Bold"
                className="text-[#28A745] bg-[#EAF7ED] px-[4px] rounded-md"
                size={32}
              />
              <h2 className="font-sora text-primary-neutral1000 text-[14px]">
                Total In Movement
              </h2>
            </div>
            <p className="font-sora mt-[15px] text-[24px] text-primary-neutralt1 font-bold">
              {trackDetails?.totalInMovement} Units
            </p>
          </div>
        )}

        {isLoading ? (
          <CardLoader />
        ) : (
          <div className="border-[1px] flex-1 min-w-[256px] border-primary-neutral200 rounded-[16px] bg-primary-baseWhite px-[24px] py-[16px]">
            <div className="flex  items-center gap-2">
              <BoxRemove
                variant="Bold"
                className="text-[#DC3545] bg-[#FFF5E6] px-[4px] rounded-md"
                size={32}
              />
              <h2 className="font-sora text-primary-neutral1000 text-[14px]">
                Total Out Movement{" "}
              </h2>
            </div>
            <p className="font-sora mt-[15px] text-[24px] text-primary-neutralt1 font-bold">
              {trackDetails?.totalOutMovement} Units
            </p>
          </div>
        )}

        {isLoading ? (
          <CardLoader />
        ) : (
          <div className="border-[1px] flex-1 min-w-[256px] border-primary-neutral200 rounded-[16px] bg-primary-baseWhite px-[24px] py-[16px]">
            <div className="flex  items-center gap-2">
              <BoxTick
                variant="Bold"
                className="text-[#4F4CD8] bg-[#E6E5F9] px-[4px] rounded-md"
                size={32}
              />
              <h2 className="font-sora text-primary-neutral1000 text-[14px]">
                Net Inventory Change{" "}
              </h2>
            </div>
            <p className="font-sora mt-[15px] text-[24px] text-primary-neutralt1 font-bold">
              {trackDetails?.netInventoryChange} Units
            </p>
          </div>
        )}
      </div>
      <TrackStockTable data={tableData} dataIsLoading={isLoading} />
    </div>
  );
};
export default TrackStock;
