import { ArrowDown } from 'iconsax-react';
import { ReactNode } from 'react';

interface AnalyticCardProps {
  title: string;
  churnRate: string;
  percentageChange: string;
  description: string;
  isPositive?: boolean;
  arrowColor?: string;
  backgroundColor?: string;
  hasMaxWidth?: boolean;
  isLoading?: boolean;
  icon?: ReactNode;
}

const AnalyticCard: React.FC<AnalyticCardProps> = ({
  title,
  churnRate,
  percentageChange,
  description,
  isPositive = true,
  icon,
  backgroundColor = '#FFF2DF',
  hasMaxWidth = true,
  isLoading,
}) => {
  return (
    <div
      className={`border rounded-xl py-5 px-6 mt-7 ${
        hasMaxWidth ? 'max-w-[448px] w-full' : ''
      }`}>
      <div className="flex gap-4 items-center">
        {isLoading ? (
          <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
        ) : (
          <div className={`p-1.5 rounded-md`} style={{ backgroundColor }}>
            {icon}
          </div>
        )}
        {isLoading ? (
          <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[120px] "></div>
        ) : (
          <p className="font-semibold text-lg text-[#2A2A2A]">{title}</p>
        )}
      </div>
      <div className="flex my-6 justify-between items-center">
        {isLoading ? (
          <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[60px] "></div>
        ) : (
          <p className="font-semibold text-[#1E1B39] text-2xl">{churnRate}</p>
        )}
        {isLoading ? (
          <div className="animate-pulse bg-slate-200 rounded-3xl w-[40px] h-5 "></div>
        ) : (
          <p
            className={`flex items-center rounded-xl text-[10px] p-0.5 border ${
              isPositive
                ? 'text-[#28A745] border-[#AEEBBC]'
                : 'text-[#FF0000] border-[#FFCCCC]'
            }`}>
            <span>{percentageChange} %</span> <ArrowDown size="12" />
          </p>
        )}
      </div>
      <div className="text-[#9B9B9B] text-sm font-normal flex gap-2 items-center">
        {isLoading ? (
          <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px]  w-full"></div>
        ) : (
          <span>{description}</span>
        )}
      </div>
    </div>
  );
};

export default AnalyticCard;
