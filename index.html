<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/png" href="/Favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
  <meta name="theme-color" content="#4F4CD8" />
  
  <link rel="manifest" href="/manifest.webmanifest" />
  <link rel="apple-touch-icon" sizes="180x180" href="/icons/purple180.png">
  <title>Vendor Login | Huxxle</title>
  <meta name="description" content="Efficiently manage your online store with Huxxle's vendor tools, including inventory tracking and business analytics.">
  <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://vendor.huxxle.com" />

  <meta property="og:title" content="Huxxle: Business Tools | A.I Powered | Inventory | Website">
  <meta property="og:description" content="Access your Huxxle Vendor Dashboard and take control of your online store.">
  <meta property="og:url" content="https://huxxle.com">
  <meta property="og:type" content="website">
  <meta property="og:image" content="https://katajereassets.blob.core.windows.net/assets/huxxleThumbnail.png"> <!-- replace with actual image URL -->
  <meta property="og:site_name" content="Huxxle">
  <meta name="keywords" content="ecommerce website, business website, online business, business tool">

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Huxxle: Business Tools | A.I Powered | Inventory | Website">
  <meta name="twitter:description" content="Efficiently manage your online store with Huxxle's vendor tools, including inventory tracking and business analytics.">
  <meta name="twitter:image" content="https://katajereassets.blob.core.windows.net/assets/huxxleThumbnail.png"> <!-- Replace with actual image URL -->
  <meta name="twitter:site" content="@join_huxxle"> 
  <meta name="twitter:creator" content="@join_huxxle"> 




  <script>
    (function waitForMetaTag() {
      // Function to check for the meta tag
      const vendorIdMeta = document.querySelector('meta[name="vendor-id"]');

      if (vendorIdMeta) {
        // Meta tag found, proceed with analytics initialization
        console.log("Meta tag found:", vendorIdMeta);
        console.log("Vendor ID:", vendorIdMeta.content);

        // Load analytics script
        var s = document.createElement("script");
        s.src = "https://api.huxxle.com/huxxlelytics/analytics.js";
        s.async = true;

        // Initialize analytics with vendor ID
        window.huxxleLytics = {
          vendorId: vendorIdMeta.content, // Vendor ID from meta tag
          appId: "VBeta1",
          apiKey: "aB3~dE5fGh",
          apiBaseUrl: "https://api.huxxle.com",
        };
        document.head.appendChild(s); // Append the analytics script
      } else {
        // Meta tag not found, retry after 50ms
        setTimeout(waitForMetaTag, 50);
      }
    })();
  </script>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>
