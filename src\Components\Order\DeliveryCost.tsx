/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from "react";
import { orderServices } from "./../../utils/Services/Order";
import { useQuery, useMutation } from "react-query";
import { useUserAuthStore } from "../../store/auth";
import { ReceiptEdit } from "iconsax-react";
import ConfirmationModal from "../../Constants/ConfirmationModal";

import { IoClose } from "react-icons/io5";
import { toast } from "react-toastify";
import EditDelivery from "../../Constants/EditDelivery";
import CreateDelivery from "../../Constants/CreateDelivery";

interface OrderProps {
  closeModal: () => void;
  isOpen: boolean;
}

const DeliveryCost: React.FC<OrderProps> = ({ closeModal, isOpen }) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    return () => {
      document.body.classList.remove("no-scroll");
    };
  }, [isOpen]);

  const user = useUserAuthStore((state) => state.user);
  const [deliveryFee, setDeliveryFee] = React.useState<any>(null);
  const [confirmModal, setConfirmModal] = useState(false)
  const [selectedDeliveryId, setSelectedDeliveryId] = useState<string | null>(null);
  const deleteMutate = useMutation(orderServices.deleteDelivery)
  const[updateDelivery, setUpdateDelivery] = useState(false)
  const [addDelivery, setAddDelivery] = useState(false)
  const { data, isLoading, isError,refetch } = useQuery(
    ["deliveryFee", user.vendorId],
    () => orderServices.getDeliveryFee(user.vendorId),
    {
      enabled: !!user.vendorId,
    }
  );

  const handleDeleteModal = (deliveryId: string)=>{
    setConfirmModal((prev)=> !prev)
    setSelectedDeliveryId(deliveryId);
  }

  const handleupdate = ()=>{
    setUpdateDelivery((prev)=>!prev)
  }
  const handleAddDelivery = ()=>{
    setAddDelivery((prev)=>!prev)
  }

  const handleDeleteDelivery = () => {
    if (selectedDeliveryId && user.vendorId) {
      deleteMutate.mutate({ deliveryId: selectedDeliveryId, vendorId: user.vendorId }, {
        onSuccess: (response) => {
          // Optionally refetch data or update the UI
          refetch()
          toast(response?.data?.message)
          setConfirmModal(false); // Close the modal after successful deletion
        },
        onError: (error: any) => {
          // Handle error
          console.error("Error deleting delivery fee:", error);
        },
      });
    }
  };

  useEffect(() => {
    if (data) {
      setDeliveryFee(data);
      console.log("Delivery Fee Data: ", data);
    }
  }, [data]);

  const handleEditDelivery = (deliveryId: string) => {
    // Find the delivery fee object that matches the deliveryId
    const deliveryToEdit = deliveryFee?.deliveryFees.find((fee: any) => fee.deliveryId === deliveryId);
    
    if (deliveryToEdit) {
      // Set the deliveryToEdit in the state
      setSelectedDelivery(deliveryToEdit);
      // Open the edit delivery modal
      setUpdateDelivery(true);
    } else {
      console.error("Delivery fee not found for ID:", deliveryId);
    }
  };

  const [selectedDelivery, setSelectedDelivery] = useState<any>(null);

  return (
    <div className="fixed font-sora  bg-black/10 top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 w-full h-full md:w-[580px]  bg-white backdrop-blur-[10px] z-30 p-2 transition-transform duration-500 ease-in-out ${
          isOpen ? "translate-x-0 " : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="h-[calc(100vh-80px)] overflow-y-auto scrollbar-none hidescroll">
            <div className="pl-6 ">
              <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                Delivery Fee
              </h2>
              <p className=" text-sm text-[#919191] font-sora ">
                Set and Manage Your Delivery Fee
              </p>
            </div>

            <div className="p-6 rounded-2xl flex flex-col gap-6 last:mb-[200px]">
              <div className="flex items-center justify-between">
                <h3 className="text-[#2A2A2A] hidden md:block font-sora font-semibold text-[16px]">
                  Delivery Information
                </h3>
                <h3 onClick={handleAddDelivery} className="cursor-pointer text-primary-purple font-sora text-[16px] font-semibold">
                  + Set New Delivery Fee
                </h3>
              </div>

              {isLoading ? (
                <div className="grid gap-5">
                  <div className="flex w-full justify-between">
                    <div className="w-80% h-[20px] bg-[#3832a9] animate-pulse rounded-2xl"></div>
                    <div className="w-20% h-[20px] bg-[#27218f] animate-pulse rounded-2xl"></div>
                  </div>
                </div>
              ) : isError ? (
                <div>Error fetching delivery fee.</div>
              ) : (
                <>
                  {deliveryFee?.deliveryFees.map((fee: any) => (
                    <div
                      key={fee.deliveryId}
                      className="w-full  flex-col gap-[20px] mt-[20px] md:mt-0 bg-[#F5F5F5] md:bg-transparent rounded-2xl px-4 py-3 md:px-0 md:py-0 flex md:flex-row md:items-center md:justify-between"
                    >
                      <div className="flex items-center md:py-3 border-b-[1px] md:border-b-0 border-[#DCDCDC] pb-[20px] md:px-4 justify-between w-full md:w-[380px] bg-[#F5F5F5] md:rounded-2xl">
                        <p className="font-sora text-[#5B5B5B] text-[14px]">
                          {fee.deliveryTitle}
                        </p>
                        <p className="font-semibold font-sora text-[#2A2A2A] text-14px">
                          ₦ {fee.deliveryValue.toLocaleString()}
                        </p>
                      </div>
                      <div className="flex">
                        <button onClick={() => handleEditDelivery(fee.deliveryId)} className="bg-[#E6E5F9] w-full flex justify-center gap-3 items-center md:hidden md:w-[44px] h-[44px] rounded-2xl p-[10px] text-primary-purple">
                          <ReceiptEdit /> Edit
                        </button>
                        <button  onClick={()=>handleDeleteModal(fee.deliveryId)} className="gap-3 w-full md:w-[44px] h-[44px] md:hidden flex justify-center items-center font-semibold rounded-2xl  text-[#DC3545]">
                          <IoClose size={24} /> Remove
                        </button>
                      </div>
                      <div></div>
                      <button onClick={() => handleEditDelivery(fee.deliveryId)} className="bg-[#E6E5F9] hidden md:block w-[44px] h-[44px] rounded-2xl p-[10px] text-primary-purple">
                        <ReceiptEdit />
                      </button>
                      <button onClick={()=>handleDeleteModal(fee.deliveryId)} className="bg-[#FAF2F2] w-[44px] h-[44px] hidden md:flex justify-center items-center font-semibold rounded-2xl  text-[#DC3545]">
                        <IoClose size={24} />
                      </button>
                    </div>
                  ))}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      {confirmModal && ( <ConfirmationModal actionButtonDisabled={deleteMutate.isLoading} actionButtonStyle="bg-[#DC3545]" isOpen={confirmModal} text="This action will delete this delivery information. This action cannot be undone" actionButtonText="Delete Delivery Fee" cancelButtonText="No, Cancel" title="Are you sure you want to delete this delivery Information" onCancelClick={()=>setConfirmModal(false)} onActionClick={handleDeleteDelivery} />)}
        {updateDelivery && (<EditDelivery selectedDelivery={selectedDelivery} refetch={refetch} isOpen={updateDelivery} closeModal={handleupdate}/>)}
        {addDelivery && (<CreateDelivery refetch={refetch} isOpen={addDelivery} closeModal={handleAddDelivery}/>)}
    </div>
  );
};

export default DeliveryCost;
