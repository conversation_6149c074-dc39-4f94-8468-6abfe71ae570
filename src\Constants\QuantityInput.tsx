import React from "react";

interface QuantityInputProps {
  label: string;
  name: string;
  value: string | number;
  placeholder?: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const QuantityInput: React.FC<QuantityInputProps> = ({
  label,
  name,
  value,
  placeholder,
  onChange,
}) => {
  const increment = () => {
    const newValue = Number(value) || 0;
    onChange({
      target: { name, value: (newValue + 1).toString() },
    } as React.ChangeEvent<HTMLInputElement>);
  };

  const decrement = () => {
    const newValue = Number(value) || 0;
    if (newValue > 0) {
      onChange({
        target: { name, value: (newValue - 1).toString() },
      } as React.ChangeEvent<HTMLInputElement>);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const allowedKeys = [
      "Backspace",
      "Tab",
      "<PERSON>Left",
      "ArrowRight",
      "Delete",
    ];
    if (!/[0-9]/.test(e.key) && !allowedKeys.includes(e.key)) {
      e.preventDefault();
    }
  };

  return (
    <div className="flex w-full flex-col gap-2">
      <label
        htmlFor={name}
        className="font-sora text-sm text-[#5B5B5B]"
      >
        {label}
      </label>
      <div className="flex w-full px-4 border-[1px] border-[#dcdcdc] rounded-xl h-[48px] items-center justify-between">
        <button
          type="button"
          onClick={decrement}
          className="w-[20px] h-[20px] flex items-center justify-center rounded-full border border-[#2a2a2a] text-xl select-none"
          aria-label="Decrease quantity"
        >
          -
        </button>
        <input
          id={name}
          name={name}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          onKeyDown={handleKeyDown}
          min={0}
          className="text-center text-sm py-2 w-fit border-none outline-none"
        />
        <button
          type="button"
          onClick={increment}
          className="w-[20px] h-[20px] flex items-center justify-center rounded-full border border-[#2a2a2a] text-xl select-none"
          aria-label="Increase quantity"
        >
          +
        </button>
      </div>
    </div>
  );
};

export default QuantityInput;
