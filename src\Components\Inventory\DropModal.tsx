/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useRef } from 'react';

interface ModalProps {
  productId: any;
  position: { top: number };
  onClose: () => void;
  onViewProduct: (row: any) => void;
  onEditProduct: (row: any) => void;
  onAdjustProduct: (row: any) => void;
  onRestockProduct: (row: any) => void;
  onDeleteProduct: (row: any) => void;
}

const DropModal: React.FC<ModalProps> = ({
  productId,
  position,
  onClose,
  onViewProduct,
  onEditProduct,
  onAdjustProduct,
  onRestockProduct,
  onDeleteProduct,
}) => {
  const modalRef = useRef<HTMLDivElement | null>(null);

  const handleOverlayClick = (event: any) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      onClose();
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleOverlayClick);
    return () => {
      document.removeEventListener('mousedown', handleOverlayClick);
    };
  }, []);

  return (
    <div
      ref={modalRef}
      className="absolute bg-white rounded-2xl font-sora border border-gray-300 shadow-lg z-50 w-[180px]   lg:left-[80%] "
      style={{ top: `${position.top}px` }}>
      <div className="cursor-pointer">
        <p
          onClick={() => onViewProduct(productId)}
          className="py-2 px-4 text-[#5B5B5B]  rounded-t-2xl font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          View Product
        </p>
        <p
          onClick={() => onEditProduct(productId)}
          className="py-2 px-4 text-[#5B5B5B] font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          Edit Product
        </p>
        <p
          onClick={() => onAdjustProduct(productId)}
          className="py-2 px-4 text-[#5B5B5B] font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          Adjust Product
        </p>
        <p
          onClick={() => onRestockProduct(productId)}
          className="py-2 px-4 text-[#5B5B5B] font-normal hover:bg-[#E6E5F9] hover:text-[#4F4CD8] hover:border hover:border-[#B4B2EE]">
          Restock Product
        </p>
        <p
          onClick={() => onDeleteProduct(productId)}
          className="py-2 px-4  text-[#DC3545] font-normal">
          Delete Product
        </p>
      </div>
    </div>
  );
};

export default DropModal;
