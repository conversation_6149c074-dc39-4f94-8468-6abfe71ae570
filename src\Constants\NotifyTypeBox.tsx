import { ArrowDown2 } from 'iconsax-react';
import { useState } from 'react';

interface NotifyTypeBoxProps {
  text?: string;
  type?: string;
}
const NotifyTypeBox: React.FC<NotifyTypeBoxProps> = ({ text, type }) => {
  const [isToggled, setIsToggled] = useState<boolean>(false);
  const handleToggle = () => {
    setIsToggled(!isToggled);
  };
  return (
    <div className="flex justify-between items-center mt-6">
      <p className="font-light text-[#1A1A1A] text-xs md:text-sm max-w-[90px] sm:max-w-[148px] w-full">{text}</p>
      <div className=" flex items-center max-w-[90px] sm:max-w-[150px] w-full justify-center text-nowrap gap-1 md:gap-3 border border-[#DCDCDC] rounded-2xl py-3 md:px-4">
        <p className="text-[#2A2A2A] text-[10px] md:text-sm font-semibold">{type}</p>
        <ArrowDown2 size="16" />
      </div>
      <div
        className={`w-10 h-5 flex items-center rounded-full p-1 cursor-pointer ${
          isToggled ? 'bg-[#4F4CD8]' : 'bg-gray-300'
        }`}
        onClick={handleToggle}>
        <div
          className={`bg-white w-4 h-4 rounded-full shadow-md transform ${
            isToggled ? 'translate-x-5' : 'translate-x-0'
          } transition`}></div>
      </div>
    </div>
  );
};
export default NotifyTypeBox;
