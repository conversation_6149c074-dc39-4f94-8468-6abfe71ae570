// ESM Vite Runner
import { createRequire } from 'module';
import { execSync } from 'child_process';

// Apply the crypto polyfill
const require = createRequire(import.meta.url);
require('./crypto-polyfill.cjs');

// Run Vite using npx to ensure it can be found
try {
  console.log("Starting Vite build...");
  execSync('npx vite build', { stdio: 'inherit' });
  console.log("Vite build completed successfully");
} catch (error) {
  console.error("Vite build failed:", error);
  process.exit(1);
}
