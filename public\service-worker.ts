/// <reference lib="webworker" />
// Add WebWorker types to ensure proper type checking
declare const self: ServiceWorkerGlobalScope;
export {};

const CACHE_NAME = "my-pwa-cache-v3"; // Updated cache version
const API_CACHE_NAME = "api-cache-v2"; // Separate cache for API responses

// Version tracking for easier updates
const VERSION = "1.0.1";
console.log(`Service Worker version ${VERSION} loading`);

const urlsToCache = [
  "/",
  "/manifest.json",
  "/Favicon.png",
  "icons/192White.png",
  "icons/512White.png",
  "/static/js/bundle.js",
];

// Network First endpoints (critical data requiring up-to-date information)
const NETWORK_FIRST_ENDPOINTS = [
  "/auth/signup", 
  "/auth/verify-otp",
  "/auth/login",
  "/business/save-business",
  "/product/add-product",
  "/product/update-product",
  "/order/edit-order",
  "/order/create-delivery-fee",
  "/promo/create-promo",
  "/alert/notifications/read",
  "/business/profile",
  "/feedback",
  // Adding the requested endpoints to network-first
  "/stats",
  "/product/products",
  "/order/orders",
  "/customers/get-customers"
];

// Stale While Revalidate endpoints (can tolerate slight staleness for faster loading)
const STALE_WHILE_REVALIDATE_ENDPOINTS = [
  // Removed the 4 endpoints that are now using network-first
  "/product/product-details",
  "/order/order-details",
  "/customers/customer-profile",
  "/analytics/analysis",
  "/analytics/get-insight",
  "/stock/stock-tracker",
  "/stock/stock-tracker-details",
  "/ai-recommendation",
  "/order/get-delivery-fees",
  "/promo/get-promo",
  "/promo/promo-details",
  "/alert/notifications",
  "/download/generate-sales-summary-report",
  "/download/generate-stock-movement-report",
  "/ai/enhance-description",
  "/download/generate-inventory-valuation-report"
];

// Endpoints that should never be cached
const NO_CACHE_ENDPOINTS = [
  "/analytics/track",
  "/auth/refresh-token",
  "/auth/logout"
];

// Maximum age of cache for SWR strategy (30 minutes for fresher data)
const SWR_MAX_AGE = 30 * 60 * 1000;



// Install event - Cache static assets
self.addEventListener("install", (event) => {
  console.log(`Service Worker version ${VERSION} installing`);
  
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      console.log('Caching app shell and static assets');
      return cache.addAll(urlsToCache);
    })
    .catch(error => {
      console.error('Pre-caching failed:', error);
    })
  );
  self.skipWaiting(); // Immediately activate the service worker
});

// Fetch event - Custom behavior based on request URL
self.addEventListener("fetch", (event) => {

// let the browser handle blob: and data: URLs itself
  if (event.request.url.startsWith("blob:") ||
      event.request.url.startsWith("data:")) {
    return;
  }

  // Only process if the request is a GET request
  if (event.request.method !== 'GET') {
    return;
  }

  // Safety check for URL construction
  try {
    const url = new URL(event.request.url);
    
    // Only handle same-origin requests or requests to our API
    const shouldHandle = url.origin === self.location.origin || 
                         url.origin.match(/https:\/\/(api\.|)huxxle\.com/);
    
    if (!shouldHandle) {
      return;
    }
    
    // Check if the request should never be cached
    const shouldNotCache = NO_CACHE_ENDPOINTS.some(endpoint => 
      url.pathname.includes(endpoint)
    );
    
    if (shouldNotCache) {
      // Pass through to network only
      return;
    }
    
    // Check which strategy to use based on the endpoint
    const isNetworkFirst = NETWORK_FIRST_ENDPOINTS.some(endpoint => 
      url.pathname.includes(endpoint)
    );
    
    const isStaleWhileRevalidate = STALE_WHILE_REVALIDATE_ENDPOINTS.some(endpoint => 
      url.pathname.includes(endpoint)
    );
    
    // Check for API calls that should be handled specially
    const isApiCall = url.origin.includes('api.huxxle.com') || 
                      url.pathname.includes('/pg/');
    
    if (isNetworkFirst || (isApiCall && !isStaleWhileRevalidate)) {
      handleNetworkFirst(event);
    } else if (isStaleWhileRevalidate) {
      handleStaleWhileRevalidate(event);
    } else {
      handleCacheFirst(event);
    }
  } catch (error) {
    console.error('Error processing fetch event:', error);
    // Let the browser handle the request normally
    return;
  }
});

// Network-first strategy implementation - always try network first, fallback to cache
function handleNetworkFirst(event: FetchEvent) {
  event.respondWith(
    fetch(event.request.clone(), { 
      // Use no-store to prevent browser from using its own HTTP cache
      cache: 'no-store'
    })
      .then(networkResponse => {
        if (!networkResponse || networkResponse.status !== 200) {
          return networkResponse;
        }

        // Clone the response before using it
        const responseToCache = networkResponse.clone();
        
        // Cache the successful network response with timestamp
        caches.open(API_CACHE_NAME).then(cache => {
          const headers = new Headers(responseToCache.headers);
          headers.append('sw-fetched-on', new Date().toISOString());
          
          const responseToStore = new Response(responseToCache.body, {
            status: responseToCache.status,
            statusText: responseToCache.statusText,
            headers: headers
          });
          
          cache.put(event.request, responseToStore);
        }).catch(err => console.error('Caching error:', err));
        
        return networkResponse;
      })
      .catch(() => {
        // If network fails, try to get from cache
        return caches.match(event.request).then(cachedResponse => {
          if (cachedResponse) {
            // Check when this response was cached
            const fetchedTime = cachedResponse.headers.get('sw-fetched-on');
            const cacheAge = fetchedTime ? 
              Date.now() - new Date(fetchedTime).getTime() : 
              Number.MAX_VALUE;
              
            // If cache is less than 24 hours old, use it as fallback
            if (cacheAge < 24 * 60 * 60 * 1000) {
              return cachedResponse;
            }
          }
          
          // Otherwise return an error response
          return new Response(JSON.stringify({
            error: 'Network request failed and cache is unavailable or too old',
            offline: true,
            timestamp: new Date().toISOString()
          }), {
            status: 503,
            statusText: 'Service Unavailable',
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-store'
            }
          });
        });
      })
  );
}

// Stale-while-revalidate strategy - use cache immediately while updating in background
function handleStaleWhileRevalidate(event: FetchEvent) {
  event.respondWith(
    caches.match(event.request).then(cachedResponse => {
      // Check if we have a valid cached response
      const cachedResponseValid = cachedResponse && 
        cachedResponse.headers.has('sw-fetched-on') &&
        (Date.now() - new Date(cachedResponse.headers.get('sw-fetched-on') || '').getTime()) < SWR_MAX_AGE;
      
      // Create a promise that will update the cache
      const fetchPromise = fetch(event.request.clone(), {
        // Use no-cache to validate with the server but allow fallback to cache
        cache: 'no-cache'
      }).then(networkResponse => {
        if (!networkResponse || networkResponse.status !== 200) {
          return networkResponse;
        }
        
        // Update the cache with the fresh response
        const responseToCache = networkResponse.clone();
        
        // Add timestamp header to track cache age
        const headers = new Headers(responseToCache.headers);
        headers.append('sw-fetched-on', new Date().toISOString());
        
        const responseToStore = new Response(responseToCache.body, {
          status: responseToCache.status,
          statusText: responseToCache.statusText,
          headers: headers
        });
        
        caches.open(API_CACHE_NAME).then(cache => {
          cache.put(event.request, responseToStore);
        }).catch(err => console.error('Caching error:', err));
        
        return networkResponse;
      }).catch(error => {
        console.error('Failed to fetch:', error);
        // If network fetch fails, return the cached response if available
        return cachedResponse || new Response(JSON.stringify({
          error: 'You are offline and no cached data is available',
          offline: true,
          timestamp: new Date().toISOString()
        }), {
          status: 503,
          statusText: 'Service Unavailable',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store'
          }
        });
      });

      // Return the cached response immediately if valid, otherwise wait for network
      if (cachedResponseValid && cachedResponse) {
        // If we have a valid cached response, use it immediately
        // And trigger cache update in the background
        fetchPromise.catch(() => {/* Silently handle background fetch errors */});
        return cachedResponse;
      } else {
        // No valid cache, we must wait for the network response
        return fetchPromise;
      }
    })
  );
}

// Cache-first strategy - primarily for static assets
function handleCacheFirst(event: FetchEvent) {
  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          // For non-API static resources, refresh the cache in the background 
          // if it's older than a day
          const fetchedTime = cachedResponse.headers.get('sw-fetched-on');
          if (fetchedTime) {
            const cacheAge = Date.now() - new Date(fetchedTime).getTime();
            if (cacheAge > 24 * 60 * 60 * 1000) {
              // Update cache in the background for static assets
              fetch(event.request.clone())
                .then(networkResponse => {
                  if (networkResponse && networkResponse.status === 200) {
                    const responseToCache = networkResponse.clone();
                    const headers = new Headers(responseToCache.headers);
                    headers.append('sw-fetched-on', new Date().toISOString());
                    
                    const responseToStore = new Response(responseToCache.body, {
                      status: responseToCache.status,
                      statusText: responseToCache.statusText,
                      headers: headers
                    });
                    
                    caches.open(CACHE_NAME).then(cache => {
                      cache.put(event.request, responseToStore);
                    });
                  }
                })
                .catch(() => {/* Silently ignore background refresh errors */});
            }
          }
          return cachedResponse; // Return cached response
        }
        
        // No cache hit, go to network
        return fetch(event.request.clone()).then((networkResponse) => {
          if (!networkResponse || networkResponse.status !== 200) {
            return networkResponse;
          }
          
          // Cache the network response for future use with timestamp
          const responseToCache = networkResponse.clone();
          const headers = new Headers(responseToCache.headers);
          headers.append('sw-fetched-on', new Date().toISOString());
          
          const responseToStore = new Response(responseToCache.body, {
            status: responseToCache.status,
            statusText: responseToCache.statusText,
            headers: headers
          });
          
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseToStore);
          }).catch(err => console.error('Caching error:', err));
          
          return networkResponse;
        });
      })
      .catch(() => {
        // For API requests, return a JSON error
        return new Response(JSON.stringify({
          error: 'Resource unavailable and no cached version exists',
          offline: true,
          timestamp: new Date().toISOString()
        }), {
          status: 503,
          statusText: 'Service Unavailable',
          headers: { 'Content-Type': 'application/json' }
        });
      })
  );
}

// Activate event - Clean up old caches and take control immediately
self.addEventListener("activate", (event) => {
  console.log(`Service Worker version ${VERSION} activating`);
  
  const cacheWhitelist = [CACHE_NAME, API_CACHE_NAME];
  event.waitUntil(
    caches.keys().then((cacheNames) =>
      Promise.all(
        cacheNames.map((cacheName) => {
          if (!cacheWhitelist.includes(cacheName)) {
            console.log('Deleting outdated cache:', cacheName);
            return caches.delete(cacheName);
          }
          return Promise.resolve(); // Explicit return for TypeScript
        })
      )
    ).then(() => {
      console.log('Service Worker activated and took control');
    })
  );
  self.clients.claim(); // Take control of all pages immediately
});

// Listen for message events (useful for communication with the main thread)
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHES') {
    event.waitUntil(
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            console.log('Clearing cache:', cacheName);
            return caches.delete(cacheName);
          })
        );
      }).then(() => {
        console.log('All caches cleared successfully');
        // Notify the client that sent the message
        if (event.source && 'postMessage' in event.source) {
          event.source.postMessage({
            type: 'CACHES_CLEARED',
            success: true
          });
        }
      })
    );
  }
  
  // Add support for updating specific cached resources
  if (event.data && event.data.type === 'UPDATE_RESOURCE') {
    const urls = event.data.urls || [];
    if (urls.length > 0) {
      event.waitUntil(
        Promise.all(
          urls.map((url: string) => {
            // Force a network fetch for this resource
            return fetch(url, { cache: 'reload' })
              .then(response => {
                if (response.ok) {
                  const headers = new Headers(response.headers);
                  headers.append('sw-fetched-on', new Date().toISOString());
                  
                  const responseToStore = new Response(response.clone().body, {
                    status: response.status,
                    statusText: response.statusText,
                    headers: headers
                  });
                  
                  // Update in both caches to be safe
                  return Promise.all([
                    caches.open(CACHE_NAME).then(cache => cache.put(url, responseToStore.clone())),
                    caches.open(API_CACHE_NAME).then(cache => cache.put(url, responseToStore.clone()))
                  ]);
                }
              })
              .catch(err => console.error(`Failed to update resource ${url}:`, err));
          })
        ).then(() => {
          console.log('Resources updated successfully');
          if (event.source && 'postMessage' in event.source) {
            event.source.postMessage({
              type: 'RESOURCES_UPDATED',
              success: true
            });
          }
        })
      );
    }
  }
});
