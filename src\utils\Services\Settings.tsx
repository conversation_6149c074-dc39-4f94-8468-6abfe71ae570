import KatajereAPI from '../KatajereAPI';

export const orderServices = {
  addProfile: async (payload: FormData) => {
    const data = await KatajereAPI().post(`/business/profile`, payload);
    return data.data;
  },
  feedBack: async (payload: FormData) => {
    return await KatajereAPI().post('/feedback', payload);
  },
  editUserProfile: async (payload: FormData) => {
    return await KatajereAPI().post('/business/profile', payload);
  },
};
