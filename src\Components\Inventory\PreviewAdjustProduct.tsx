/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation,  } from "react-query";
import { inventoryServices } from "../../utils/Services/InventoryServices";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../store/auth";
// import { Notifications } from "../../utils/Services/Notification";


const PreviewAdjustProduct = ({
  onBack,
  productDetails,
  productId,
  itemID,
  oldStockData,
  tempData,
  refetch,
  onClose,
  productRefetch,
  stockRefetch,
}: any) => {
  const user = useUserAuthStore((state) => state.user);
  const mutation = useMutation((formData: FormData) =>
    inventoryServices.editProduct(formData)
  );

  // const { refetch: notificationRefetch } = useQuery(
  //   ["notifications", user.vendorId],
  //   () => Notifications.getNotification(user.vendorId),
  //   {
  //     enabled: !!user.vendorId,
  //     onSuccess: () => {},
  //     onError: (error: any) => {
  //       toast.error("Failed to fetch notifications");
  //       console.log("notification error:", error);
  //     },
  //   }
  // );
  const restockProduct = () => {


    const formData = new FormData();
    formData.append("productId", productId);
    formData.append("userEmail", user.userEmail);
    formData.append("vendorId", user.vendorId);
    formData.append("quantityChange", productDetails.quantityChange);
    formData.append("stockLevel", productDetails.newStockLevel);
    formData.append("itemID", itemID);
    formData.append("movementType", productDetails.movementType);
    formData.append(" movementReason", productDetails.movementReason);
    formData.append(" editAction", "PRODADJUST");

    mutation.mutate(formData, {
      onSuccess: (response) => {
        if (stockRefetch) {
          stockRefetch();
        }
        if (productRefetch) {
          productRefetch(); // Call only if productRefetch is defined
        }
        toast.success(response?.data?.message);
        refetch();
        // notificationRefetch()
        onClose();
      },
      onError: (error: any) => {
        toast.error(error?.response?.data?.error || error?.message);
      },
    });
  };

  
  return (
    <div className="overflow-auto">
      <div className="pl-3">
        {" "}
        <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
          Preview Adjust Stock Units
        </h2>
        <p className=" text-sm text-[#919191] font-sora ">
          Kindly review your entry and confirm
        </p>
      </div>
      <div className="p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6  mb-44">
        <h4 className="text-lg font-sora font-semibold text-[#2a2a2a]">
          Adjustment Details
        </h4>
        <div className="p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
          <p className="text-[12px] font-sora font-normal text-[#5b5b5b]">
            Product Name
          </p>
          <p className="text-sm font-normal text-[#5b5b5b] font-sora">
            {oldStockData.productName}{" "}
          </p>
        </div>
        <div className="p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
          <p className="text-[12px] font-sora font-normal text-[#5b5b5b]">
            SKU Number
          </p>
          <p className="text-sm font-normal text-[#5b5b5b] font-sora">
            {oldStockData.SKU || tempData.sku}{" "}
          </p>
        </div>
        <div className="p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
          <p className="text-[12px] font-sora font-normal text-[#5b5b5b]">
            Current Stock Level
          </p>
          <p className="text-sm font-normal text-[#5b5b5b] font-sora">
            {" "}
            {oldStockData.stockLevel || tempData.totalUnitsInStock} pcs
          </p>
        </div>

        <div className="p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
          <p className="text-[12px] font-sora font-normal text-[#5b5b5b]">
            Adjustment Type
          </p>
          <p className="text-sm font-normal text-[#5b5b5b] font-sora">
            {productDetails.movementType}
          </p>
        </div>
        <div className="p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
          <p className="text-[12px] font-sora font-normal text-[#5b5b5b]">
            Quantity to Adjust
          </p>
          <p className="text-sm font-normal text-[#5b5b5b] font-sora">
            {productDetails.quantityChange} pcs
          </p>
        </div>
        <div className="p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
          <p className="text-[12px] font-sora font-normal text-[#5b5b5b]">
            New Stock Level
          </p>
          <p className="text-sm font-normal text-[#5b5b5b] font-sora">
            {productDetails.newStockLevel} pcs
          </p>
        </div>
        <div className="flex flex-col gap-2">
          <p className="text-[12px] font-sora font-normal text-[#5b5b5b]">
            Reason for Adjustments
          </p>
          <p className="text-sm font-normal text-[#5b5b5b] font-sora">
            {productDetails.movementReason || "-"}
          </p>
        </div>
      </div>
      <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2  md:p-5">
        <div className="flex w-full px-7.5 gap-2.5">
          <button
            type="button"
            onClick={onBack}
            className="bg-transparent  text-[#2a2a2a] text-sm font-sora h-[49px] font-semibold border border-[#dcdcdc] px-12  rounded-2xl cursor-pointer flex-1/2"
          >
            Back
          </button>
          <button
            type="button"
            disabled={mutation.isLoading}
            onClick={restockProduct}
            className={`${
              mutation.isLoading ? "opacity-50 cursor-auto" : ""
            } w-full text-[#fcfcfc] bg-[#4f4cd8] text-sm h-[49px] font-sora font-semibold border border-[#dcdcdc] px-6  rounded-2xl cursor-pointer flex-2 `}
          >
            {mutation.isLoading ? "  Adjusting Product ..." : " Adjust Product"}
          </button>
        </div>
      </div>
    </div>
  );
};
export default PreviewAdjustProduct;
