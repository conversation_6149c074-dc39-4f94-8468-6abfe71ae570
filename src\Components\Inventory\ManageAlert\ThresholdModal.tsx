/* eslint-disable @typescript-eslint/no-explicit-any */
import { Warning2 } from "iconsax-react";
import InputField from "../../../Constants/InputField";
import { useState } from "react";
import { inventoryServices } from "../../../utils/Services/InventoryServices";
import { useMutation } from "react-query";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../../store/auth";


interface FrequencyModalProps {
  isOpen: boolean;
  closeModal: () => void;
  settings: any,
  refetch:any
}

interface threshold {
  lowStockThreshold: number;
  highStockThreshold: number;
  sensitivityLevel: number;
}

const ThresholdModal: React.FC<FrequencyModalProps> = ({
  isOpen,
  closeModal,
  settings,refetch
}) => {
  const alertMutate = useMutation(inventoryServices.alertSettings);

  const [threshold, setThreshold] = useState<threshold>({
    lowStockThreshold: settings?.lowStockThreshold || 0,
    highStockThreshold:settings?.highStockThreshold || 0,
    sensitivityLevel: settings?.sensitivityLevel || 0,
  });
  // Handling the change event
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setThreshold((prevState) => ({
      ...prevState,
      [name]: value, // Dynamically set the state property based on the 'name' attribute
    }));
  };
  const user = useUserAuthStore((state) => state.user);


  const handleSave = () => {
    const payload = {
      lowStockThreshold: threshold.lowStockThreshold,
      highStockThreshold: threshold.highStockThreshold,
      sensitivityLevel: threshold.sensitivityLevel,
      vendorId:user.vendorId
    };
    try {
      alertMutate.mutate(payload, {
        onSuccess: (response) => {
          refetch()
          closeModal()
          toast.success(response?.data?.message);
        },

        onError: (error: any) => {
          toast.error(error?.response?.data?.error || error?.message);
        },
      });
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <div className="fixed font-sora top-0 left-0 w-full h-full bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-screen md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="flex flex-col h-full">
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="flex-grow overflow-y-auto scrollbar-none hidescroll">
            <div className="pl-6 flex flex-col gap-6">
              <div>
                <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold">
                  Set Threshold
                </h2>
                <p className="text-sm text-[#919191] font-sora">
                  Manage threshold levels of your products.
                </p>
              </div>
              <div className="flex flex-col gap-2">
                <p className="text-[14px] font-medium text-[#5B5B5B] font-sora">
                  Low Stock Threshold
                </p>
                <InputField
                  name="lowStockThreshold"
                  placeholder="Enter a level"
                  value={threshold.lowStockThreshold}
                  onChange={handleInputChange}
                  type="number"
                />
                <p className="flex items-center text-[12px] text-[#5B5B5B] font-sora gap-2">
                  <Warning2 size={14} />
                  Set a level that triggers a low stock alert for each product
                  or category.
                </p>
              </div>
              <div className="flex flex-col gap-2">
                <p className="text-[14px] font-medium text-[#5B5B5B] font-sora">
                  High Stock Threshold
                </p>
                <InputField
                  name="highStockThreshold"
                  placeholder="Enter a level"
                  value={threshold.highStockThreshold}
                  onChange={handleInputChange}
                  type="number"
                />
                <p className="flex items-center text-[12px] text-[#5B5B5B] font-sora gap-2">
                  <Warning2 size={14} />
                  Define the maximum stock level that triggers a high stock
                  alert.
                </p>
              </div>
              <div className="flex flex-col gap-2">
                <p className="text-[14px] font-medium text-[#5B5B5B] font-sora">
                  Stock Movement Sensitivity (%)
                </p>
                <InputField
                  name="sensitivityLevel"
                  placeholder="Enter a level"
                  value={threshold.sensitivityLevel}
                  onChange={handleInputChange}
                  type="number"
                />
                <p className="flex items-center text-[12px] text-[#5B5B5B] font-sora gap-2">
                  <Warning2 size={14} />
                  e.g., notify if more than 20% of stock is moved.
                </p>
              </div>
            </div>
          </div>
          <div className="p-6 mt-auto">
            <button
              onClick={handleSave}
              disabled={alertMutate.isLoading}
              className={`${
                alertMutate.isLoading ? "opacity-50 cursor-auto" : ""
              } w-full text-[#fcfcfc] bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl cursor-pointer flex-2 `}
            >
              {alertMutate.isLoading ? "Saving Changes" : "Save Changes"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThresholdModal;
