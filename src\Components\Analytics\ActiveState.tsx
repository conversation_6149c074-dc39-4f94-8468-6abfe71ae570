/* eslint-disable @typescript-eslint/no-explicit-any */

import CustomersInsight from './CustomersInsight';
import FinancialHealth from './FinancialHealth';
import InventoryData from './InventoryData';
import Orderdata from './Orderdata';
import SalesPerformance from './SalesPerformance';
import Tabs from './Tabs';

const ActiveState = ({
  data,
  isLoading,
  isError,
  formattedStartDate,
  formattedEndDate,
}: any) => {
  const tabs = [
    {
      title: 'Sales Performance',
      content: (
        <SalesPerformance
          data={data}
          isLoading={isLoading}
          isError={isError}
          formattedEndDate={formattedEndDate}
          formattedStartDate={formattedStartDate}
        />
      ),
    },
    {
      title: 'Inventory Data',
      content: (
        <InventoryData
          data={data}
          isLoading={isLoading}
          isError={isError}
          formattedEndDate={formattedEndDate}
          formattedStartDate={formattedStartDate}
        />
      ),
    },
    {
      title: 'Customer Insights',
      content: (
        <CustomersInsight
          data={data}
          isLoading={isLoading}
          isError={isError}
          formattedEndDate={formattedEndDate}
          formattedStartDate={formattedStartDate}
        />
      ),
    },
    {
      title: 'Order Data',
      content: (
        <Orderdata
          data={data}
          isLoading={isLoading}
          isError={isError}
          formattedEndDate={formattedEndDate}
          formattedStartDate={formattedStartDate}
        />
      ),
    },
    {
      title: 'Financial Health',
      content: (
        <FinancialHealth
          data={data}
          isLoading={isLoading}
          isError={isError}
          formattedEndDate={formattedEndDate}
          formattedStartDate={formattedStartDate}
        />
      ),
    },
  ];

  return (
    <>
      <div className="mt-6">
        <Tabs tabs={tabs} />
      </div>
    </>
  );
};

export default ActiveState;
