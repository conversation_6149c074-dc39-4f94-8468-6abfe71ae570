/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
// import { IoMdArrowBack } from "react-icons/io";
import { FiUploadCloud } from 'react-icons/fi';
import { useMutation } from 'react-query';
import { orderServices } from '../../utils/Services/Order';
import { toast } from 'react-toastify';
import { useUserAuthStore } from '../../store/auth';

interface DeclinePaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  closePaymentModal: () => void;
  order: any;
  refetch: any;
  refetchTable: any;
}

const DeclinePaymentModal: React.FC<DeclinePaymentModalProps> = ({
  isOpen,
  onClose,
  order,
  closePaymentModal,
  refetch,
  refetchTable
}) => {
  const [reason, setReason] = useState<string>('');
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const mutation = useMutation((data: any) => orderServices.editOrder(data));
  const user = useUserAuthStore((state) => state.user);
  const business = useUserAuthStore((state) => state.business);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      setPreview(URL.createObjectURL(selectedFile));
    }
  };

  const handleSend = () => {
    const formData = new FormData();

    formData.append('vendorId', user.vendorId);
    formData.append('orderId', order?.orderId || '');
    formData.append('vendorEmail', user.userEmail);
    if (file) {
      formData.append('declineFile', file);
    }
    formData.append('declineReason', reason || '');
    formData.append('paymentStatus', 'Declined');
    formData.append('sendToCustomer', 'Yes');
    formData.append('customerName', order?.customerName || '');
    formData.append('customerEmail', order?.customerEmail || '');
    formData.append('subDomain', business.business_name);
    formData.append('editEntireOrder', 'true');

    mutation.mutate(formData, {
      onSuccess: async () => {
        toast.success('Declined Succesfully');
        refetch();
        refetchTable();
        onClose();
        closePaymentModal();
      },
      onError: (error: any) => {
        if (error?.response?.status === 429) {
          toast.error(error?.response?.data?.message);
        } else {
          toast.error(
            error?.response ? error?.response?.data?.error : error?.message
          );
        }
      },
    });
  };

  return (
    <>
      <div
        className={`fixed top-0 left-0 w-full h-full bg-[rgba(0,0,0,0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
        <div className="fixed top-0 right-0 md:w-[580px] w-full pb-24 bg-white h-full overflow-y-auto z-30 p-5">
          <div className="flex justify-end items-center">
            <span
              onClick={onClose}
              className="text-3xl font-bold cursor-pointer">
              &times;
            </span>
          </div>

          <h2 className="mt-5 text-xl text-[#181818] font-semibold">
            Decline Proof of Payment
          </h2>
          <p className="text-sm text-[#5B5B5B] mt-2">
            Send a return message to the customer.
          </p>

          <div className="mt-5 bg-[#F5F5F5] p-5  rounded-2xl">
            <div className="mb-4">
              <label
                htmlFor="reason"
                className="block text-[14px] font-normal mb-2 text-[#5B5B5B]">
                Reason for declining payment
              </label>
              <textarea
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Tell your customer why you declined this payment e.g., receipt is not clear or wrong amount"
                className="mt-1 w-full h-[188px] p-3 border rounded-xl text-sm text-gray-700"
              />
            </div>

            {/* File Upload */}
            <div className="mb-4">
              <label className="block text-[14px] font-normal text-[#5B5B5B] mb-4">
                Additional Note/Attachment
              </label>
              <div
                className="flex flex-col items-center justify-center w-full h-28 rounded-2xl bg-gray-50 border-2 border-dashed cursor-pointer"
                onClick={() => document.getElementById('file-upload')?.click()}>
                <input
                  id="file-upload"
                  type="file"
                  accept="image/png, image/jpeg, application/pdf"
                  onChange={handleFileChange}
                  className="hidden"
                />
                {preview ? (
                  <img
                    src={preview}
                    alt="Selected file preview"
                    className="w-full h-full object-contain rounded-xl"
                  />
                ) : (
                  <div className="flex flex-col items-center">
                    <FiUploadCloud size={32} className="text-[#4F4CD8] mb-2" />
                    <p className="text-sm font-medium text-gray-600">
                      Click here to upload receipt
                    </p>
                    <p className="text-xs px-2 text-[#7E8494] text-center">
                      Kindly upload a screenshot or receipt of your payment
                      (PNG, JPG, or PDF)
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex mt-10 flex-col md:flex-row md:gap-4 gap-2">
              <button
                onClick={onClose}
                className="w-full py-3 text-[#2A2A2A] border font-semibold rounded-2xl md:order-1 order-2">
                No, Don’t Send
              </button>
              <button
                onClick={handleSend}
                disabled={mutation.isLoading || !reason}
                className={`w-full py-3 bg-[#DC3545] text-white font-semibold rounded-2xl md:order-2 order-1 ${
                  mutation.isLoading || !reason
                    ? ' opacity-50 cursor-not-allowed '
                    : ' '
                }`}>
                {mutation.isLoading ? 'Sending . . .' : ' Send To Customer'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DeclinePaymentModal;
