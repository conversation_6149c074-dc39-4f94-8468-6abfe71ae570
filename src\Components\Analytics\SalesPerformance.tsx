/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ArrowDown,
  ArrowUp,
  DollarCircle,
  MoneyRecive,
  ReceiptDisscount,
  Refresh,
} from 'iconsax-react';
import ProductChart from './ProductChart';
import DateHead from './DateHead';
import { useEffect } from 'react';
import SalesByChannel<PERSON>ieChart from './SalesByChannelPieChart';

const SalesPerformance = ({
  data,
  isLoading,
  isError,
  formattedStartDate,
  formattedEndDate,
}: any) => {
  useEffect(() => {
    document.title = 'Sales Performance - Analytics';
  });
  return (
    <div className="font-sora ">
      <DateHead />
      {isError ? (
        <h2 className="flex justify-center items-center py-16">
          An Error Occurred, Please Refresh
        </h2>
      ) : (
        <div className="flex flex-col lg:flex-row gap-6">
          <div className="w-full md:max-w-[448px]">
            <div className="border rounded-xl py-4 px-6  ">
              <div className="flex gap-4 items-center">
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                ) : (
                  <div className="bg-[#E8F2FB] p-1.5 rounded-full">
                    <DollarCircle size="16" color="#1976D2" variant="Bold" />
                  </div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                ) : (
                  <p className="font-semibold text-lg text-[#2A2A2A]">
                    Total Sales Volume{' '}
                  </p>
                )}
              </div>
              <div className="flex my-6 justify-between">
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                ) : (
                  <p className="font-semibold text-[#1E1B39] text-2xl">
                    ₦ {data?.totalSalesVolume?.toLocaleString()}
                  </p>
                )}
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 rounded-3xl w-[40px] "></div>
                ) : (
                  <p
                    className={`flex items-center rounded-3xl  text-[10px] p-1 ${
                      data?.totalSalesChange < 0
                        ? 'border-[#DC3545] border'
                        : 'border-[#AEEBBC] border'
                    } `}>
                    <span
                      className={`${
                        data?.totalSalesChange < 0
                          ? 'text-[#DC3545]'
                          : 'text-[#28A745]'
                      }`}>
                      {data?.totalSalesChange} %
                    </span>
                    {data?.totalSalesChange < 0 ? (
                      <ArrowDown className="text-[#DC3545]" size="12" />
                    ) : (
                      <ArrowUp className="text-[#28A745]" size="12" />
                    )}
                  </p>
                )}
              </div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] max-w-[320px]"></div>
              ) : (
                <p className="text-[#9B9B9B] text-sm font-normal">
                  {/* Your sales volume grew between July 1, 2024 to August 1, 2024{' '} */}
                  {data?.salesInsight}
                </p>
              )}
            </div>
            <div className="border rounded-xl py-4 px-6 my-6">
              <div className="flex gap-4 items-center">
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                ) : (
                  <div className="bg-[#E6F7FA] p-1.5 rounded-full">
                    <ReceiptDisscount
                      size="16"
                      color="#30B0C7"
                      variant="Bold"
                    />
                  </div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                ) : (
                  <p className="font-semibold text-lg text-[#2A2A2A]">
                    Revenue from Discounts/Offers{' '}
                  </p>
                )}
              </div>
              <div className="flex my-6 justify-between">
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                ) : (
                  <p className="font-semibold text-[#1E1B39] text-2xl">
                    ₦ {data?.revenueDiscount?.toLocaleString()}
                  </p>
                )}
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 rounded-3xl w-[40px] "></div>
                ) : (
                  <p
                    className={`flex items-center rounded-3xl  text-[10px] p-1 ${
                      data?.revenueDiscountChange < 0
                        ? 'border-[#DC3545] border'
                        : 'border-[#AEEBBC] border'
                    } `}>
                    <span
                      className={`${
                        data?.revenueDiscountChange < 0
                          ? 'text-[#DC3545]'
                          : 'text-[#28A745]'
                      }`}>
                      {data?.revenueDiscountChange} %
                    </span>
                    {data?.revenueDiscountChange < 0 ? (
                      <ArrowDown className="text-[#DC3545]" size="12" />
                    ) : (
                      <ArrowUp className="text-[#28A745]" size="12" />
                    )}
                  </p>
                )}
              </div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] max-w-[320px]"></div>
              ) : (
                <p className="text-[#9B9B9B] text-sm font-normal">
                  {data?.revenueInsight}
                </p>
              )}
            </div>
            <div className="border rounded-xl py-4 px-6 ">
              <div className="flex gap-4 items-center">
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                ) : (
                  <div className="bg-[#FAE6EA] p-1.5 rounded-full">
                    <Refresh size="16" color="#FF2D55" variant="Bold" />
                  </div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                ) : (
                  <p className="font-semibold text-lg text-[#2A2A2A]">
                    Refunded Orders
                  </p>
                )}
              </div>
              <div className="flex mt-6 justify-between items-center">
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                ) : (
                  <p className="font-semibold text-[#1E1B39] text-2xl">
                    {data?.refundedOrders?.toLocaleString()} orders
                  </p>
                )}
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 rounded-3xl w-[40px]  "></div>
                ) : (
                  <p className="flex items-center rounded-3xl text-[#DC3545] text-[10px] p-1 border border-[#F3BCC1]">
                    <span> {data?.refundOrdersChange} %</span>{' '}
                    <ArrowUp size="12" />
                  </p>
                )}
              </div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px] my-3"></div>
              ) : (
                <p className=" mt-[20px] text-lg text-[#6B6B6B]">
                  Total Amount
                  <span className="font-semibold text-[#1E1B39]">
                    {' '}
                    ₦ {data?.refundSalesVolume?.toLocaleString()}
                  </span>
                </p>
              )}
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] max-w-[320px]"></div>
              ) : (
                <p className="mt-[10px] text-[#9B9B9B] text-sm font-normal">
                  {data?.refundOrderInsight}
                </p>
              )}
            </div>
            <div className="border rounded-xl py-4 px-6 mt-6">
              <div className="flex gap-4 items-center">
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 p-3 rounded-full"></div>
                ) : (
                  <div className="bg-[#E8F2FB] p-1.5 rounded-full">
                    <MoneyRecive size="16" color="#1976D2" variant="Bold" />
                  </div>
                )}
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[200px]"></div>
                ) : (
                  <p className="font-semibold text-lg text-[#2A2A2A]">
                    Sales By Channel{' '}
                  </p>
                )}
              </div>
              <div className="flex my-6 justify-between">
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[100px]"></div>
                ) : (
                  <p className="font-semibold text-[#1E1B39] text-2xl">
                    ₦ {data?.totalSalesVolume?.toLocaleString()}
                  </p>
                )}
                {isLoading ? (
                  <div className="animate-pulse bg-slate-200 rounded-3xl w-[40px] "></div>
                ) : (
                  <p
                    className={`flex items-center rounded-3xl  text-[10px] p-1 ${
                      data?.totalSalesChange < 0
                        ? 'border-[#DC3545] border'
                        : 'border-[#AEEBBC] border'
                    } `}>
                    <span
                      className={`${
                        data?.totalSalesChange < 0
                          ? 'text-[#DC3545]'
                          : 'text-[#28A745]'
                      }`}>
                      {data?.totalSalesChange} %
                    </span>
                    {data?.totalSalesChange < 0 ? (
                      <ArrowDown className="text-[#DC3545]" size="12" />
                    ) : (
                      <ArrowUp className="text-[#28A745]" size="12" />
                    )}
                  </p>
                )}
              </div>
              {isLoading ? (
                <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] max-w-[320px]"></div>
              ) : (
                <p className="text-[#8B8B8B] text-[14px] leading-[22.4px] font-normal">
                  {data?.salesInsight}
                </p>
              )}
              <div className="flex justify-center w-full text-[12px]">
                <SalesByChannelPieChart
                  data={data?.salesByChannel}
                  isLoading={isLoading}
                />
              </div>
            </div>
          </div>
          <ProductChart
            data={data}
            isLoading={isLoading}
            formattedEndDate={formattedEndDate}
            formattedStartDate={formattedStartDate}
          />
        </div>
      )}
    </div>
  );
};
export default SalesPerformance;
