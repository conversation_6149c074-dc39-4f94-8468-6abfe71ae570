/* eslint-disable @typescript-eslint/no-explicit-any */

import { useState, useEffect } from "react";
import InputField from "../../Constants/InputField";
import {
  AddCircle,
  MinusCirlce,
  PercentageSquare,
  RefreshRightSquare,
} from "iconsax-react";
import Dropdown from "../../Constants/DropDown";
// import aiStar from "../../assets/aiStar.svg";
import DateInputPicker from "../../Constants/DateInputPicker";
import DateInputPickerPromo from "../../Constants/DateInputPickerPromo";
import { useMutation, useQuery } from "react-query";
import { orderServices } from "./../../utils/Services/Order";
import { useUserAuthStore } from "../../store/auth";
import { toast } from "react-toastify";
import { Notifications } from "../../utils/Services/Notification";

interface PromoDetailsProps {
  isOpen: boolean;
  closeModal: () => void;
  pomo: any;
  customers: any;
  products: any;
  refetchTable: any;
}
interface type {
  name: string;
}

interface PromoDetails {
  vendorId: string;
  promoCode: string;
  promoType: string;
  promoName: string;
  valueType: string;
  value: string;
  description: string;
  eligibleCustomers: string[];
  eligibleProducts: string[];
  eligibleCustomersType: string;
  eligibleProductsType: string;
  usageLimitType: string;
  usageLimit: number;
}

const EditPromo: React.FC<PromoDetailsProps> = ({
  isOpen,
  closeModal,
  pomo,
  customers,
  products,
  refetchTable,
}) => {
  console.log(pomo);
  const user = useUserAuthStore((state) => state.user);
  const PromoType: type[] = [{ name: "Discount" }, { name: "Coupon" }];
  const [activeTab, setActiveTab] = useState<string>("Discount");
  const [limit, setLimit] = useState<string>("Unlimited");
  const [couponType, setCouponType] = useState<string>(
    "Add Coupon to All Cart"
  );
  const [reportStartDate, setReportStartDate] = useState(new Date());
  const [reportEndDate, setReportEndDate] = useState(new Date());
  const tabs = ["Unlimited", "Limited", "Per Customers"];
  const coupon = ["Add Coupon to All Cart", "Add Coupon Per Product"];
  const eligibleCustomerIds =
    pomo?.eligibleCustomers?.map((customer: any) => customer.customerId) || [];
  const eligibleProductIds = pomo?.eligibleProducts?.map(
    (product: any) => product.productId
  );
  const [perCustomer, setPerCustomer] = useState<number>(0);

  const [promo, setPromo] = useState<PromoDetails>({
    vendorId: user.vendorId,
    promoCode: pomo?.promoCode || "",
    promoType: activeTab,
    promoName: pomo?.promoName,
    valueType: "",
    value: pomo?.promoValue,
    description: pomo?.promoDescription,
    eligibleCustomers: eligibleCustomerIds,
    eligibleProducts: eligibleProductIds,
    eligibleCustomersType: "",
    eligibleProductsType: "",
    usageLimitType: pomo?.usageLimitType,
    usageLimit: pomo?.usageLimit,
  });

  const editPromo = useMutation(orderServices.editPromo);
  const handleTab = (type: any) => {
    setPromo((prevState) => ({
      ...prevState,
      promoType: type,
    }));
    setActiveTab(type);
    console.log(activeTab);
  };
  const handleLimitTab = (tab: string) => {
    setLimit(tab);
  };

  const handleCoupon = (tab: string) => {
    setCouponType(tab);
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setPromo((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    console.log(value);
  };

  const handleSelect = (value: string, name: string) => {
    setPromo((prevState) => {
      if (name === "eligibleCustomers") {
        const updatedCustomers = Array.isArray(prevState.eligibleCustomers)
          ? prevState.eligibleCustomers.includes(value)
            ? prevState.eligibleCustomers
            : [...prevState.eligibleCustomers, value]
          : [value];

        return {
          ...prevState,
          eligibleCustomers: updatedCustomers,
          eligibleCustomersType:
            updatedCustomers.length > 0 ? "Specific" : "All",
        };
      }

      if (name === "eligibleProducts") {
        const updatedProducts = Array.isArray(prevState.eligibleProducts)
          ? prevState.eligibleProducts.includes(value)
            ? prevState.eligibleProducts
            : [...prevState.eligibleProducts, value]
          : [value];

        return {
          ...prevState,
          eligibleProducts: updatedProducts,
          eligibleProductsType: updatedProducts.length > 0 ? "Specific" : "All",
        };
      }

      return {
        ...prevState,
        [name]: value,
      };
    });
  };

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    // Initialize eligibleCustomers when pomo changes
    setPromo((prevState) => ({
      ...prevState,
      eligibleCustomers:
        pomo?.eligibleCustomers?.map((customer: any) => customer.customerId) ||
        [],
    }));

    return () => {
      document.body.classList.remove("no-scroll");
    };
  }, [isOpen, pomo.eligibleCustomers]);
  console.log("pcurrent:", promo.eligibleCustomers);
  console.log("passed:", customers);

  const customerInfo = customers;

  const { refetch: notificationRefetch } = useQuery(
    ["notifications", user.vendorId],
    () => Notifications.getNotification(user.vendorId),
    {
      enabled: !!user.vendorId,
      onSuccess: () => {},
      onError: (error: any) => {
        console.log("notification error:", error);
      },
    }
  );

  const handleSubmit = async () => {
    const payload = {
      vendorId: promo.vendorId,
      promoCode: promo.promoCode,
      promoName: promo.promoName,
      promoType: promo.promoType,
      description: promo.description,
      valueType: promo.valueType,
      value: promo.value,
      startDate: reportStartDate.toISOString().split("T")[0],
      endDate: reportEndDate.toISOString().split("T")[0],
    eligibleCustomersType: promo.eligibleCustomers.length > 0  ? 'Specific' : 'All',
      eligibleCustomers: promo.eligibleCustomers,
      eligibleProductsType: promo.eligibleProducts.length > 0 ? 'Specific' :  couponType === 'Add Coupon to All Cart' ? 'All' : 'Specific',
      usageLimitType: promo.usageLimitType,
      usageLimit: promo.usageLimit,
      eligibleProducts: promo.eligibleProducts,
    };
    console.log(payload);
    try {
      editPromo.mutate(payload, {
        onSuccess: (response) => {
          refetchTable();
          notificationRefetch();
          toast.success(response?.message);
          closeModal();
        },

        onError: (error: any) => {
          toast.error(error?.response?.data?.error || error?.message);
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleSelectCustomer = (customerName: string) => {
    if (customerName === "All Customers") {
      setPromo((prevState) => ({
        ...prevState,
        eligibleCustomersType: "All",
        eligibleCustomers: [],
      }));
    } else {
      const selectedCustomer = customers.find(
        (cust: any) => cust.customerName === customerName
      );
      if (selectedCustomer) {
        if (
          promo.eligibleCustomersType === "Specific" &&
          promo.eligibleCustomers.includes(selectedCustomer.customerId)
        ) {
          toast.error("Customer already selected!");
          return;
        }
        setPromo((prevState) => ({
          ...prevState,
          eligibleCustomersType: "Specific",
          eligibleCustomers: [
            ...prevState.eligibleCustomers,
            selectedCustomer.customerId,
          ],
        }));
      }
    }
  };

  const handleSelectProduct = (productName: string) => {
    if (productName === "All Products") {
      setPromo((prevState) => ({
        ...prevState,
        eligibleProductsType: "All",
        eligibleProducts: [],
      }));
    } else {
      const selectedProduct = products.find(
        (prod: any) => prod.productName === productName
      );
      if (selectedProduct) {
        if (
          promo.eligibleProductsType === "Specific" &&
          promo.eligibleProducts.includes(selectedProduct.productId)
        ) {
          toast.error("Customer already selected!");
          return;
        }
        setPromo((prevState) => ({
          ...prevState,
          eligibleProductsType: "Specific",
          eligibleProducts: [
            ...prevState.eligibleProducts,
            selectedProduct.productId,
          ],
        }));
      }
    }
  };

  const handleRemoveCustomer = (idToRemove: string) => {
    setPromo((prevState) => {
      const updatedCustomers = prevState.eligibleCustomers.filter(
        (id) => id !== idToRemove
      );
      return {
        ...prevState,
        eligibleCustomers: updatedCustomers,
        eligibleCustomersType: updatedCustomers.length > 0 ? "Specific" : "All",
      };
    });
  };
  const handleRemoveProduct = (idToRemove: string) => {
    setPromo((prevState) => {
      const updatedProducts = prevState.eligibleProducts.filter(
        (id) => id !== idToRemove
      );
      return {
        ...prevState,
        eligibleProducts: updatedProducts,
        eligibleProductsType: updatedProducts.length > 0 ? "Specific" : "All",
      };
    });
  };

  return (
    <div className="fixed inset-0 z-50 font-sora bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full md:w-[580px] bg-white z-30 p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeModal}
            className="text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]"
          >
            &times;
          </span>

          {/* Ensure this div takes full height minus the close button */}
          <div className="h-[calc(100vh-80px)] md:pl-6 pl-0 flex flex-col gap-4 overflow-y-auto scrollbar-none hidescroll">
            <div className="mb-[15px]">
              <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold">
                Edit Promo/Sales
              </h2>
              <p className="text-sm text-[#919191] font-sora">
                Create irresistible offers to get more customers and increased
                sales
              </p>
            </div>
            <div className="flex flex-col gap-2 w-full">
              <p className="text-[14px] text-primary-neutralt2 font-sora">
                Type
              </p>
              <div className="w-full p-1 border-[1px] border-primary-neutral300 rounded-3xl">
                <div className="flex justify-between gap-3 items-center">
                  {PromoType.map((type) => (
                    <div className="w-full" key={type.name}>
                      {" "}
                      {/* Add unique key prop */}
                      <button
                        onClick={() => handleTab(type.name)}
                        className={`p-2 rounded-3xl flex-1 w-full font-sora text-center ${
                          activeTab === type.name
                            ? type.name === "Discount"
                              ? "bg-[#E6E5F9] text-primary-purple"
                              : " bg-[#FFF2DF] text-[#FF9900]"
                            : "text-primary-neutralt1"
                        }`}
                      >
                        {type.name}
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-2 w-full">
              <p className="text-[14px] text-primary-neutralt2 font-sora">
                Name
              </p>
              <InputField
                id="promo-name"
                placeholder="Enter Promo/Sales name"
                value={promo.promoName}
                onChange={handleChange}
                name="promoName"
              />
            </div>
            <div className="flex flex-col gap-2 w-full">
              <p className="text-[14px] text-primary-neutralt2 font-sora">
                Code
              </p>
              <div className="relative">
                <InputField
                  id="promo-name"
                  value={promo.promoCode}
                  placeholder="Enter preferred Promo/Sales code"
                  onChange={handleChange}
                  name="promoCode"
                />
                <RefreshRightSquare
                  variant="Bold"
                  className="absolute top-6 right-5 text-primary-purple"
                />
              </div>
            </div>
            <div className="flex justify-between items-center gap-2 w-full">
              <div className="flex w-full flex-col gap-2">
                <p className="text-[14px] text-primary-neutralt2 font-sora">
                  Value Type
                </p>
                <Dropdown
                  // onSelect={handleSelect}
                  onSelect={(value) => handleSelect(value, "valueType")}
                  options={["Percentage", ""]}
                  value={promo.valueType}
                />
              </div>
              <div className="flex flex-col w-full">
                <p className="text-[14px] text-primary-neutralt2 font-sora">
                  Value Percentage
                </p>
                <div className="relative">
                  <InputField
                    id="promo-name"
                    placeholder="Enter a percentage"
                    onChange={handleChange}
                    value={promo.value}
                    name="value"
                  />
                  <PercentageSquare
                    variant="Bold"
                    className="absolute top-6 right-5 text-primary-purple"
                  />
                </div>
              </div>
            </div>
            <div className="p-0 flex flex-col gap-2 relative">
              <p className="text-sm font-sans font-normal text-[#5b5b5b]">
                Description (Optional)
              </p>
              <textarea
                rows={12}
                className=" text-[#7b7b7b] text-xs p-4 border border-[#cccccc] rounded-2xl outline-none"
                placeholder="Tell your customer why you are running this promo "
                id="promoDescription"
                onChange={handleChange}
                value={promo.description}
                name="description"
              ></textarea>
              {/* <div className="absolute bottom-2 right-2 mt-5">
                <button className="flex items-center bg-[#e6e5f9] p-2.5 gap-2.5 text-[#4f4cd8] rounded-2xl font-semibold cursor-pointer border border-[#4f4cd8]">
                  <img src={aiStar} alt="ai" />
                  <span>Write with AI </span>
                </button>
              </div> */}
            </div>
            <div className="flex w-full flex-col md:flex-row  justify-between items-center gap-2">
              <div className="p-0 flex w-full flex-col gap-2.5 relative last:border-b-0 ">
                <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                  Valid From
                </p>{" "}
                <DateInputPicker
                  selectedDate={reportStartDate}
                  onDateChange={setReportStartDate}
                />
              </div>
              <div className="p-0 w-full flex flex-col gap-2.5 relative last:border-b-0 ">
                <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                  Valid Till
                </p>
                <DateInputPickerPromo
                  selectedDate={reportEndDate}
                  onDateChange={setReportEndDate}
                />
              </div>
            </div>
            {activeTab === "Discount" && (
              <div className="flex flex-col gap-6">
                <div className="flex flex-col gap-2 w-full">
                  <p className="text-[14px] text-primary-neutralt2 font-sora">
                    Eligible Customers
                  </p>
                  <Dropdown
                    onSelect={(value) => handleSelectCustomer(value)}
                    options={[
                      ...customers.map(
                        (customer: any) => customer.customerName
                      ),
                      "All Customers",
                    ]}
                    placeholder="Select Eligible Customers"
                    value={promo.eligibleCustomersType}
                  />

                  {promo?.eligibleCustomers?.length > 0 && (
                    <ul className="mt-4 space-y-2">
                      {promo.eligibleCustomers.map((customerId, index) => {
                        const customerData = customerInfo.find(
                          (cust: any) =>
                            cust.customerId.toString() === customerId.toString()
                        );
                        console.log({ customerId, customerData, customerInfo });

                        return (
                          <li
                            key={index}
                            className="flex justify-between items-center bg-gray-100 px-4 py-2 rounded-lg shadow-sm"
                          >
                            <span className="text-gray-700">
                              {customerData?.customerName || "Unknown"}
                            </span>
                            <button
                              onClick={() => handleRemoveCustomer(customerId)}
                              className="text-red-500 hover:text-red-700 font-bold"
                            >
                              X
                            </button>
                          </li>
                        );
                      })}
                    </ul>
                  )}
                </div>
                <div className="flex flex-col gap-2 w-full">
                  <p className="text-[14px] text-primary-neutralt2 font-sora">
                    Eligible Products
                  </p>
                  <Dropdown
                    onSelect={(value) => handleSelectProduct(value)}
                    options={[
                      ...products.map((product: any) => product.productName),
                      "All Products",
                    ]}
                    placeholder="Select Eligible Products"
                    value={""}
                  />
                  {promo?.eligibleProducts.length > 0 && (
                    <ul className="mt-4 space-y-2">
                      {promo.eligibleProducts.map((productId, index) => {
                        const productData = products.find(
                          (cust: any) => cust.productId === productId
                        );

                        return (
                          <li
                            key={index}
                            className="flex justify-between items-center bg-gray-100 px-4 py-2 rounded-lg shadow-sm"
                          >
                            <span className="text-gray-700">
                              {productData?.productName || "Unknown"}
                            </span>
                            <button
                              onClick={() => handleRemoveProduct(productId)}
                              className="text-red-500 hover:text-red-700 font-bold"
                            >
                              X
                            </button>
                          </li>
                        );
                      })}
                    </ul>
                  )}
                </div>
              </div>
            )}

            {activeTab === "Coupon" && (
              <div className="flex flex-col gap-6">
                <div className="flex flex-col gap-2">
                  <p className="text-[14px] text-primary-neutralt2 font-sora">
                    Type
                  </p>
                  <div className="w-full p-1 border-[1px] border-primary-neutral300 rounded-3xl">
                    <div className="flex justify-between gap-3 items-center">
                      {coupon.map((type) => (
                        <div className="w-full" key={type}>
                          {" "}
                          {/* Add unique key prop */}
                          <button
                            onClick={() => handleCoupon(type)}
                            className={`p-2 rounded-3xl flex-1 w-full font-sora text-center ${
                              couponType === type
                                ? type === "Add Coupon to All Cart"
                                  ? "bg-[#EAF7ED] text-[#28A745]"
                                  : " bg-[#FAF2F2] text-[#DC3545]"
                                : "text-primary-neutralt1"
                            }`}
                          >
                            {type}
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                {couponType === "Add Coupon to All Cart" ? (
                  <div>
                    <div className="flex flex-col gap-2 w-full">
                      <p className="text-[14px] text-primary-neutralt2 font-sora">
                        Minimum Amount for Cart (Optional)
                      </p>
                      <InputField
                        id="minimum-value"
                        placeholder="Specify minimum amount for coupon to be valid"
                      />
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="flex flex-col gap-2 w-full">
                      <p className="text-[14px] text-primary-neutralt2 font-sora">
                        Eligible Products
                      </p>
                      <Dropdown
                        onSelect={(value) =>
                          handleSelect(value, "eligibleProductsType")
                        }
                        options={["mr man", "badboy"]}
                        placeholder="All Customers"
                        value={promo.eligibleProductsType}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="flex flex-col gap-2 w-full">
              <p className="text-[14px] text-primary-neutralt2 font-sora">
                Usage Limit
              </p>
              <div className="w-full flex gap-2 p-1 border rounded-3xl justify-between">
                {tabs.map((tab) => (
                  <button
                    key={tab}
                    className={`flex-1 md:px-4 px-1 py-2 text-center md:text-[14px] text-[12px] rounded-full transition ${
                      limit === tab
                        ? tab === "Unlimited"
                          ? "bg-[#FAF2F2] text-[#DC3545] font-semibold"
                          : tab === "Limited"
                          ? "bg-[#FFF2DF] text-[#FF9900] font-semibold"
                          : "bg-[#EAF7ED] text-[#28A745] font-semibold"
                        : "text-primary-neutralt2 bg-primary-baseWhite"
                    }`}
                    onClick={() => handleLimitTab(tab)}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>
            {limit === "Limited" && (
              <div className="flex flex-col gap-2 w-full">
                <p className="text-[14px] text-primary-neutralt2 font-sora">
                  Set Limits
                </p>
                <InputField
                  id="limitedPromo "
                  onChange={handleChange}
                  placeholder="Enter maximum number of limits"
                  value={pomo.usageLimit}
                  name="usageLimitType"
                />
              </div>
            )}
            {limit === "Per Customers" && (
              <div className="flex flex-col gap-2 w-full">
                <p className="text-[14px] text-primary-neutralt2 font-sora">
                  Set Limit Per Customer
                </p>
                <div className="flex w-full  justify-between p-2 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                  <MinusCirlce
                    onClick={() =>
                      setPerCustomer((prev: number) =>
                        prev > 0 ? prev - 1 : prev
                      )
                    }
                    className={`cursor-pointer ${
                      perCustomer > 0
                        ? "text-primary-neutralt1"
                        : "text-primary-neutral300"
                    }`}
                  />
                  <p className="text-[14px] font-sora text-primary-neutralt1">
                    {perCustomer}
                  </p>
                  <AddCircle
                    onClick={() => setPerCustomer((prev: any) => prev + 1)}
                    className="cursor-pointer text-primary-neutralt1"
                  />
                </div>
              </div>
            )}

            <div className="mb-[100px]"></div>
          </div>

          {/* Bottom Buttons */}
          <div className="fixed bottom-0  left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2 md:p-10">
            <div className="flex w-full gap-2.5">
              <button
                onClick={closeModal}
                className="bg-transparent text-[#2a2a2a] text-sm font-semibold border px-12 py-4 rounded-2xl"
              >
                Back
              </button>
              <button
                disabled={editPromo.isLoading}
                onClick={handleSubmit}
                className={`${
                  editPromo.isLoading ? "opacity-50 cursor-auto" : ""
                } w-full text-[#fcfcfc] bg-[#4f4cd8] text-sm h-[49px] font-sora font-semibold border border-[#dcdcdc] px-6  rounded-2xl cursor-pointer flex-2 `}
              >
                {editPromo.isLoading ? "Submitting" : "Submit"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditPromo;
