/* eslint-disable @typescript-eslint/no-explicit-any */
import { Icon } from 'iconsax-react';

interface ActionButton {
  title?: string;
  image?: Icon;
  onClick?: any;
}

const AnalyticButton: React.FC<ActionButton> = ({
  title,
  image: Icons,
  onClick,
}) => {
  return (
    <button
      onClick={onClick}
      type="button"
      className="flex items-center rounded-2xl mt-6 px-4 py-3 border gap-8 text-sm font-semibold text-[#2A2A2A]">
      <span> {title}</span>
      {Icons && <Icons size={16} />}
    </button>
  );
};
export default AnalyticButton;
