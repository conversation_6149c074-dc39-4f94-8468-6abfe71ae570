/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useRef, useEffect} from "react";
import Logo from "../assets/Logo.svg";
import NumericInput from "../Constants/NumericInput";
import { Messages3 } from "iconsax-react";
// import <PERSON><PERSON>omb from "./HoneyComb";
import WelcomeVerify from "./WelcomeVerify";
import { useLocation, useNavigate } from "react-router-dom";
import { useMutation } from "react-query";
import { AuthServices } from "../utils/Services/AuthServices";
import { toast } from "react-toastify";
import { RouteNames } from "../utils/RouteNames";
import { useUserAuthStore } from "../store/auth";
import { FaSpinner } from "react-icons/fa";
import NewRight from "./NewRight";

type resendCodeProps = {
  email: string;
};
type VerifyOTPProps = {
  email: string;
  otp: string;
};
type VerifyProps = {
  isSignup: boolean;
};

const Verify = ({ isSignup }: VerifyProps) => {
  const userAuthStore = useUserAuthStore((state) => state);
  const navigate = useNavigate();
  const location = useLocation();
  const { email } = location.state || {};
  const [otpCode, setOtpCode] = useState<string[]>(["", "", "", "", "", ""]);
  const [isVerified, setIsVerified] = useState(false);
  const inputRefs = useRef<HTMLInputElement[]>([]);

  useEffect(() => {
    document.title = "Verify Account - Huxxle";
  }, []);
  
  const mutation = useMutation((data: VerifyOTPProps) =>
    AuthServices.verifyOtp(data)
  );
  const resendCodeMutation = useMutation((data: resendCodeProps) =>
    AuthServices.login(data)
  );

  const resendCode = (data: resendCodeProps) => {
    const signupData: resendCodeProps = {
      ...data,
      email: email,
    };

    resendCodeMutation.mutate(signupData, {
      onSuccess: async (data) => {
        toast.success(data?.data?.message);
      },
      onError: (error: any) => {
        toast.error(
          error?.response ? error?.response?.data?.error : error?.message
        );
      },
    });
  };

  const handleBack = () => {
    navigate(-1); // Go back to the previous page
  };

  const handleOTPVerification = 
    (e?: React.FormEvent) => {
      if (e) e.preventDefault();

      const enteredOTP = otpCode.join("");
      const verifyData: VerifyOTPProps = {
        email,
        otp: enteredOTP,
      };
      if (otpCode.every((code) => code !== "")) {
        mutation.mutate(verifyData, {
          onSuccess: async (data) => {
            setIsVerified(true);
            userAuthStore.updateUser(data?.data?.user);
            userAuthStore.updateToken(data?.data?.token);
            userAuthStore.updateBusiness(data?.data?.business);
            userAuthStore.updateBank(data?.data?.bank);
            userAuthStore.updateAccount(data?.data?.newAccount);
            userAuthStore.updateBusinessSocialMedia(
              data?.data?.businessSocialMedia
            );
            userAuthStore.updateUser(data?.data?.user);
            userAuthStore.updateUserStatus(data?.data?.userStatus);
            userAuthStore.updateBusinessWebsite(data?.data?.businessWebsite);
            sessionStorage.removeItem("signupFormData"); // Clear form data on successful submission

            if (isSignup) {
              toast.success("User signed up successfully!");
              navigate(RouteNames.welcome);
            } else {
              toast.success("User logged in successfully!");
              navigate(RouteNames.overview);
              if (Object.keys(data?.data?.business).length === 0) {
                navigate(RouteNames.business);
              }
            }
          },
          onError: (error: any) => {
            toast.error(
              error?.response ? error?.response?.data?.error : error?.message
            );
          },
        });
      }
    }

  

 const handleInputChange = (index: number, value: string) => {
   const newOtp = [...otpCode];

   // Allow only numeric values
   if (!/^\d?$/.test(value)) {
     return; // Ignore non-numeric input
   }

   newOtp[index] = value;
   setOtpCode(newOtp);

   // Focus next input if the current input has a value
   if (value.length === 1) {
     focusNextInput(index);
   }

   // Trigger verification if all fields are filled
   if (newOtp.every((code) => code !== "")) {
     handleOTPVerification();
   }
 };




  const handleKeyDown = (
    event: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (event.key === "Enter") {
      handleOTPVerification();
    } else if (event.key === "Backspace" && !otpCode[index]) {
      focusPrevInput(index);
    }
  };

  const focusNextInput = (index: number) => {
    if (index < otpCode.length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const focusPrevInput = (index: number) => {
    if (index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    const pastedData = event.clipboardData
      .getData("Text")
      .slice(0, otpCode.length);

    if (pastedData.length === otpCode.length) {
      const newOtp = pastedData.split("");
      setOtpCode(newOtp);


      if (newOtp.every((code) => code !== "")) {
        handleOTPVerification(); // Trigger verification after pasting full OTP
      }
    }
  };

  return (
    <div className="fixed inset-0 flex flex-col h-full md:flex-row  p-5 mt-0 lg:mt-0 md:justify-center gap-2 w-full  overflow-auto">
      <div className="flex-1 h-full">
        <div className="flex flex-col h-full">
          {/* Left */}
          <div className="h-full">
            <div className="flex p-2 justify-center md:justify-start">
              <img src={Logo} alt="Katajere Logo" className="mb-[15px]" />
            </div>
            <div
              className={
                isVerified
                  ? "sm:px-[40px]  h-full mt-[50px] font-sora"
                  : "sm:px-[40px] h-full sm:mt-[20%] mt-12 font-sora"
              }
            >
              {!isVerified ? (
                <div className="max-w-[496px] mx-auto">
                  <h2 className="text-[24px] sm:text-[28px]">
                    Verify Email Address
                  </h2>
                  <p className="sm:mt-5 mt-2 mb-6 text-sm text-[#7e8494] leading-[22px]">
                    We have sent a 6 digit unique code to the email
                    <span className="text-primary-purple"> {email}</span>,
                    please kindly enter the code below to complete your email
                    verification.
                  </p>
                  <form onSubmit={handleOTPVerification}>
                    <div className="flex justify-between">
                      {otpCode.map((value, index) => (
                        <NumericInput
                          key={index}
                          inputRef={(el: any) =>
                            (inputRefs.current[index] = el)
                          }
                          value={value}
                          onChange={(val) => handleInputChange(index, val)}
                          maxLength={1}
                          className="w-[54.67px] h-[52px] text-center"
                          required
                          onKeyDown={(e) => handleKeyDown(e, index)}
                          onPaste={(e) => handlePaste(e)}
                        />
                      ))}
                    </div>
                    <p className="my-6 text-center text-sm font-sora font-light">
                      I have not received a code?{" "}
                      <span
                        onClick={() => resendCode({ email })}
                        className="text-primary-purple font-semibold cursor-pointer"
                      >
                        Send Code Again
                      </span>
                    </p>
                    <div className="flex justify-between">
                      <button
                        onClick={handleBack}
                        type="button"
                        className="w-[30%] p-4 flex border-[1.5px] border-[#7e8494] rounded-[12px] text-black text-sm h-[40px] cursor-pointer transition duration-300 bg-white items-center justify-center"
                      >
                        Back
                      </button>
                      <button
                        disabled={
                          !otpCode.every((code) => code !== "") ||
                          mutation.isLoading
                        }
                        type="submit"
                        className={`p-4 w-[67%] right-0 flex border-none rounded-[12px] text-white text-sm h-[40px] cursor-pointer transition duration-300 items-center justify-center ${
                          !otpCode.every((code) => code !== "") ||
                          mutation.isLoading
                            ? "opacity-50 bg-primary-purple cursor-not-allowed"
                            : "bg-primary-purple"
                        }`}
                      >
                        {mutation.isLoading ? (
                          <p className="flex justify-center animate-spin-slow-fast">
                            <FaSpinner size={20} />
                          </p>
                        ) : (
                          "Verify"
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              ) : (
                isSignup && <WelcomeVerify />
              )}
            </div>
            {/* <div className="mt-auto">
              {" "}
              <div className="flex  justify-between md:hidden gap-5">
                <button className="border-[1px] font-sora text-[#5B5B5B] rounded-lg py-3 border-primary-neutral300 flex-1">
                  Terms of Use
                </button>
                <button className="border-[1px] font-sora text-[#5B5B5B] rounded-lg py-3 border-primary-neutral300 flex-1">
                  Privacy Policy
                </button>
              </div>
            </div> */}

            <div
              className={
                isVerified ? "flex mt-auto items-center" : "flex justify-center md:hidden mt-auto  "
              }
            >
              <Messages3 size="24" color="#5B5B5B" />
              <p className="text-xs pl-2 text-[#5b5b5b]">
                Do you need help? Reach out to{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="no-underline text-[#4f4cd8]"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
          {/* right */}
       
          {/* <HoneyComb /> */}
        </div>
        
      </div>
         <div className="w-full h-full lg:block flex-1 hidden">
            <NewRight/>
          </div>
    </div>
  );
};

export default Verify;
