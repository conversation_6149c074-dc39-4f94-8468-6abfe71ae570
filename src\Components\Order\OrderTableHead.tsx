/* eslint-disable @typescript-eslint/no-explicit-any */
import { SearchNormal1 } from "iconsax-react";
import Filter from "../../Constants/Filter";
import { useState } from "react";

interface OrderHead {
  // data: string[];
  searchTerm: string;
  selectedCategory: string;
  selectedStatus: string;
  onSearchChange: (value: string) => void;
  onCategoryChange: (value: string) => void;
  onStatusChange: (value: string) => void;
}

const OrderTableHead: React.FC<OrderHead> = ({
  searchTerm,
  // selectedCategory,
  selectedStatus,
  onSearchChange,
  // onCategoryChange,
  onStatusChange,
}) => {
    const [dropDown, setDropDown] = useState<any>(false);
  
  return (
    <div className="flex flex-col md:flex-row justify-between items-start p-5">
      {/* Title Section */}
      <div className="table-tittle w-full md:w-[626px]">
        <h1 className="text-lg text-[#393939] mb-2 font-semibold">
          Promo/Sales History
        </h1>
        <p className="text-sm text-[#919191] font-normal font-sora mb-2.5">
          Track all your stock movement over time
        </p>
      </div>

      {/* Action Section */}
      <div className="w-full md:w-[671px] flex flex-col md:flex-row gap-2.5">
        <div className="flex w-full gap-2 items-center">
          <div className="flex items-center h-[42px] gap-[10px] py-2 px-2 border border-gray-300 text-[#ABABAB] text-xs rounded-2xl bg-[#fcfcfc] w-full">
            <SearchNormal1 size="20" color="#ABABAB" />
            <input
              type="text"
              id="searchInput"
              placeholder="Search"
              value={searchTerm}
              className="bg-[#fcfcfc] outline-none w-full"
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>
          <div className="md:hidden">
            <Filter
              statuses="Status"
              stat1="Active"
              stat2="Expired"
              stat3="Inactive"
              selectedStatus={selectedStatus}
              onStatusChange={onStatusChange}
              setDropDown={setDropDown}
              dropDown={dropDown}
            />
          </div>
        </div>

        <select
          id="statusFilter"
          value={selectedStatus}
          onChange={(e) => onStatusChange(e.target.value)}
          className="hidden md:inline py-2 px-4 border text-sm text-[#ABABAB] border-gray-300 rounded-2xl bg-[#fcfcfc] outline-none w-full md:w-auto">
          <option value="">Filter by Status</option>
          <option value="Active">Active</option>
          <option value="Expired">Expired</option>
          <option value="Inactive">Inactive</option>
        </select>
      </div>
    </div>
  );
};
export default OrderTableHead;
