// Initialize service worker for push notifications
export function register(config: any): void {
  if ('serviceWorker' in navigator) {
    console.log("<PERSON><PERSON><PERSON> supports service workers, registering...");
    window.addEventListener('load', () => {
      // Use an absolute path for the service worker
      const swUrl = `${window.location.origin}/sw-push.js`;
      console.log("Registering service worker:", swUrl);
      
      navigator.serviceWorker.register(swUrl)
        .then(registration => {
          console.log('Service worker registered successfully:', registration);
          
          // Watch for updates to the service worker
          registration.onupdatefound = () => {
            const installingWorker = registration.installing;
            if (!installingWorker) return;
            
            installingWorker.onstatechange = () => {
              console.log(`Service worker state changed to: ${installingWorker.state}`);
              
              if (installingWorker.state === 'installed') {
                if (navigator.serviceWorker.controller) {
                  // At this point, the updated precached content has been fetched,
                  // but the previous service worker will still serve the older content
                  console.log('New content is available and will be used when all tabs are closed');
                  
                  // Execute callback if set
                  if (config && config.onUpdate) {
                    config.onUpdate(registration);
                  }
                } else {
                  // At this point, everything has been precached
                  console.log('Content is cached for offline use');
                  
                  // Execute callback if set
                  if (config && config.onSuccess) {
                    config.onSuccess(registration);
                  }
                }
              }
            };
          };
        })
        .catch(error => {
          console.error('Error during service worker registration:', error);
        });
    });
  } else {
    console.warn('Service workers are not supported by this browser');
  }
}

export function unregister(): void {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready
      .then(registration => {
        registration.unregister();
      })
      .catch(error => {
        console.error(error.message);
      });
  }
}
