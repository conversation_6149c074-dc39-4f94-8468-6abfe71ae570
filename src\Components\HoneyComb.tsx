import { useState, useEffect } from "react";
import { Link } from "react-router-dom";

const HoneyComb = () => {
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState<number>(0);
  const quotes = [
    "The difference between a successful person and others is not a lack of strength, not a lack of knowledge, but rather a lack in will. – <PERSON>",
    "Success is not the key to happiness. Happiness is the key to success. If you love what you are doing, you will be successful. – <PERSON>",
    "The only limit to our realization of tomorrow is our doubts of today. – <PERSON>",
  ];

  const nextQuote = () => {
    setCurrentQuoteIndex((prevIndex) =>
      prevIndex === quotes.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevQuote = () => {
    setCurrentQuoteIndex((prevIndex) =>
      prevIndex === 0 ? quotes.length - 1 : prevIndex - 1
    );
  };

  // Automatically change quotes every 2 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      nextQuote();
    }, 4000); // 4000ms = 4 seconds

    // Cleanup the interval on component unmount
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="absolute hidden mt-3 lg:flex h-full w-1/2 top-0 left-1/2 bg-[url('assets/right_vector.svg')] bg-no-repeat bg-contain bg-[100%]  flex-col items-center justify-center">
      <div className="absolute top-[20px] right-[20px] flex gap-[15px] w-[200px] h-[36px] flex-row items-center justify-center">
        <Link to='https://huxxle.com/terms-of-use' target="blank" className="flex h-[30px] text-center bg-[#e6e5f91a] gap-[10px] rounded-[12px] border-[0.5px] border-[#817fe3] text-[#cdccf4] text-xs items-center px-[7.5px] cursor-pointer hover:bg-[#3836b928]">
          Terms of Use
        </Link>
        <Link to='https://huxxle.com/privacy-policy' target="blank" className="flex h-[30px] text-center bg-[#e6e5f91a] gap-[10px] rounded-[12px] border-[0.5px] border-[#817fe3] text-[#cdccf4] text-xs items-center px-[7.5px] cursor-pointer hover:bg-[#3836b928]">
          Privacy Policy
        </Link>
      </div>
      <h2 className="text-center text-[30px] leading-[36px] text-[#fcfcfc] font-normal ml-[90px] mr-[50px] absolute bottom-[36%]">
        Managing Your Business Made Easy
      </h2>
      <div className="rounded-[24px] border-[0.5px] border-[#817fe3] m-[20px] absolute  lx:left-[43px] lg:[20px] xl:left-[45.5px] bg-[#e6e5f91a] h-[95px] flex bottom-[28%] md:bottom-[12.5%] gap-[10px]">
        <p className="p-[15px] text-[12px] leading-[20px] text-[#cdccf4] w-[422px]">
          {quotes[currentQuoteIndex]}
        </p>
      </div>
      <div className="flex items-center justify-between w-full mx-auto absolute left-3.5 bottom-0 p-[10px]">
        <div
          onClick={prevQuote}
          className="cursor-pointer border-[2px] border-primary-purple200 rounded-full flex justify-center h-[64px] w-[64px] text-primary-purple200 text-5xl"
        >
          ←
        </div>
        <div className="flex space-x-2">
          {quotes.map((_, index) => (
            <span
              key={index}
              className={`block h-2 rounded-full ${
                index === currentQuoteIndex
                  ? "bg-primary-purple500 w-[30px]"
                  : "bg-primary-purple200 w-[8px]"
              }`}
            ></span>
          ))}
        </div>
        <div
          onClick={nextQuote}
          className="cursor-pointer border-[2px] border-primary-purple200 rounded-full flex justify-center h-[64px] w-[64px] text-primary-purple200 text-5xl"
        >
          →
        </div>
      </div>
    </div>
  );
};

export default HoneyComb;
