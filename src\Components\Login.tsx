/* eslint-disable @typescript-eslint/no-explicit-any */
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import AuthInput from "../Constants/AuthInput";
import { useMutation } from "react-query";
import { AuthServices } from "../utils/Services/AuthServices";
import { toast } from "react-toastify";
import { RouteNames } from "../utils/RouteNames";
import { useNavigate } from "react-router-dom";
import { FaSpinner } from "react-icons/fa";

type LoginProps = {
  email: string;
};
const Login = () => {
  const navigate = useNavigate();
  const {
    formState: { isDirty, isValid, errors },
    handleSubmit,
    control,
  } = useForm<LoginProps>({
    mode: "onChange",
  });
  const mutation = useMutation((data: LoginProps) => AuthServices.login(data));
  const onSubmitForm: SubmitHandler<LoginProps> = (data) => {
    const loginData = {
      ...data,
    };

    mutation.mutate(loginData, {
      onSuccess: async (data) => {
        toast.success(data?.data?.message);
        navigate(RouteNames.verifyLogin, {
          state: { email: loginData.email },
        });
      },
      onError: (error: any) => {
        if (error?.response?.status === 429) {
          toast.error(error?.response?.data?.message);
        } else {
          toast.error(
            error?.response ? error?.response?.data?.error : error?.message
          );
        }
      },
    });
  };
  return (
    <>
      <form className="mt-4" onSubmit={handleSubmit(onSubmitForm)}>
        <Controller
          name="email"
          rules={{
            required: "email address is required",
            pattern: {
              value:
                /^([\w-.]+@([\w-]+\.)+[\w-]{2,4}|(\+?\d{1,3}[- ]?)?\d{10})$/,
              message: "Invalid email format",
            },
          }}
          control={control}
          render={({ field }) => (
            <AuthInput
              label="Email Address or Phone Number"
              type="text"
              placeholder="Enter your email address or phone number"
              errorMessage={errors.email && errors.email.message}
              {...field}
            />
          )}
        />

        <button
          type="submit"
          disabled={!isDirty || !isValid || mutation.isLoading}
          className={`text-primary-baseWhite hover:bg-[#6866DE] font-sora  h-[49px] px-4 rounded-[16px] focus:outline-none focus:shadow-outline w-full ${
            !isDirty || !isValid || mutation.isLoading
              ? "bg-primary-purple opacity-50 cursor-not-allowed "
              : " bg-primary-purple"
          }`}
        >
          {mutation.isLoading ? (
            <p className="flex justify-center animate-spin ">
              <FaSpinner size={20} />
            </p>
          ) : (
            "Log In"
          )}
        </button>
      </form>
    </>
  );
};
export default Login;
