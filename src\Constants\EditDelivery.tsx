/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from "react";
import InputField from "../Constants/InputField";
import { orderServices } from "../utils/Services/Order";
import { useMutation } from "react-query";
import { useUserAuthStore } from "../store/auth";
import { toast } from "react-toastify";
import { InfoCircle, Warning2 } from "iconsax-react";
interface OrderProps {
  closeModal: () => void;
  isOpen: boolean;
  selectedDelivery: any;
  refetch:any
}
interface AddDelivery {
  vendorId: string;
  deliveryFeeValue: number;
  deliveryTitle: string;
  isPublic: string;
}

const EditDelivery: React.FC<OrderProps> = ({
  closeModal,
  isOpen,
  selectedDelivery,
  refetch
}) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    return () => {
      document.body.classList.remove("no-scroll");
    };
  }, [isOpen]);

  const [isToggled, setIsToggled] = useState<boolean>(false);
  const user = useUserAuthStore((state) => state.user);
  const [addDelivery, setAddDelivery] = useState<AddDelivery>({
    vendorId: user.vendorId,
    deliveryFeeValue: 0,
    deliveryTitle: "",
    isPublic: "",
  });

  useEffect(() => {
    if (selectedDelivery) {
      setAddDelivery({
        vendorId: user.vendorId,
        deliveryFeeValue: selectedDelivery.deliveryValue.toString(),
        deliveryTitle: selectedDelivery.deliveryTitle,
        isPublic: selectedDelivery.isPublic,
      });
      setIsToggled(selectedDelivery.isPublic);
    }
  }, [selectedDelivery, user.userEmail, user.vendorId]);

  const deliveryFee = useMutation(orderServices.updateDelivery);
  const isDisabled =
    !addDelivery.deliveryTitle || !addDelivery.deliveryFeeValue;

  // Function to handle the toggle
  const handleToggle = () => {
    setIsToggled(!isToggled);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
  
    if (name === "deliveryFeeValue") {
      // Allow only numbers (0-9), removing any other characters like hyphens, dots, spaces, etc.
      const numericValue = value.replace(/[^0-9]/g, ""); // Removes everything except digits
  
      setAddDelivery((prevState) => ({
        ...prevState,
        [name]: Number(numericValue),
      }));
    } else {
      setAddDelivery((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    }
  };
  
  

  const handleSubmit = async () => {
    const payload = {
      deliveryId: selectedDelivery.deliveryId,
      vendorId: user.vendorId,
      deliveryTitle: addDelivery.deliveryTitle,
      isPublic: isToggled ? "Yes" : "No",
      deliveryFeeValue: addDelivery.deliveryFeeValue,
    };
    try {
      const res: any = await deliveryFee.mutateAsync(payload);
      console.log(res);
      toast.success(res.message)
      refetch()
      closeModal();
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="fixed font-sora  bg-black/10 top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-1 md:p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? "translate-x-0 " : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-0 pr-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="overflow-y-auto h-screen scrollbar-none hidescroll ">
            <div className="md:pl-6 px-2 md:px-0 ">
              <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                Update Delivery Fee
              </h2>
              <p className=" text-sm text-[#919191] font-sora ">
                Kindly provide your delivery fee details
              </p>
              <div className="md:py-4 py-3 px-4 grid gap-2 mt-6 bg-[#FFF2DF] rounded-2xl md:px-6">
                <p className="flex font-sora  font-bold text-[16px] items-center gap-2">
                  {" "}
                  <InfoCircle variant="Bold" color="#FF9900" /> Set Your
                  Delivery Prices
                </p>
                <p className="text-[#5B5B5B] text-[14px] leading-[22.4px] font-sora">
                Kindly set prices for delivery, Your customers will select from this delivery options during check out. You can always update it anytime.
                </p>
              </div>
            </div>
            <div className="p-6 rounded-2xl flex flex-col gap-6 last:mb-[200px]">
              <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                  Title
                </p>
                <InputField
                  id="title"
                  placeholder="Give Your delivery a title"
                  onChange={handleChange}
                  value={addDelivery.deliveryTitle}
                  name="deliveryTitle"
                />
                <p className="text-[12px] text-[#5B5B5B] flex gap-2 items-center font-sora"> <Warning2 size={16}/> e.g Delivery within Lagos</p>
              </div>
              <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                  Amount
                </p>
                <InputField
                  id="amount"
                  placeholder="Enter Amount"
                  onChange={handleChange}
                  type="number"
                  onKeyDown={(e) => {
                    const allowedKeys = ["Backspace", "Tab", "ArrowLeft", "ArrowRight"];
                    if (
                      !/[0-9]/.test(e.key) &&
                      !allowedKeys.includes(e.key)
                    ) {
                      e.preventDefault();
                    }
                  }}
                  value={addDelivery.deliveryFeeValue}
                  
                  name="deliveryFeeValue"
                />
              </div>
              <div className="p-6 flex bg-[#F5F5F5] md:mb-0 mb-[300px] rounded-2xl flex-col gap-3">
                <h2 className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                  Do you want customer’s to see this on your website ?
                </h2>
                <div className="flex justify-between">
                  <p>Yes</p>
                  <div
                    className={`w-10 h-6 flex items-center bg-gray-300  rounded-full p-1 cursor-pointer ${
                      isToggled ? "bg-purple-600" : "bg-gray-300"
                    }`}
                    onClick={handleToggle}
                  >
                    <div
                      className={`bg-white w-4 h-4 rounded-full shadow-md transform ${
                        isToggled ? "translate-x-5" : "translate-x-0"
                      } transition`}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 px-3  md:p-10">
              <div className="flex w-full px-7.5 gap-2.5">
                <button
                  onClick={closeModal}
                  className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2"
                >
                  Back
                </button>
                <button
                  className={`${
                    isDisabled ? "opacity-50 cursor-not-allowed" : ""
                  } ${
                    deliveryFee.isLoading ? "opacity-50 cursor-auto" : ""
                  } w-full text-[#fcfcfc] bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 h-[49px] rounded-2xl cursor-pointer flex-2 `}
                  onClick={handleSubmit}
                  //   onClick={handleProduct}
                  disabled={deliveryFee.isLoading || isDisabled}
                >
                  {deliveryFee.isLoading ? "Updating Fee" : " Update Delivery Fee"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
     
    </div>
  );
};

export default EditDelivery;
