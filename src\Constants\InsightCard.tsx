/* eslint-disable @typescript-eslint/no-explicit-any */
interface InsightCardProps {
  text?: any;
  buttonText?: string;
}

const InsightCard: React.FC<InsightCardProps> = ({ text, buttonText }) => {
  return (
    <div className="bg-[#F5F5F5] py-6 px-4 rounded-3xl mb-6">
      <div>{text}</div>
      <button
        type="button"
        className="border border-[#4F4CD8] mt-6 py-3 max-w-[180px] w-full rounded-2xl text-sm font-semibold text-[#1A1A1A]">
        {buttonText}
      </button>
    </div>
  );
};
export default InsightCard;
