import axios from 'axios';
import { isTokenValid } from './Helpers';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useUserAuthStore } from '../store/auth';

const KatajereAPI = () => {
 
  let headers = {};

  const url = window.location.pathname;
  if (url !== '/' && url !== '/verify') {
    const token = useUserAuthStore.getState().token || (() => {
      const userAuthStore = localStorage.getItem('user-auth-store');
      if (userAuthStore) {
        const parsedAuthStore = JSON.parse(userAuthStore);
        return parsedAuthStore.token;
      }
      return null;  // Return null if token is not found in either store or localStorage
    })();
    if (token) {
      if (!isTokenValid(token)) {
        toast.error('Session expired,kindly login again');
        setTimeout(() => {
          toast.dismiss();
        }, 5000);
        sessionStorage.removeItem('user-auth-store');
        localStorage.removeItem('user-auth-store'); // Also clear from local storage
        window.location.href = '/';
       
      }
      headers = {
        Authorization: `Bearer ${token}`,
      };
    }
  }

  const baseURL = import.meta.env.VITE_REACT_APP_KATAJERE_BASE_URL || '';
  const axiosInstance = axios.create({
    baseURL,
    timeout: 30000,
    headers,
  });

  return axiosInstance;
};

export default KatajereAPI;
