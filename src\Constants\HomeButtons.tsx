import React from "react";

interface ActionButton {
  title?: string;
  image?: React.ElementType;
  bgColor?: string;
  textColor?: string;
  border?: string;
  borderColor?: string;
  hover?: string;
  type?: string;
  customStyles?: string;

  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  onKeyPress?: (e: React.KeyboardEvent<HTMLButtonElement>) => void;
}

const HomeButtons: React.FC<ActionButton> = ({
  title,
  image: IconComponent,
  bgColor,
  textColor,
  border,
  borderColor,
  onClick,
  customStyles,
  hover,
}) => {
  return (
    <button
      onClick={onClick}
      className={`w-full h-[49px] flex items-center gap-2 justify-center rounded-[16px]  py-[8px] md:px-[16px] font-sora font-semibold text-[14px] duration-300 ${bgColor} ${hover} ${textColor} ${border} ${borderColor} ${customStyles} mb-0 md:mb-0 `}
    >
      <span className="md:text-[14px] text-[12px] leading-[20px]">{title}</span>
      {IconComponent && <IconComponent size={16} />}
    </button>
  );
};

export default HomeButtons;
