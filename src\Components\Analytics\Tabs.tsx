import { useState, ReactNode } from 'react';

interface Tab {
  title: string;
  content: ReactNode;
}

interface TabsProps {
  tabs: Tab[];
}

const Tabs: React.FC<TabsProps> = ({ tabs }) => {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <>
      <div className="w-full font-sora overflow-x-scroll lg:overflow-x-hidden">
        <div className="flex border-[#EFEFEF] border-b-4">
          {tabs.map((tab, index) => (
            <div
              key={index}
              onClick={() => setActiveTab(index)}
              className={`flex items-center ${
                activeTab === index ? '' : ''
              }`}>
              <div className="">
                <button
                  key={index}
                  className={`p-2.5  font-semibold text-sm mr-4 truncate  ${
                    activeTab === index
                      ? 'text-[#4F4CD8] border-b-4 border-[#4F4CD8]'
                      : 'text-[#BBBBBB]'
                  } focus:outline-none`}>
                  {tab.title}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className='px-2 md:px-0'>{tabs[activeTab].content}</div>
    </>
  );
};

export default Tabs;
