/* eslint-disable @typescript-eslint/no-explicit-any */
import { Save2 } from 'iconsax-react';
import SettingsInput from '../../Constants/SettingsInput';
import { useUserAuthStore } from '../../store/auth';
import { useState } from 'react';
import { useMutation } from 'react-query';
import { orderServices } from '../../utils/Services/Settings';
import { toast } from 'react-toastify';

interface profileSettings {
  vendor_email: string;
  first_name: string;
  last_name: string;
  vendorId: string;
}
const PersonalProfile = () => {
  const user = useUserAuthStore((state) => state.user);
  const updateUser = useUserAuthStore((state) => state.updateUser);
  const [profile, setProfile] = useState<profileSettings>({
    vendor_email: user.userEmail,
    first_name: user.firstName,
    last_name: user.lastName,
    vendorId: user.vendorId,
  });
  const mutation = useMutation((formData: FormData) =>
    orderServices.editUserProfile(formData)
  );
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setProfile((prevDetails) => ({
      ...prevDetails,
      [name]: value,
    }));
  };
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append('vendorId', profile.vendorId);
    const userData = {
      vendor_email: profile.vendor_email,
      first_name: profile.first_name,
      last_name: profile.last_name,
    };
    formData.append('userData', JSON.stringify(userData));
    mutation.mutate(formData, {
      onSuccess: (data) => {
        updateUser(data?.data?.user);
        toast.success(data?.data?.message || 'Profile updated successfully!');
      },
      onError: (error: any) => {
        const errorMessage =
          error?.response?.data?.error || 'Something went wrong';
        toast.error(errorMessage);
      },
    });
  };

  return (
    <div className="mt-6 flex items-center justify-center w-full">
      <form
        onSubmit={handleSubmit}
        className="max-w-[540px] w-full border p-6 rounded-2xl">
        <SettingsInput
          placeholder="First Name"
          label="First Name"
          name="first_name"
          value={profile.first_name}
          onChange={handleChange}
        />
        <SettingsInput
          placeholder="Last Name"
          label="Last Name"
          value={profile.last_name}
          name="last_name"
          onChange={handleChange}
        />
        <SettingsInput
          placeholder=" Email Address"
          label=" Email Address"
          type="email"
          name="vendor_email"
          value={profile.vendor_email}
          onChange={handleChange}
        />
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={mutation.isLoading}
            className={` flex gap-[31px] px-4 h-[41px] rounded-2xl items-center font-semibold text-[#FCFCFC] text-sm ${
              mutation.isLoading
                ? 'bg-[#4F4CD8]  opacity-50 cursor-not-allowed '
                : ' bg-[#4F4CD8] '
            }`}>
            <span>{mutation.isLoading ? 'saving . . .' : 'Save Changes'}</span>
            <Save2 size="16" />
          </button>
        </div>
      </form>
    </div>
  );
};
export default PersonalProfile;
