import { useState, useRef, useEffect } from "react";
import { Calendar as DateCalendar } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { Calendar } from "iconsax-react";
import "../calender.css"; // Assuming you have some custom styles here

interface DateInputPickerProps {
  selectedDate: Date;
  onDateChange: (date: Date) => void;
}

const DateInputPickerPromo: React.FC<DateInputPickerProps> = ({
  selectedDate,
  onDateChange,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  const togglePicker = () => {
    setShowPicker(!showPicker);
  };

  const handleSelect = (date: Date) => {
    onDateChange(date);
    setShowPicker(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setShowPicker(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [ref]);

  return (
    <div className="relative" ref={ref}>
      {/* Date Display Box */}
      <div
        onClick={togglePicker}
        className="flex items-center justify-between gap-2  bg-white text-[#5b5b5b] text-sm font-sora font-normal h-[48px] px-4 border border-[#cccccc] rounded-2xl cursor-pointer ">
        <span className="text-gray-700">
          {selectedDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })}
        </span>
        <Calendar size="20" className="text-gray-500" />
      </div>

      {/* Date Picker */}
      {showPicker && (
        <div className="absolute z-50 -top-10 bg-white border border-gray-300 rounded-lg p-4">
          <DateCalendar
            date={selectedDate}
            onChange={handleSelect}
            className="custom-date-picker "
          />
        </div>
      )}
    </div>
  );
};

export default DateInputPickerPromo;
