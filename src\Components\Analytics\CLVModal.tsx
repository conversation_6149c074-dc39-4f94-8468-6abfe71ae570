/* eslint-disable @typescript-eslint/no-explicit-any */
import { InsightTable } from "../../utils/InsightTable";

const CLVModal = ({ isOpen, onClose, data }: any) => {
  const rows = data?.customerLifetimeValueFullList ?? []


  
  const headers = [
    {
      title: 'Customer Name',
      render: (row: any) => (
        <p className="text-[#5B5B5B] text-xs font-bold ">{row.customerName}</p>
      ),
    },
    {
      title: 'CLV(₦)',
      render: (row: any) => (
        <p className="text-[#5B5B5B] text-xs font-normal">{row.clv}</p>
      ),
    },
    {
      title: 'Customer Segmentation ',
      render: (row: any) => (
        <p className="text-[#5B5B5B] font-normal text-xs ">{row.segmentation}</p>
      ),
    },
  ];


  // const rows = [
  //   {
  //     id: 1,
  //     customerName: '<PERSON>',
  //     price: '₦2,400,000',
  //     customerSeg: 'Frequent  Buyers ',
  //     action: 'Offer Premium',
  //     percent: '16%',
  //   },
  //   {
  //     id: 2,
  //     customerName: '<PERSON> McKinney',
  //     price: '₦500,000',
  //     customerSeg: 'Frequent  Buyers ',
  //     action: 'Send Loyalty Offer',
  //     percent: '10%',
  //   },
  //   {
  //     id: 3,
  //     customerName: 'Floyd Miles',
  //     price: '₦500,000',
  //     customerSeg: 'Frequent  Buyers ',
  //     action: 'Reconnect',
  //     percent: '5%',
  //   },
  //   {
  //     id: 4,
  //     customerName: 'Jacob Jones',
  //     price: '₦400,000',
  //     customerSeg: 'High Spenders  ',
  //     action: 'Reconnect',
  //     percent: '4%',
  //   },
  //   {
  //     id: 5,
  //     customerName: 'Albert Flores',
  //     price: '₦350,000',
  //     customerSeg: 'Occasional  Buyers   ',
  //     action: 'Reconnect',
  //     percent: '2%',
  //   },
  // ];


  return (
    <div className="fixed font-sora  top-0 left-0 w-full h-full  bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-screen md:w-[580px] bg-[#FCFCFC] backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${isOpen ? 'translate-x-0' : 'translate-x-full'
          }`}>
        <div>
          <span
            onClick={onClose}
            className=" text-4xl font-bold cursor-pointer my-4 flex justify-end z-[999999]">
            &times;
          </span>
          <div className="px-2 sm:px-10 overflow-y-auto h-screen scrollbar-none hidescroll  pb-32">
            <h4 className="text-[#181818] font-semibold text-2xl">
              Customer Lifetime Value (CLV){' '}
            </h4>
            <p className="text-[#919191] font-normal text-sm mb-6">
              Customer Lifetime Value for your products{' '}
            </p>
            <div className="overflow-x-auto max-w-[240px] sm:max-w-full ">
              <InsightTable rows={rows} headers={headers} showHead={true} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default CLVModal