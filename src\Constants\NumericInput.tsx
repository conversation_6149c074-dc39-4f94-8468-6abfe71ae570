// NumericInput.tsx
/* eslint-disable @typescript-eslint/no-explicit-any */

interface NumericInputProps {
  value: string;
  onChange: (value: string) => void;
  maxLength?: number;
  className?: string;
  required?: boolean;
  inputRef?: any;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void; // Add onKeyDown prop
  onPaste?: (event: React.ClipboardEvent<HTMLInputElement>) => void; // Add onPaste here
}

const NumericInput: React.FC<NumericInputProps> = ({
  value,
  onChange,
  maxLength = 1,
  className = "",
  required = false,
  inputRef,
  onKeyDown,
  onPaste
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (/^\d$/.test(value)) {
      onChange(value);
    } else if (value === "") {
      onChange("");
    }
  };

  return (
    <input
      ref={inputRef}
      type="text"
      inputMode="numeric"
      maxLength={maxLength}
      value={value}
      onChange={handleChange}
      onKeyDown={onKeyDown} // Pass onKeyDown to handle key events
      className={`  sm:w-[54.67px] sm:h-[52px] w-10 h-10  border-[2px] border-[#DCDCDC] rounded-[12px] p-4 gap-[10px] text-center text-lg leading-[21px] font-normal text-[#9a9a9a] shadow-[0px_1px_3px_0px_#3232471a] ${className}`}
      required={required}
      onPaste={onPaste}
    />
  );
};

export default NumericInput;
