/* eslint-disable @typescript-eslint/no-explicit-any */

import { useState } from "react";
import { useMutation } from "react-query";
import { inventoryServices } from "../../../utils/Services/InventoryServices";
import { useUserAuthStore } from "../../../store/auth";
import { toast } from "react-toastify";

interface FrequencyModalProps {
  isOpen: boolean;
  closeModal: () => void;
  settings: any;
  refetch:any

}

const FrequencyModal: React.FC<FrequencyModalProps> = ({
  isOpen,
  closeModal,
  settings,
  refetch


}) => {
  const [frequency, setFrequency] = useState<string>(settings.notificationFrequency)

    const frequencyMutate = useMutation(inventoryServices.alertSettings);

  const handleFrequency = (name:string) => {
    if (name === 'immediateSummary') {
      setFrequency('Immediate')
    }
    else if (name === 'dailySummary') {
      setFrequency('Daily')
    } else {
      setFrequency('Weekly')
    }
  }
  const user = useUserAuthStore((state) => state.user);
  
  const handleSave = () => {
    const payload = {
    notificationFrequency:frequency,
      vendorId: user.vendorId,
    };
    try {
      frequencyMutate.mutate(payload, {
        onSuccess: (response) => {
          refetch();
          closeModal();
          toast.success(response?.data?.message);
        },

        onError: (error: any) => {
          toast.error(error?.response?.data?.error || error?.message);
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="fixed font-sora top-0 left-0 w-full h-full bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-screen md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="flex flex-col h-full">
          <span
            onClick={closeModal}
            className="close-drawer text-4xl pr-4 font-bold cursor-pointer my-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="overflow-y-auto flex-grow scrollbar-none hidescroll">
            <div className="pl-6 flex flex-col gap-6">
              <div>
                <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold">
                  Set Frequency
                </h2>
                <p className="text-sm text-[#919191] font-sora">
                  Manage the frequency at which you get notifications.
                </p>
              </div>
              <div className="rounded-2xl flex flex-col gap-2 border-primary-neutral200 border-[1px] p-4">
                <div className="flex justify-between items-center">
                  <p className="text-primary-neutralt1 font-sora text-[16px] leading-[25.6px]">
                    Immediate Alerts
                  </p>
                  <input
                    type="radio"
                    name="immediateSummary"
                    className="w-5 h-5 accent-primary-purple rounded-full"
                    checked={frequency === "Immediate"}
                    onChange={() => handleFrequency("immediateSummary")}
                  />
                </div>
                <p className="text-[#9B9B9B] font-sora text-[14px] md:w-[414px]">
                  Get notified as soon as a condition is met.
                </p>
              </div>

              <div className="rounded-2xl flex flex-col gap-2 border-primary-neutral200 border-[1px] p-4">
                <div className="flex justify-between items-center">
                  <p className="text-primary-neutralt1 font-sora text-[16px] leading-[25.6px]">
                    Daily Summary
                  </p>
                  <input
                    type="radio"
                    name="dailySummary"
                    disabled={true}
                    className="w-5 h-5 cursor-not-allowed accent-primary-purple rounded-full"
                    checked={frequency === "Daily"}
                    onChange={() => handleFrequency("dailySummary")}
                  />
                </div>
                <p className="text-[#9B9B9B] font-sora text-[14px] md:w-[414px]">
                  Receive a daily report summarizing all alerts.
                </p>
              </div>

              <div className="rounded-2xl flex flex-col gap-2 border-primary-neutral200 border-[1px] p-4">
                <div className="flex justify-between items-center">
                  <p className="text-primary-neutralt1 font-sora text-[16px] leading-[25.6px]">
                    Weekly Summary
                  </p>
                  <input
                    type="radio"
                    name="weeklySummary"
                    disabled={true}
                    className="w-5 h-5 cursor-not-allowed accent-primary-purple rounded-full"
                    checked={frequency === "Weekly"}
                    onChange={() => handleFrequency("weeklySummary")}
                  />
                </div>
                <p className="text-[#9B9B9B] font-sora text-[14px] md:w-[414px]">
                  Receive weekly report summarizing all alerts.
                </p>
              </div>
            </div>
          </div>
          <div className="p-6 mt-auto">
            <button
              onClick={handleSave}
              disabled={frequency === 'Immediate'}
              className={`${
                frequencyMutate.isLoading || frequency === 'Immediate' ? "opacity-50 cursor-auto" : ""
              } w-full text-[#fcfcfc] bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl cursor-pointer flex-2 `}
            >
              {frequencyMutate.isLoading ? "Saving Changes" : "Save Changes"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FrequencyModal;
