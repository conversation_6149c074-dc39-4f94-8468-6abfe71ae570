// import { ArrowUp } from 'iconsax-react';
/* eslint-disable @typescript-eslint/no-explicit-any */

import InsightCard from '../../Constants/InsightCard';
import CustomerInsightTable from './CustomerInsightTable';
import InventoryInsightTable from './InventoryInsightTable';

const Insights = ({ data }: any) => {
  const totalOrderIncrease = data?.insights?.orderData?.totalOrderIncrease;
  const percentageIncrease = Math.round(totalOrderIncrease?.percentage * 100);
  const message = `
  Your total order increased by ${percentageIncrease}% from last month. 
  This growth highlights a positive trend in your sales performance. 
  ${totalOrderIncrease?.note}
`;
 

  return (
    <div>
      <InventoryInsightTable data={data} />
      <InsightCard
        text={
          <p className="text-[#7B7B7B] font-normal text-base max-w-[468px] w-full">
            <span className="font-semibold"> T-shirts</span> have been in stock
            for <span className="font-semibold">150 days</span>. Consider
            running a discount to clear aging stock
          </p>
        }
        buttonText="Create New Discount"
      />
      <div className="bg-[#F5F5F5] py-6 px-4 rounded-3xl mb-6 ">
        <p className="text-[#7B7B7B] font-normal text-base max-w-[468px] w-full">
          <span className="font-semibold">Men's Denim Jacket </span> is the{" "}
          <span className="font-semibold">fastest-moving item</span> with{" "}
          <span className="font-semibold">200 units </span> sold in{" "}
          <span className="font-semibold">24 days</span>
        </p>
        <button
          type="button"
          className="border border-[#4F4CD8] mt-6 py-3 max-w-[180px] w-full rounded-2xl text-sm font-semibold text-[#1A1A1A]"
        >
          Increase Stock
        </button>
        <p className="my-6 text-[#7B7B7B] font-normal text-base max-w-[468px] w-full">
          <span className="font-semibold"> T-shirts</span> are the{" "}
          <span className="font-semibold">slowest</span> with only{" "}
          <span className="font-semibold">50 units</span> sold in{" "}
          <span className="font-semibold">150 days</span>.
        </p>
        <div className="flex items-left sm:items-center gap-6 flex-col sm:flex-row ">
          <button
            type="button"
            className="border border-[#4F4CD8]  py-3 max-w-[180px] w-full rounded-2xl text-sm font-semibold text-[#1A1A1A]"
          >
            Boost Ads
          </button>
          <span>or</span>
          <button
            type="button"
            className="border border-[#4F4CD8] py-3 max-w-[180px] w-full rounded-2xl text-sm font-semibold text-[#1A1A1A]"
          >
            Create New Discount
          </button>
        </div>
      </div>
      <CustomerInsightTable data={data} />
      <h4 className="text-[#3A3A3A] font-semibold text-lg mb-4">Order Data</h4>
      <div className=" mb-6 ">
        {/* <div className="flex items-center">
          <p className="text-[#8B8B8B] font-normal text-base">
            Your <span className="font-semibold">total order</span> increased by{' '}
          </p>
          <p className="flex items-center rounded-2xl mx-1 text-[#28A745] text-[10px] p-0.5 border border-[#AEEBBC]">
            <span>${data?.insights?.orderData?.totalOrderIncrease}</span> <ArrowUp size="12" />
          </p>
          <p className="text-[#8B8B8B] font-normal text-base">
            from last month .This
          </p>
        </div>
        <p className="text-[#8B8B8B] font-normal text-base">
          growth highlights a positive trend in your sales performance.
        </p> */}
        {message}
      </div>
      <InsightCard
        text={
          <p className="text-[#7B7B7B] font-normal text-base max-w-[468px] w-full">
            With <span className="font-semibold">online orders</span> being your
            highest volume source, invest in improving your website and online
            marketing strategies to further capitalize on this trend.
          </p>
        }
        buttonText="Boost Online Ads"
      />
      <InsightCard
        text={
          <p className="text-[#7B7B7B] font-normal text-base max-w-[468px] w-full">
            <span className="font-semibold">In-store orders</span> being your
            lowest volume source, create discounts on items bought from the
            store to boost orders from in-store .
          </p>
        }
        buttonText="Create Discount"
      />
    </div>
  );
};
export default Insights;
