import { ReceiptDiscount, TicketDiscount, TicketExpired } from 'iconsax-react';
import PromoTable from './PromoTable';
import CreateNewPromo from './CreateNewPromo';
import { useState, useEffect } from 'react';
import { RouteNames } from '../../utils/RouteNames';
import { Link, useLocation } from 'react-router-dom';
import { orderServices } from './../../utils/Services/Order';
import { useUserAuthStore } from '../../store/auth';
import { useQuery } from 'react-query';
import CardLoader from '../../Constants/CardLoader';
import { FormatPrice } from '../../utils/FormatPrice';

const PromoSales = () => {
  useEffect(() => {
    document.title = 'Promo - Orders';
  }, []);

  const [newPromo, setNewPromo] = useState<boolean>(false);
  const user = useUserAuthStore((state) => state.user);

  const handlePromo = () => {
    setNewPromo((prev) => !prev);
  };

  const { data, isError, isLoading, refetch } = useQuery(
    ['getPromoData', user.vendorId],
    () => orderServices.getPromo(user.vendorId),
    {
      enabled: !!user.vendorId,
    }
  );

  

  const location = useLocation();
  const { data: details } = location.state || {}; // Fallback if state is em
  const { customerList = [], productList = [] } = details || {}; // Destructure and provide defaults

  const productStats = [
    {
      id: 1,
      title: 'Total Value Applied',
      value: data?.totalValueApplied,
      iconColor: '#4F4CD8',
      bgColor: '#E6E5F9',
      icon: ReceiptDiscount,
    },
    {
      id: 2,
      title: 'Total Active Promo',
      value: data?.totalActivePromo,
      iconColor: '#28A745',
      bgColor: '#EAF7ED',
      icon: TicketDiscount,
    },
    {
      id: 3,
      title: 'Total Promo Used',
      value: data?.totalPromoUsed,
      iconColor: '#FF9900',
      bgColor: '#FFF2DF',
      icon: ReceiptDiscount,
    },
    {
      id: 4,
      title: 'Upcoming Expirations',
      value: data?.upcomingExpirations,
      iconColor: '#dc3545',
      bgColor: '#FAF2F2',
      icon: TicketExpired,
    },
  ];
  return (
    <div className="md:mt-[70px] p-4 md:p-0">
      <div className="flex flex-col md:flex-row justify-between gap-2 my- font-sora ">
        <div className="">
          <h1 className="text-2xl font-bold m-0">
            Promo/Sales (Discounts & Coupons)
          </h1>
          <Link
            to={RouteNames.order}
            className="track-stock-subheader text-[#7b7b7b] text-sm cursor-pointer">
            Order Management &gt;&gt;{' '}
            <span className="text-[#4f4cd8]"> Promo/Sales</span>
          </Link>
        </div>
        <div className="flex items-center">
          <div className="w-full">
            <button
              onClick={handlePromo}
              className="border w-full justify-center border-gray-300 gap-2 rounded-2xl px-3 py-2 text-base flex items-center cursor-pointer bg-[#4f4cd8] text-primary-baseWhite font-semibold">
              <TicketDiscount size={16} /> Start New Promo/Sales
            </button>
          </div>
        </div>
      </div>

      <div className="my-8  flex lg:justify-between scrollbar-hide overflow-x-scroll md:overflow-hidden md:flex-wrap gap-2 lg:gap-y-0 lg:flex-nowrap">
        {isError ? (
          <div className="text-center w-full py-20">
            Sorry, An error occured fetching the cards. Kindly refresh the page
          </div>
        ) : (
          <>
            {productStats?.map((stat) => (
              <div key={stat.id} className=" flex-1 min-w-[256px]  ">
                {isLoading ? (
                  <CardLoader />
                ) : (
                  <div className="border-[1px] border-primary-neutral200 rounded-[16px] bg-primary-baseWhite p-6 relative">
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex items-center gap-1">
                        <div
                          className={`p-1 rounded-lg`}
                          style={{ backgroundColor: stat.bgColor }}>
                          {/* <Box size="15" color={stat.iconColor} variant="Bold" /> */}
                          <stat.icon color={stat.iconColor} variant="Bold" />
                        </div>
                        <span className="text-sm font-normal">
                          {stat.title}
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-start items-center mt-5">
                      <span className="text-2xl font-bold">
                        {FormatPrice(stat.value)}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </>
        )}
      </div>

      <div>
        <PromoTable
          customer={customerList}
          products={productList}
          data={data}
          refetchTable={refetch}
          isPromoTableLoading={isLoading}
          isPromoTableError={isError}
        />
      </div>
      {newPromo && (
        <CreateNewPromo
          customer={customerList}
          products={productList}
          refetch={refetch}
          closeModal={handlePromo}
          isOpen={newPromo}
        />
      )}
    </div>
  );
};

export default PromoSales;
