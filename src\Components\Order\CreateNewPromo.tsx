/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect, useRef } from "react";
import InputField from "../../Constants/InputField";
import {
  AddCircle,
  MinusCirlce,
  PercentageSquare,
  RefreshRightSquare,
} from "iconsax-react";
import Dropdown from "../../Constants/DropDown";
import DateInputPickerPromo from "../../Constants/DateInputPickerPromo";
import {  useMutation, useQuery } from "react-query";
import { orderServices } from "./../../utils/Services/Order";
import { useUserAuthStore } from "../../store/auth";
import { toast } from "react-toastify";
import { Notifications } from "../../utils/Services/Notification";

interface CreateOrderProps {
  closeModal: () => void;
  isOpen: boolean;
  refetch: any;
  customer: any;
  products:any
}
interface type {
  name: string;
}

interface PromoDetails {
  vendorId: string;
  promoCode: string;
  promoType: string;
  promoName: string;
  valueType: string;
  value: string;
  description: string;
  eligibleCustomersType: string;
  eligibleCustomers: string[];
  eligibleProductsType: string;
  eligibleProducts: string[];
  usageLimitType: string;
  usageLimit: any
;
  
}


const CreateNewPromo: React.FC<CreateOrderProps> = ({ closeModal, isOpen,refetch,customer,products }) => {


  const user = useUserAuthStore((state) => state.user);
  const PromoType: type[] = [{ name: "Discount" }, { name: "Coupon" }];
  const [activeTab, setActiveTab] = useState<string>("Discount");
  const [limit, setLimit] = useState<string>("Unlimited");
  const [couponType, setCouponType] = useState<string>(
    "Add Coupon to All Cart"
  );
  const [reportStartDate, setReportStartDate] = useState(new Date());
  const [reportEndDate, setReportEndDate] = useState(new Date());
  // const [reportStartDate, setReportStartDate] = useState<Date>(new Date());
  // const [reportEndDate, setReportEndDate] = useState<Date>(() => {
  //   const date = new Date();
  //   date.setDate(date.getDate() + 7); // Default to 1 week from today
  //   return date;
  // });
  const tabs = ["Unlimited", "Limited", "Per Customers"];
  const coupon = ["Add Coupon to All Cart", "Add Coupon Per Product"];
  const [perCustomer, setPerCustomer] =useState<number>(0)
  const [promo, setPromo] = useState<PromoDetails>({
    vendorId: "",
    promoCode: "",
    promoType: activeTab,
    promoName: "",
    valueType: "",
    value: "",
    description: "",
    eligibleCustomersType: '',
    eligibleCustomers: [],
    eligibleProductsType: '',
    eligibleProducts: [],
    usageLimitType: limit,
    usageLimit: null,
  });

  const createPromo = useMutation(orderServices.createPromo);
  const handleTab = (type: any) => {
    setPromo((prevState) => ({
      ...prevState,
      promoType: type,
    }));
    setActiveTab(type);
    setPromo((prevState) => ({
      ...prevState,
      eligibleCustomers: [],
      eligibleProducts:[]
    }))
  };
  const handleLimitTab = (tab: string) => {
    setLimit(tab);
    setPromo((prevState) => ({
      ...prevState,
      usageLimitType: tab, // Also update `promo.usageLimitType` with the same value
    }));
  };

  const handleCoupon = (tab: string) => {
    setCouponType(tab);
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;

    setPromo((prevState) => ({
      ...prevState,
      [name]: name === "usageLimit" ? parseInt(value) || 0 : value, // Parse usageLimit as an integer
    }));
  };

   const handleSelect = (value: string, name: string) => {
     setPromo((prevState) => {
       if (name === "eligibleCustomers") {
         const updatedCustomers = Array.isArray(prevState.eligibleCustomers)
           ? prevState.eligibleCustomers.includes(value)
             ? prevState.eligibleCustomers
             : [...prevState.eligibleCustomers, value]
           : [value];

         return {
           ...prevState,
           eligibleCustomers: updatedCustomers,
           eligibleCustomersType:
             updatedCustomers.length > 0 ? "Specific" : "All",
         };
       }

       if (name === "eligibleProducts") {
         const updatedProducts = Array.isArray(prevState.eligibleProducts)
           ? prevState.eligibleProducts.includes(value)
             ? prevState.eligibleProducts
             : [...prevState.eligibleProducts, value]
           : [value];

         return {
           ...prevState,
           eligibleProducts: updatedProducts,
           eligibleProductsType:
             updatedProducts.length > 0 ? "Specific" : "All",
         };
       }

       return {
         ...prevState,
         [name]: value,
       };
     });
   };

   const handleSelectCustomer = (customerName: string) => {
     if (customerName === "All Customers") {
       setPromo((prevState) => ({
         ...prevState,
         eligibleCustomersType: "All",
         eligibleCustomers: [],
       }));
     } else {
       const selectedCustomer = customer.find(
         (cust: any) => cust.customerName === customerName
       );
       if (selectedCustomer) {
         if (
           promo.eligibleCustomersType === "Specific" &&
           promo.eligibleCustomers.includes(selectedCustomer.customerId)
         ) {
           toast.error("Customer already selected!");
           return;
         }
         setPromo((prevState) => ({
           ...prevState,
           eligibleCustomersType: "Specific",
           eligibleCustomers: [
             ...prevState.eligibleCustomers,
             selectedCustomer.customerId,
           ],
         }));
       }
     }
   };

   const handleSelectProduct = (productName: string) => {
     if (productName === "All Products") {
       setPromo((prevState) => ({
         ...prevState,
         eligibleProductsType: "All",
         eligibleProducts: [],
       }));
     } else {
       const selectedProduct = products.find(
         (prod: any) => prod.productName === productName
       );
       if (selectedProduct) {
         if (
           promo.eligibleProductsType === "Specific" &&
           promo.eligibleProducts.includes(selectedProduct.productId)
         ) {
           toast.error("Product already selected!");
           return;
         }
         
         setPromo((prevState) => ({
           ...prevState,
           eligibleProductsType: "Specific",
           eligibleProducts: [
             ...prevState.eligibleProducts,
             selectedProduct.productId,
           ],
         }));
       }
     }
   };

   const handleRemoveCustomer = (idToRemove: string) => {
     setPromo((prevState) => {
       const updatedCustomers = prevState.eligibleCustomers.filter(
         (id) => id !== idToRemove
       );
       return {
         ...prevState,
         eligibleCustomers: updatedCustomers,
         eligibleCustomersType:
           updatedCustomers.length > 0 ? "Specific" : "All",
       };
     });
  };
   const handleRemoveProduct = (idToRemove: string) => {
     setPromo((prevState) => {
       const updatedProducts = prevState.eligibleProducts.filter(
         (id) => id !== idToRemove
       );
       return {
         ...prevState,
         eligibleProducts: updatedProducts,
         eligibleProductsType: updatedProducts.length > 0 ? "Specific" : "All",
       };
     });
   };



  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    return () => {
      document.body.classList.remove("no-scroll");
    };
  }, [isOpen]);
   const { refetch: notificationRefetch } = useQuery(
     ["notifications", user.vendorId],
     () => Notifications.getNotification(user.vendorId),
     {
       enabled: !!user.vendorId,
       onSuccess: () => {},
       onError: (error: any) => {
         console.log("notification error:", error);
       },
     }
   );

  const handleSubmit = async () => {
    const payload = {
      vendorId: user.vendorId,
      promoCode: promo.promoCode,
      promoType: promo.promoType,
      promoName: promo.promoName,
      valueType: promo.valueType,
      value: promo.value,
      description: promo.description,
      startDate: reportStartDate.toISOString().split("T")[0],
      endDate: reportEndDate.toISOString().split("T")[0],
      eligibleCustomersType: promo.eligibleCustomers.length > 0  ? 'Specific' : 'All',
      eligibleCustomers: promo.eligibleCustomers,
      eligibleProductsType: promo.eligibleProducts.length > 0 ? 'Specific' :  couponType === 'Add Coupon to All Cart' ? 'All' : 'Specific',
      eligibleProducts: promo.eligibleProducts,
      usageLimitType: promo.usageLimitType,
      usageLimit:
        promo.usageLimitType === "Unlimited"
          ? null // Convert to empty string for unlimited cases
          : promo.usageLimit !== null
          ? promo.usageLimit.toString() // Convert number to string
          : "0", // Fallback to "0" if usageLimit is null
    };
    try {
      createPromo.mutate(payload, {
        onSuccess: (response) => {
          toast.success(response?.message);
          notificationRefetch();
          closeModal();
          refetch();
        },

        onError: (error: any) => {
          toast.error(error?.response?.data?.error || error?.message);
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  const modalRef = useRef<HTMLDivElement | null>(null);
  // Focus on the modal when it opens
  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen]);
  // Function to remove a name from the list
 

  return (
    <div
      className="fixed inset-0 z-50 font-sora bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm transition-transform duration-500 ease-in-out"
      ref={modalRef}
      tabIndex={-1}
      onKeyDown={(e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
        }
      }}>
      <div
        className={`fixed top-0 right-0 h-full md:w-[580px] bg-white z-30 p-4 md:p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
        <div>
          <span
            onClick={closeModal}
            className="text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]">
            &times;
          </span>

          {/* Ensure this div takes full height minus the close button */}
          <div className="h-[calc(100vh-80px)]  md:pl-6 flex flex-col gap-4 overflow-y-auto scrollbar-none hidescroll">
            <div className="mb-[15px]">
              <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold">
                Start New Promo/Sales
              </h2>
              <p className="text-sm text-[#919191] font-sora">
                Create irresistible offers to get more customers and increased
                sales
              </p>
            </div>
            <div className="flex flex-col gap-2 w-full">
              <p className="text-[14px] text-primary-neutralt2 font-sora">
                Type
              </p>
              <div className="w-full p-1 border-[1px] border-primary-neutral300 rounded-3xl">
                <div className="flex justify-between gap-3 items-center">
                  {PromoType.map((type) => (
                    <div className="w-full" key={type.name}>
                      {' '}
                      {/* Add unique key prop */}
                      <button
                        onClick={() => handleTab(type.name)}
                        className={`p-2 rounded-3xl flex-1 w-full font-sora text-center ${
                          activeTab === type.name
                            ? type.name === 'Discount'
                              ? 'bg-[#E6E5F9] text-primary-purple'
                              : ' bg-[#FFF2DF] text-[#FF9900]'
                            : 'text-primary-neutralt1'
                        }`}>
                        {type.name}
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-2 w-full">
              <p className="text-[14px] text-primary-neutralt2 font-sora">
                Name
              </p>
              <InputField
                id="promo-name"
                placeholder="Enter Promo/Sales name"
                type="text"
                value={promo.promoName}
                onChange={handleChange}
                name="promoName"
              />
            </div>
            <div className="flex flex-col gap-2 w-full">
              <p className="text-[14px] text-primary-neutralt2 font-sora">
                Code
              </p>
              <div className="relative">
                <InputField
                  id="promo-name"
                  value={promo.promoCode}
                  placeholder="Enter preferred Promo/Sales code"
                  onChange={handleChange}
                  name="promoCode"
                />
                <RefreshRightSquare
                  variant="Bold"
                  className="absolute top-3 right-5 text-primary-purple"
                />
              </div>
            </div>
            <div className="flex flex-col md:flex-row md:justify-between items-center gap-2 w-full">
              <div className="flex w-full flex-col gap-2">
                <p className="text-[14px] text-primary-neutralt2 font-sora">
                  Value Type
                </p>
                <Dropdown
                  // onSelect={handleSelect}
                  onSelect={(value) => handleSelect(value, 'valueType')}
                  options={['Percentage', 'Amount']}
                  value={promo.valueType}
                />
              </div>
              <div className="flex flex-col w-full">
                <p className="text-[14px] text-primary-neutralt2 font-sora">
                  Value {promo.valueType}
                </p>
                <div className="relative">
                  <InputField
                    id="promo-name"
                    placeholder={`Enter a ${promo.valueType}`}
                    onChange={handleChange}
                    value={promo.value}
                    name="value"
                  />
                  {promo.valueType == 'Percentage' &&(
                    <PercentageSquare
                    variant="Bold"
                    className="absolute top-3 right-5 text-primary-purple"
                  />
                  )}
                </div>
              </div>
            </div>
            <div className="p-0 flex flex-col gap-2 relative">
              <p className="text-sm font-sans font-normal text-[#5b5b5b]">
                Description (Optional)
              </p>
              <textarea
                rows={12}
                className=" text-[#7b7b7b] text-xs p-4 border border-[#cccccc] rounded-2xl outline-none"
                placeholder="Tell your customer why you are running this promo "
                id="promoDescription"
                onChange={handleChange}
                value={promo.description}
                name="description"></textarea>
              {/* <div className="absolute bottom-2 right-2 mt-5">
                <button className="flex items-center bg-[#e6e5f9] p-2.5 gap-2.5 text-[#4f4cd8] rounded-2xl font-semibold cursor-pointer border border-[#4f4cd8]">
                  <img src={aiStar} alt="ai" />
                  <span>Write with AI </span>
                </button>
              </div> */}
            </div>
            <div className="flex-col w-full flex md:flex-row md:justify-between items-center gap-2">
              <div className="p-0 flex w-full flex-col gap-2.5 relative last:border-b-0 ">
                <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                  Valid From
                </p>{' '}
                <DateInputPickerPromo
                  selectedDate={reportStartDate}
                  onDateChange={setReportStartDate}
                />
              </div>
              <div className="p-0 w-full flex flex-col gap-2.5 relative last:border-b-0  ">
                <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                  Valid Till
                </p>
                <DateInputPickerPromo
                  selectedDate={reportEndDate}
                  onDateChange={setReportEndDate}
                />
              </div>
            </div>
          
            {activeTab === 'Discount' && (
              <div className="flex flex-col gap-6">
                <div className="flex flex-col gap-2 w-full">
                  <p className="text-[14px] text-primary-neutralt2 font-sora">
                    Eligible Customers
                  </p>
                  <Dropdown
                    onSelect={(value) => handleSelectCustomer(value)}
                    options={[
                      ...customer.map((cust: any) => cust.customerName),
                      'All Customers',
                    ]}
                    placeholder="Select Eligible Customers"
                    value={''}
                  />

                  {promo?.eligibleCustomers.length > 0 && (
                    <ul className="mt-4 space-y-2">
                      {promo.eligibleCustomers.map((customerId, index) => {
                        const customerData = customer.find(
                          (cust: any) => cust.customerId === customerId
                        );

                        return (
                          <li
                            key={index}
                            className="flex justify-between items-center bg-gray-100 px-4 py-2 rounded-lg shadow-sm">
                            <span className="text-gray-700">
                              {customerData?.customerName || 'Unknown'}
                            </span>
                            <button
                              onClick={() => handleRemoveCustomer(customerId)}
                              className="text-red-500 hover:text-red-700 font-bold">
                              X
                            </button>
                          </li>
                        );
                      })}
                    </ul>
                  )}
                </div>
                <div className="flex flex-col gap-2 w-full">
                  <p className="text-[14px] text-primary-neutralt2 font-sora">
                    Eligible Products
                  </p>
                  <Dropdown
                    onSelect={(value) => handleSelectProduct(value)}
                    options={[
                      ...products.map((product: any) => product.productName),
                      'All Products',
                    ]}
                    placeholder="Select Products"
                    value={''}
                  />
                  {promo?.eligibleProducts.length > 0 && (
                    <ul className="mt-4 space-y-2">
                      {promo.eligibleProducts.map((productId, index) => {
                        const productData = products.find(
                          (cust: any) => cust.productId === productId
                        );

                        return (
                          <li
                            key={index}
                            className="flex justify-between items-center bg-gray-100 px-4 py-2 rounded-lg shadow-sm">
                            <span className="text-gray-700">
                              {productData?.productName || 'Unknown'}
                            </span>
                            <button
                              onClick={() => handleRemoveProduct(productId)}
                              className="text-red-500 hover:text-red-700 font-bold">
                              X
                            </button>
                          </li>
                        );
                      })}
                    </ul>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'Coupon' && (
              <div className="flex flex-col gap-6">
                <div className="flex flex-col gap-2">
                  <p className="text-[14px] text-primary-neutralt2 font-sora">
                    Type
                  </p>
                  <div className="w-full p-1 border-[1px] border-primary-neutral300 rounded-3xl">
                    <div className="flex justify-between gap-3 items-center">
                      {coupon.map((type) => (
                        <div className="w-full" key={type}>
                          {' '}
                          {/* Add unique key prop */}
                          <button
                            onClick={() => handleCoupon(type)}
                            className={`p-2 rounded-3xl flex-1 w-full font-sora text-center ${
                              couponType === type
                                ? type === 'Add Coupon to All Cart'
                                  ? 'bg-[#EAF7ED] text-[#28A745]'
                                  : ' bg-[#FAF2F2] text-[#DC3545]'
                                : 'text-primary-neutralt1'
                            }`}>
                            {type}
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                {couponType === 'Add Coupon to All Cart' ? (
                  <div>
                    <div className="flex flex-col gap-2 w-full">
                      <p className="text-[14px] text-primary-neutralt2 font-sora">
                        Minimum Amount for Cart (Optional)
                      </p>
                      <InputField
                        id="minimum-value"
                        placeholder="Specify minimum amount for coupon to be valid"
                      />
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="flex flex-col gap-2 w-full">
                      <p className="text-[14px] text-primary-neutralt2 font-sora">
                        Eligible Products
                      </p>
                      <Dropdown
                        onSelect={(value) => handleSelectProduct(value)}
                        options={[
                          ...products.map(
                            (product: any) => product.productName
                          ),
                        ]}
                        placeholder="Select Eligible Products"
                        value={''}
                      />
                      {promo?.eligibleProducts.length > 0 && (
                        <ul className="mt-4 space-y-2">
                          {promo.eligibleProducts.map((productId, index) => {
                            const productData = products.find(
                              (cust: any) => cust.productId === productId
                            );

                            return (
                              <li
                                key={index}
                                className="flex justify-between items-center bg-gray-100 px-4 py-2 rounded-lg shadow-sm">
                                <span className="text-gray-700">
                                  {productData?.productName || 'Unknown'}
                                </span>
                                <button
                                  onClick={() => handleRemoveProduct(productId)}
                                  className="text-red-500 hover:text-red-700 font-bold">
                                  X
                                </button>
                              </li>
                            );
                          })}
                        </ul>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="flex flex-col gap-2 w-full">
              <p className="text-[14px] text-primary-neutralt2 font-sora">
                Usage Limit
              </p>
              <div className="w-full flex gap-2 p-1 border rounded-3xl justify-between">
                {tabs.map((tab) => (
                  <button
                    key={tab}
                    className={`flex-1 px-1 md:px-4 py-1 text-[12px] md:text-[14px] md:py-2 text-center rounded-full transition ${
                      limit === tab
                        ? tab === 'Unlimited'
                          ? 'bg-[#FAF2F2] text-[#DC3545] font-semibold'
                          : tab === 'Limited'
                          ? 'bg-[#FFF2DF] text-[#FF9900] font-semibold'
                          : 'bg-[#EAF7ED] text-[#28A745] font-semibold'
                        : 'text-primary-neutralt2 bg-primary-baseWhite'
                    }`}
                    onClick={() => handleLimitTab(tab)}>
                    {tab}
                  </button>
                ))}
              </div>
            </div>
            {limit === 'Limited' && (
              <div className="flex flex-col gap-2 w-full">
                <p className="text-[14px] text-primary-neutralt2 font-sora">
                  Set Limits
                </p>
                <InputField
                  id="limitedPromo "
                  onChange={handleChange}
                  placeholder="Enter maximum number of limits"
                  value={promo.usageLimit || ''}
                  name="usageLimit"
                  type="number"
                />
              </div>
            )}
            {limit === 'Per Customers' && (
              <div className="flex flex-col gap-2 w-full">
                <p className="text-[14px] text-primary-neutralt2 font-sora">
                  Set Limit Per Customer
                </p>
                <div className="flex w-full  justify-between p-2 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                  <MinusCirlce
                    onClick={() =>
                      setPerCustomer((prev: number) =>
                        prev > 0 ? prev - 1 : prev
                      )
                    }
                    className={`cursor-pointer ${
                      perCustomer > 0
                        ? 'text-primary-neutralt1'
                        : 'text-primary-neutral300'
                    }`}
                  />
                  <p className="text-[14px] font-sora text-primary-neutralt1">
                    {perCustomer}
                  </p>
                  <AddCircle
                    onClick={() => setPerCustomer((prev: any) => prev + 1)}
                    className="cursor-pointer text-primary-neutralt1"
                  />
                </div>
              </div>
            )}

            <div className="mb-[150px]"></div>
          </div>

          {/* Bottom Buttons */}
          <div className="fixed bottom-0  left-0 w-full flex justify-around z-50 bg-white py-2.5 p-3 md:p-10">
            <div className="flex w-full gap-2.5">
              <button
                onClick={closeModal}
                className="bg-transparent text-[#2a2a2a] text-sm font-semibold border px-12 h-[49px] rounded-2xl">
                Back
              </button>
              <button
                onClick={handleSubmit}
                disabled={createPromo.isLoading}
                className={`${
                  createPromo.isLoading ? 'opacity-50 cursor-auto' : ''
                } w-full text-[#fcfcfc] bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl cursor-pointer flex-2 `}>
                {createPromo.isLoading ? 'Submitting' : 'Submit'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateNewPromo;
