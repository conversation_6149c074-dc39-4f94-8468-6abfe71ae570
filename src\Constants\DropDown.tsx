import { ArrowDown2 } from "iconsax-react";
import { useState, useRef, useEffect } from "react";

interface DropdownProps {
  options: string[];
  onSelect: (value: string) => void;
  placeholder?: string;
  border?: string;
  borderBg?: string;
  value?: string | string[];
  refetchDelivery?: () => void;
}

const Dropdown: React.FC<DropdownProps> = ({
  options,
  border,
  borderBg,
  onSelect,
  placeholder,
  value,
  refetchDelivery,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<string | null>(
    value ? (value as string) : null
  );
  const [openUpwards, setOpenUpwards] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLUListElement>(null);
  const lastSearchKey = useRef<string | null>(null);
  const lastSearchIndex = useRef<number>(-1);

  const handleOverlayClick = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleOverlayClick);
    return () => {
      document.removeEventListener("mousedown", handleOverlayClick);
    };
  }, []);

  const handleSelect = (option: string) => {
    setSelectedOption(option);
    onSelect(option);
    setIsOpen(false);
  };

  const toggleDropdown = () => {
    if (!dropdownRef.current) return;

    const dropdownRect = dropdownRef.current.getBoundingClientRect();
    const windowHeight = window.innerHeight;

    if (dropdownRect.bottom + 350 > windowHeight) {
      setOpenUpwards(true);
    } else {
      setOpenUpwards(false);
    }

    setIsOpen(!isOpen);
    if (refetchDelivery) refetchDelivery();
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    if (!isOpen) return;

    const key = event.key.toLowerCase();
    if (key.length === 1 && key.match(/[a-z]/)) {
      const matchingIndexes = options
        .map((option, index) => (option.toLowerCase().startsWith(key) ? index : -1))
        .filter((index) => index !== -1);

      if (matchingIndexes.length > 0) {
        if (lastSearchKey.current === key) {
          // Move to the next matching option
          const currentIndex = lastSearchIndex.current;
          const nextIndex =
            matchingIndexes.find((index) => index > currentIndex) ?? matchingIndexes[0];

          lastSearchIndex.current = nextIndex;
        } else {
          // Reset to first match when a new key is pressed
          lastSearchKey.current = key;
          lastSearchIndex.current = matchingIndexes[0];
        }

        if (listRef.current) {
          const listItems = listRef.current.children as HTMLCollectionOf<HTMLLIElement>;
          if (listItems[lastSearchIndex.current]) {
            listItems[lastSearchIndex.current].scrollIntoView({
              behavior: "smooth",
              block: "nearest",
            });
          }
        }
      }
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
    } else {
      document.removeEventListener("keydown", handleKeyDown);
      lastSearchKey.current = null;
      lastSearchIndex.current = -1; // Reset when closing dropdown
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen]);

  return (
    <div className="relative font-sora" ref={dropdownRef}>
      <button
        type="button"
        className={`w-full ${border} ${borderBg} flex items-center justify-between text-[#5b5b5b] text-sm font-sora font-normal h-[48px] px-4 border border-[#cccccc] rounded-2xl`}
        onClick={toggleDropdown}
      >
        <span className="text-left">
          {selectedOption || placeholder || "Select an option"}
        </span>
        <ArrowDown2 size={20} color="#ABABAB" />
      </button>
      {isOpen && (
        <ul
          ref={listRef}
          className={`absolute text-sm w-full bg-white border rounded shadow-lg z-10 overflow-auto h-[200px] md:max-h-[200px] ${
            openUpwards ? "bottom-full mb-2" : "top-full mt-2"
          }`}
        >
          {options.map((option) => (
            <li
              key={option}
              className="p-2 cursor-pointer hover:bg-gray-100"
              onClick={() => handleSelect(option)}
            >
              {option}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default Dropdown;
