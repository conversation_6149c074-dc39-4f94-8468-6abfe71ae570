/* eslint-disable @typescript-eslint/no-explicit-any */

import Dropdown from '../../Constants/DropDown';
import DateInputPicker from '../../Constants/DateInputPicker';
import { useState, useEffect } from 'react';
import InputField from '../../Constants/InputField';
import AddCustomer from './AddCustomer';
import AddProduct from '../Inventory/AddProduct';

import {
  AddCircle,
  ArrowDown2,
  ArrowUp2,
  Minus<PERSON><PERSON><PERSON>ce,
  Trash,
} from 'iconsax-react';
import { FormatPrice } from '../../utils/FormatPrice';
import { useQuery } from 'react-query';
import { inventoryServices } from './../../utils/Services/InventoryServices';
import { useUserAuthStore } from '../../store/auth';
import DeliveryCost from './DeliveryCost';
import { orderServices } from './../../utils/Services/Order';
import { toast } from 'react-toastify';
import { useMutation } from 'react-query';
import { Notifications } from '../../utils/Services/Notification';

interface CreateOrderProps {
  closeModal: () => void;
  isOpen: boolean;
  order: any;
  orderDetailsRefetch: any;
  refetch: any;
}

interface OrderDetails {
  vendorId: string;
  orderId: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  cartProducts: any[];
  vendorEmail: string;
  billingAddress: string;
  billingCity: string;
  billingState: string;
  billingZipCode: string;
  country: string;
  paymentMethod: string;
  channel: string;
  promoCode: string;
  deliveryFee: any;
  orderDate: Date;
  paymentStatus: string;
  amountPaid: number;
  isVendorCreated: 'No';
  saveAddress: string;
  socialMediaHandle: string;
  firstName: string;
  lastName: string;
  orderNumber: any;
  subTotalFee: any;
}

const EditOrder: React.FC<CreateOrderProps> = ({
  closeModal,
  isOpen,
  order,
  orderDetailsRefetch,
  refetch,
}) => {
  const user = useUserAuthStore((state) => state.user);
  const [addCustomerPanel, setAddCustomerPanel] = useState<boolean>(false);
  const [addDeliveryPanel, setAddDeliveryPanel] = useState<boolean>(false);
  const [addProduct, setAddProduct] = useState<boolean>(false);
  const [isMoreDetails, setIsMoreDetails] = useState<number | null>(null);
  const tabs = ['Not Yet Paid', 'Paid', 'Installment'];
  const [allData, setAllData] = useState<any>([]);
  const [deliveryFeeData, setDeliveryFeeData] = useState<any[]>([]);
  const [orderDetail, setOrderDetail] = useState<OrderDetails>({
    vendorId: user.vendorId,
    orderId: '',
    vendorEmail: user.userEmail,
    customerId: '',
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    cartProducts: [],
    billingCity: '',
    billingAddress: '',
    billingState: '',
    billingZipCode: '',
    country: '',
    paymentMethod: order?.paymentMethod,
    channel: '',
    promoCode: '',
    deliveryFee: order?.deliveryFee,
    orderDate: new Date(order?.orderDate),
    paymentStatus: '',
    amountPaid: 0,
    isVendorCreated: 'No',
    saveAddress: '',
    socialMediaHandle: '',
    firstName: '',
    lastName: '',
    orderNumber: '',
    subTotalFee: 0,
  });
  const [activeTab, setActiveTab] = useState(
    order?.paymentStatus || 'Not Yet Paid'
  );
  const [editedPrices, setEditedPrices] = useState<any>({});

  const handlePriceChange = (productId:any, value:any) => {
    setEditedPrices((prev:any) => ({
      ...prev,
      [productId]: value === '' ? '' : Number(value),
    }));
  };

  const [quantities, setQuantities] = useState<{ [key: string]: number }>(
    () =>
      orderDetail?.cartProducts.reduce(
        (acc: { [key: string]: number }, product: any) => {
          acc[product.productId] = product.quantity; // Use productId for better alignment
          return acc;
        },
        {}
      ) || {}
  );
  useEffect(() => {
    // Update orderDetail state with values from `order` if available
    if (order && order?.channel) {
      setOrderDetail((prev) => ({
        ...prev,
        channel: order.channel || prev.channel,
        customerId: order.customerId || prev.customerId,
        orderId: order.orderId || prev.orderId,
        customerName: order.customerName || prev.customerName,
        customerEmail: order.customerEmail || prev.customerEmail,
        customerPhone: order.customerPhone || prev.customerPhone,
        cartProducts: order.orderSummary || prev.cartProducts, // Assuming orderSummary contains the products
        billingCity: order.billingCity || prev.billingCity,
        billingAddress: order.billingAddress || prev.billingAddress,
        billingState: order.billingState || prev.billingState,
        billingZipCode: order.billingZipCode || prev.billingZipCode,
        country: order.country || prev.country,
        paymentMethod: order.paymentMethod || prev.paymentMethod,
        amountPaid: order.amountPaid || prev.amountPaid,
        paymentStatus: order.paymentStatus || prev.paymentStatus,
        orderNumber: order.orderId || prev.orderId,
        deliveryFee: order.deliveryFee || prev.deliveryFee,
        firstName: order.firstName || prev.firstName,
        lastName: order.lastName || prev.lastName,
        subTotalFee: order.subTotalFee || prev.subTotalFee,
      }));
    }
  }, [order]);

 useEffect(() => {
   if (orderDetail.cartProducts?.length > 0) {
     const newSubTotalFee = orderDetail.cartProducts.reduce((acc, product) => {
       const quantity = quantities[product.productId] ?? product.quantity;
       const effectivePrice =
         editedPrices[product.productId] !== undefined
           ? editedPrices[product.productId]
           : product.discountedPrice;
       return acc + effectivePrice * quantity;
     }, 0);
     setOrderDetail((prev) => ({
       ...prev,
       subTotalFee: FormatPrice(newSubTotalFee),
     }));
   } else {
     setOrderDetail((prev) => ({
       ...prev,
       subTotalFee: 0,
     }));
   }
 }, [orderDetail.cartProducts, quantities, editedPrices]);

  const selectedDelivery = deliveryFeeData.find(
    (d: any) => d.deliveryValue === orderDetail?.deliveryFee
  );

  const editOrder = useMutation(orderServices.editOrder);
  const { mutateAsync } = editOrder;

  // Manage scroll lock when any modal is open
  useEffect(() => {
    if (isOpen || addCustomerPanel || addDeliveryPanel) {
      document.body.classList.add('no-scroll');
    } else {
      document.body.classList.remove('no-scroll');
    }

    return () => {
      document.body.classList.remove('no-scroll');
    };
  }, [isOpen, addCustomerPanel, addDeliveryPanel]);

  // Function to open AddCustomer modal
  const handleCustomer = () => {
    setAddCustomerPanel(true);
  };

  const handleAddProduct = () => {
    setAddProduct(true);
  };
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setOrderDetail((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectProduct = (product: any) => {
    const selectedProduct = allData.find((p: any) => p.productName === product);
    if (!selectedProduct) return;
    if (selectedProduct.status === 'Out of Stock') {
      toast.error('This product is out of stock and cannot be picked.');
      return;
    }
    setOrderDetail((prev) => {
      const existingProds = [...prev.cartProducts];

      const productExists = existingProds.some(
        (p: any) => p.productName === selectedProduct.productName
      );

      if (productExists) {
        console.log('Product already exists in the cart.');
        return prev;
      }
      const updatedProducts = [
        ...existingProds,
        {
          productId: selectedProduct.productId,
          productName: selectedProduct.productName,
          quantity: 1,
          discountAmount: selectedProduct.discountAmount ?? 0,
          discount: selectedProduct.discountPercentage ?? 0,
          discountedPrice: selectedProduct.discountedPrice ?? 0, // Default to 0 if undefined
          sellingPrice: selectedProduct.sellingPrice ?? 0,
          productImage: selectedProduct.productImage || '',
        },
      ];

      return {
        ...prev,
        cartProducts: updatedProducts,
      };
    });
  };

  const handleDelivery = () => {
    setAddDeliveryPanel(true);
  };

  const closeCustomerPanel = () => {
    setAddCustomerPanel(false);
    setAddDeliveryPanel(false);
  };

  const closeAddProduct = () => {
    setAddProduct(false);
  };

  // show order details
  const handleMoreDetails = (id: number) => {
    setIsMoreDetails((prevId) => (prevId === id ? null : id));
  };

  // Other CreateOrder logic
  const handleSelect = (value: string) => {
    setOrderDetail((prev) => ({
      ...prev,
      paymentMethod: value,
    }));
    console.log('Selected:', value);
  };

  const handleChannelSelect = (value: string) => {
    setOrderDetail((prev) => ({
      ...prev,
      channel: value,
    }));
  };

  const handlePaid = (value: string) => {
    setOrderDetail((prev) => ({
      ...prev,
      paymentMethod: value,
    }));
  };

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
    setOrderDetail((prev) => ({
      ...prev,
      paymentStatus: tab,
    }));
  };

  // Add useEffect to log the updated state
  useEffect(() => {
    console.log('Updated paymentStatus:', orderDetail.paymentStatus);
  }, [orderDetail.paymentStatus]);

  const { data } = useQuery(
    ['products', user.vendorId], // Unique query key
    () => inventoryServices.getAllProducts(user.vendorId),
    {
      enabled: !!user.vendorId,
    }
  );
  useEffect(() => {
    if (data) {
      setAllData(data.products || []);
      console.log(data); // Log the fetched data
    }
  }, [data]);

  const { data: fee } = useQuery(
    ['deliveryFee', user.vendorId],
    () => orderServices.getDeliveryFee(user.vendorId),
    {
      enabled: !!user.vendorId,
    }
  );

  useEffect(() => {
    if (fee) {
      console.log('Fetched delivery fees:', fee);
      setDeliveryFeeData(fee.deliveryFees || []);
    }
  }, [fee]);

  // const handleCustomerSelect = (customerData: any) => {
  //   console.log(customerData);

  //   // Split customerName into firstName and lastName
  //   const [firstName, ...lastNameArray] = customerData.customerName.split(" ");
  //   const lastName = lastNameArray.join(" ");

  //   const shippingAddresses = customerData.shippingAddresses.map(
  //     (address: any) => ({
  //       shippingCity: address.shippingCity,
  //       shippingState: address.shippingState,
  //       shippingZipCode: address.shippingZipCode,
  //       shippingAddress: address.shippingAddress,
  //     })
  //   );
  //   console.log(shippingAddresses);

  //   setOrderDetail((prev) => ({
  //     ...prev,
  //     customerId: customerData.customerId,
  //     customerName: customerData.customerName,
  //     customerEmail: customerData.emailAddress,
  //     customerPhone: customerData.phoneNumber,
  //     billingAddress: customerData.address,
  //     country: customerData.country,
  //     billingZipCode: customerData.zipCode,
  //     billingState: customerData.state,
  //     billingCity: customerData.city,
  //     shippingAddresses,
  //     firstName,
  //     lastName,
  //   }));
  // };

  const handleDeliveryChange = (data: string) => {
    const selected: any = deliveryFeeData.find(
      (x: any) => x.deliveryTitle + ' - ' + x.deliveryValue === data
    );
    setOrderDetail((prev) => ({
      ...prev,
      deliveryFee: selected.deliveryValue,
    }));
  };

  const handleIncreaseQuantity = (productId: string) => {
    setQuantities((prevQuantities) => ({
      ...prevQuantities,
      [productId]:
        (prevQuantities[productId] ??
          (orderDetail.cartProducts.find((p) => p.productId === productId)
            ?.quantity ||
            0)) + 1,
    }));
  };

  const handleDecreaseQuantity = (productId: string) => {
    setQuantities((prevQuantities) => {
      const currentQuantity =
        prevQuantities[productId] ??
        (orderDetail.cartProducts.find((p) => p.productId === productId)
          ?.quantity ||
          0);

      if (currentQuantity <= 1) {
        handleDeleteProduct(productId);
        return prevQuantities;
      }

      return {
        ...prevQuantities,
        [productId]: currentQuantity - 1,
      };
    });
  };
  const handleDeleteProduct = (productId: string) => {
    setQuantities((prevQuantities) => {
      const updatedQuantities = { ...prevQuantities };
      delete updatedQuantities[productId]; // Remove product from quantities
      return updatedQuantities;
    });

    setOrderDetail((prevOrderDetails) => ({
      ...prevOrderDetails,
      cartProducts: prevOrderDetails.cartProducts.filter(
        (product: any) => product.productId !== productId
      ),
    }));
  };

  const subTotal = Number(
    String(orderDetail.subTotalFee || 0).replace(/,/g, '')
  );
  const deliveryFee = Number(
    String(orderDetail.deliveryFee || 0).replace(/,/g, '')
  );

  const { refetch: notificationRefetch } = useQuery(
    ['notifications', user.vendorId],
    () => Notifications.getNotification(user.vendorId),
    {
      enabled: !!user.vendorId,
      onSuccess: () => {},
      onError: (error: any) => {
        console.log('notification error:', error);
      },
    }
  );

  const handleSubmit = async () => {
    const transformedCartProducts = orderDetail.cartProducts.map(
      (product: any) => ({
        product_id: product.productId,
        quantity: quantities[product.productId] ?? product.quantity,
        real_price: product.sellingPrice,
        discount:
          product.discount ||
          Math.round(
            ((product.sellingPrice -
              (editedPrices[product.productId] || product.discountedPrice)) /
              product.sellingPrice) *
              100
          ),
        amountAfterDiscount:
          editedPrices[product.productId] || product.discountedPrice,
      })
    );

    const payload = {
      ...orderDetail,
      cartProducts: transformedCartProducts,
      orderDate: new Date(orderDetail.orderDate),
      paymentMethod:
        orderDetail.paymentStatus === 'Not Yet Paid'
          ? ''
          : orderDetail.paymentMethod,
    };

    console.log('payload', payload);

    try {
      const response: any = await mutateAsync(payload);
      orderDetailsRefetch();
      refetch();
      toast.success(response.message);
      notificationRefetch();
      closeModal();
    } catch (error) {
      console.error('Error during order creation:', error);
      toast.error('Failed to create order. Please try again.');
    }
  };

  return (
    <div className="fixed inset-0 z-50 font-sora bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white z-30 p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
        {/* Close button for CreateOrder */}
        <div>
          <span
            onClick={closeModal}
            className="text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]">
            &times;
          </span>

          {/* Ensure this div takes full height minus the close button */}
          <div className="h-[calc(100vh-80px)] overflow-y-auto scrollbar-none hidescroll">
            <div className="pl-6">
              <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold">
                Edit Order
              </h2>
              <p className="text-sm text-[#919191] font-sora">
                Provide the Information below to create an order
              </p>
            </div>

            {/* Customer Section */}
            <div className="p-6 rounded-2xl flex flex-col gap-3">
              <div className="w-full">
                <div>
                  <p className="block text-primary-neutralt1 font-sora font-semibold text-[16px] mb-6">
                    Who wants to buy?
                  </p>
                  <InputField
                    label="Name"
                    value={orderDetail.customerName}
                    placeholder="Enter Name"
                    readOnly
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <p className="md:text-[14px] text-[12px] text-primary-neutralt2">
                  Is the buyer a new customer?
                </p>
                <button
                  onClick={handleCustomer}
                  className="text-primary-purple text-[12px] md:text-[14px] font-semibold">
                  + Add New Customer
                </button>
              </div>
            </div>

            {/* Date Section */}
            <div className="p-6 flex flex-col gap-6">
              <h4 className="font-semibold font-sora text-base">
                When are they buying?
              </h4>
              <div className="flex flex-col gap-2.5">
                <p className="text-[14px] font-sora text-[#5b5b5b]">Date</p>
                <DateInputPicker
                  selectedDate={orderDetail?.orderDate || new Date()}
                  onDateChange={(newDate) =>
                    setOrderDetail({ ...orderDetail, orderDate: newDate })
                  }
                />
              </div>
            </div>

            {/* Product Section */}
            <h4 className="font-semibold font-sora text-base p-6">
              What are they buying?
            </h4>
            <div className="p-6 flex bg-[#F5F5F5] rounded-2xl flex-col gap-3">
              <p className="font-sora text-[14px] text-primary-neutralt2">
                Product
              </p>
              <Dropdown
                onSelect={handleSelectProduct}
                options={
                  allData.map((product: any) => product.productName) || []
                }
                placeholder="Select product from your inventory"
                border="bg-[#EAF7ED]"
              />
              <div className="flex items-center justify-between">
                <p className="md:text-[14px] text-[12px] text-primary-neutralt2">
                  Is this a new product?
                </p>
                <button
                  onClick={handleAddProduct}
                  className="text-primary-purple text-[12px] md:text-[14px] font-semibold">
                  + Add New Product
                </button>
              </div>

              {orderDetail?.cartProducts?.length > 0 && (
                <div className="bg-primary-baseWhite p-2.5 md:p-6 rounded-2xl flex flex-col gap-3">
                  {orderDetail.cartProducts.map((product: any, index: any) => {
                    const quantity =
                      quantities[product.productId] ?? product.quantity;

                    return (
                      <div
                        key={index}
                        className="border-b-[1px] last:border-none justify-between items-start gap-4 flex flex-col">
                        <div className="flex items-start w-full justify-between">
                          <div className="flex items-center gap-2">
                            <div>
                              <img
                                className="w-[50px] h-[50px] md:w-[100px] md:h-[100px] object-contain rounded-2xl"
                                src={product.productImage}
                                alt="productImage"
                              />
                            </div>
                            <div className="flex flex-col gap-3">
                              <p className="text-[#919191] font-sora text-[12px]">
                                Product
                              </p>
                              <h4 className="text-[16px] font-sora text-primary-neutralt1">
                                {product.productName}
                              </h4>
                              {isMoreDetails === index ? (
                                ''
                              ) : (
                                <div className="flex md:w-[120px] items-center justify-evenly py-1 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                                  <button
                                    type="button"
                                    onClick={() =>
                                      handleDecreaseQuantity(product.productId)
                                    }>
                                    {quantity <= 1 ? (
                                      <Trash
                                        size={22}
                                        className="text-primary-neutralt1"
                                      />
                                    ) : (
                                      <MinusCirlce className="text-primary-neutralt1" />
                                    )}
                                  </button>

                                  <p className="text-[14px] font-sora text-primary-neutralt1">
                                    {' '}
                                    {quantity}
                                  </p>
                                  <AddCircle
                                    className="text-primary-neutralt1 cursor-pointer"
                                    onClick={() =>
                                      handleIncreaseQuantity(product.productId)
                                    }
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex flex-col h-[100px] text-right">
                            {isMoreDetails === index ? (
                              ''
                            ) : (
                              <div>
                                <h4 className="font-sora text-primary-neutralt2 text-[16px] font-semibold">
                                  ₦ {FormatPrice(product.discountedPrice)}
                                </h4>
                                <p className="text-[14px] text-[#DC3545] line-through">
                                  ₦ {FormatPrice(product.sellingPrice)}
                                </p>
                              </div>
                            )}

                            <div
                              onClick={() => handleMoreDetails(index)}
                              className={`${
                                isMoreDetails === index
                                  ? 'cursor-pointer self-end'
                                  : 'mt-auto cursor-pointer self-end'
                              }`}>
                              {isMoreDetails === index ? (
                                <ArrowUp2 className="text-primary-neutralt1 text-right" />
                              ) : (
                                <ArrowDown2 className="text-primary-neutralt1 text-right" />
                              )}
                            </div>
                          </div>
                        </div>
                        {isMoreDetails === index && (
                          <div className="flex flex-col w-full gap-5">
                            <h4 className="text-[16px] font-sora font-semibold text-primary-neutralt1">
                              Pricing and Quantity
                            </h4>
                            <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                              <p className="font-sora text-primary-neutralt2 text-[14px]">
                                Quantity
                              </p>
                              <div className="flex w-[210px]  justify-between p-2 md:p-4 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                                <button
                                  type="button"
                                  disabled={quantity <= 1}
                                  onClick={() =>
                                    handleDecreaseQuantity(product.productId)
                                  }>
                                  <MinusCirlce
                                    className={` ${
                                      quantity <= 1
                                        ? 'text-primary-neutral300 cursor-not-allowed'
                                        : 'text-primary-neutralt1 cursor-pointer'
                                    }`}
                                  />
                                </button>
                                <p className="text-[14px] font-sora text-primary-neutralt1">
                                  {quantity}
                                </p>
                                <AddCircle
                                  className="text-primary-neutralt1 cursor-pointer "
                                  onClick={() =>
                                    handleIncreaseQuantity(product.productId)
                                  }
                                />
                              </div>
                            </div>
                            {/* <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                              <p className="font-sora text-primary-neutralt2 text-[14px]">
                                Selling Price
                              </p>
                              <div className="flex max-w-fit md:w-[210px] p-2 md:p-4 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                                <p className="font-sora text-primary-neutralt2 ">
                                  ₦ {FormatPrice(product.discountedPrice)} 
                                </p>
                              </div>
                            </div> */}
                            <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                              <p className="font-sora text-primary-neutralt2 text-[14px]">
                                Selling Price
                              </p>
                              <div className="flex max-w-fit md:w-[210px] p-2 md:p-4 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                                <span className="font-sora text-primary-neutralt2">
                                  ₦
                                </span>
                                <input
                                  type="text" // Changed to text to allow clearing
                                  value={
                                    editedPrices[product.productId] !==
                                    undefined
                                      ? editedPrices[product.productId]
                                      : product.discountedPrice
                                  }
                                  onChange={(e) =>
                                    handlePriceChange(
                                      product.productId,
                                      e.target.value
                                    )
                                  }
                                  onFocus={() => {
                                    if (
                                      editedPrices[product.productId] ===
                                      undefined
                                    ) {
                                      handlePriceChange(product.productId, '');
                                    }
                                  }}
                                  className="w-full outline-none text-primary-neutralt2 bg-transparent"
                                />
                              </div>
                            </div>
                            <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                              <p className="font-sora text-primary-neutralt2 text-[14px]">
                                Discounted Price
                              </p>
                              <div className="flex w-fit md:w-[210px] items-center  justify-between p-2 md:p-4 border-[1px] rounded-3xl border-primary-neutral300 ">
                                <p className="font-sora text-[#DC3545] ">
                                  ₦{' '}
                                  {FormatPrice(
                                    product.sellingPrice -
                                      editedPrices[product.productId] ||
                                      product.discountedPrice
                                  )}
                                </p>
                                <div className="flex items-center gap-3">
                                  {' '}
                                  <p className="font-sora text-[#DC3545] ">
                                    {product.discount || Math.round(
                                      ((product.sellingPrice -
                                        (editedPrices[product.productId] ||
                                          product.discountedPrice)) /
                                        product.sellingPrice) *
                                        100
                                    )}
                                    %
                                  </p>
                                  {/* <p className="font-sora text-[18px] text-primary-neutralt1">
                                    X
                                  </p> */}
                                </div>
                              </div>
                            </div>
                            {/* <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                              <p className="font-sora text-primary-neutralt2 text-[14px]">
                                Delivery
                              </p>
                              <div className="flex w-[210px] p-2 md:p-4 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                                <p className="font-sora text-primary-neutralt2 ">
                                  ₦ {FormatPrice(orderDetail.deliveryFee)}{' '}
                                  {orderDetail.deliveryFee === 0 && '(free)'}
                                </p>
                              </div>
                            </div> */}
                            <div className="flex w-full pb-4  border-primary-neutral300 justify-between items-center">
                              <p className="font-sora text-primary-neutralt2 text-[14px]">
                                Total
                              </p>
                              <div className="self-end">
                                <p className="font-sora text-[16px] text-primary-neutralt2 ">
                                  ₦{' '}
                                  {FormatPrice(
                                    product.discountedPrice * quantity +
                                      orderDetail.deliveryFee
                                  )}
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
              <div>
                <p className="mb-3">Delivery Cost</p>
                <Dropdown
                  onSelect={handleDeliveryChange}
                  options={
                    deliveryFeeData.map(
                      (d: any) => d.deliveryTitle + ' - ' + d.deliveryValue
                    ) || []
                  }
                  placeholder="select delivery fee"
                  value={
                    selectedDelivery
                      ? `${selectedDelivery?.deliveryTitle} - ${selectedDelivery?.deliveryValue}`
                      : orderDetail?.deliveryFee
                  }
                  border="bg-[#EAF7ED]"
                />

                <div className="flex items-center justify-between mt-4">
                  <span className="text-[12px] md:text-[14px] text-primary-neutralt2">
                    Do you want to set new cost
                  </span>
                  <button
                    onClick={handleDelivery}
                    className="text-primary-purple text-[12px] md:text-[14px] font-semibold">
                    + Set New Delivery Fee
                  </button>
                </div>
                <div className="flex justify-between items-center mt-10 py-2.5 border-y border-y-[#CCCCCC]">
                  <p className="text-sm text-[#5B5B5B] font-sora font-normal">
                    Sub Total
                  </p>
                  <span className="text-base text-[#5B5B5B] font-sora font-normal">
                    ₦ {orderDetail?.subTotalFee.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between items-center my-4 py-2.5 border-b border-b-[#CCCCCC]">
                  <p className="text-sm text-[#5B5B5B] font-sora font-normal">
                    Delivery fee
                  </p>
                  <span className="text-base text-[#5B5B5B] font-sora font-normal">
                    ₦ {orderDetail?.deliveryFee || 0}
                  </span>
                </div>
                <div className="flex justify-between py-2.5 items-center">
                  <p className="text-sm text-[#5B5B5B] font-sora font-normal">
                    Grand Total
                  </p>
                  <span className="text-xl text-[#28A745] font-sora font-bold">
                    ₦ {(subTotal + deliveryFee).toLocaleString()}
                   
                  </span>
                </div>
              </div>
            </div>

            {/* Channel Section */}
            <div className="p-6 flex flex-col gap-3">
              <h4 className="font-semibold font-sora text-base">
                Where are they buying from?
              </h4>
              <p className="font-sora text-[14px] text-primary-neutralt2">
                Channel
              </p>
              <Dropdown
                options={[
                  'WhatsApp',
                  'Instagram',
                  'Telegram',
                  'Facebook',
                  'TikTok',
                  'Instore',
                  'Twitter (X)',
                  'Others',
                ]}
                onSelect={handleChannelSelect}
                value={orderDetail?.channel}
                placeholder={orderDetail?.channel}
              />
            </div>

            {/* Payment Method Section */}
            <div className="p-6 flex flex-col gap-6 mb-[100px]">
              <h4 className="font-semibold font-sora text-base">
                How are they paying?
              </h4>
              <div className="w-full flex gap-2 p-1 border rounded-3xl justify-between">
                {tabs.map((tab) => (
                  <button
                    key={tab}
                    className={`flex-1 px-3 md:px-4 py-2 text-center text-[12px] md:text-[14px] rounded-full transition ${
                      activeTab === tab
                        ? tab === 'Not Yet Paid'
                          ? 'bg-[#FFF2DF] text-[#FF9900] font-semibold'
                          : tab === 'Paid'
                          ? 'bg-[#EAF7ED] text-[#28A745] font-semibold'
                          : 'bg-[#E6E5F9] text-primary-purple font-semibold'
                        : 'text-primary-neutralt2 bg-primary-baseWhite'
                    }`}
                    onClick={() => handleTabClick(tab)}>
                    {tab}
                  </button>
                ))}
              </div>

              {/* Conditional Rendering for Paid or Installment */}
              {activeTab === 'Paid' && (
                <Dropdown
                  options={['Cash', 'Bank Transfer', 'POS', 'Crypto', 'Others']}
                  onSelect={handlePaid}
                  value={orderDetail.paymentMethod}
                  placeholder="Select how you were paid"
                />
              )}

              {activeTab === 'Installment' && (
                <>
                  <Dropdown
                    options={[
                      'Cash',
                      'Bank Transfer',
                      'POS',
                      'Crypto',
                      'Others',
                    ]}
                    onSelect={handleSelect}
                    value={orderDetail.paymentMethod}
                    placeholder="Select how you were paid"
                  />
                  <InputField
                    name="amountPaid"
                    placeholder="Enter the amount you were paid"
                    className="w-full mt-2"
                    value={orderDetail.amountPaid}
                    onChange={handleInputChange}
                  />
                </>
              )}
            </div>
          </div>

          {/* Bottom Buttons */}
          <div className="fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2 md:p-10">
            <div className="flex w-full gap-2.5">
              <button
                onClick={closeModal}
                className="bg-transparent text-[#2a2a2a] text-sm font-semibold border px-12 h-[49px] rounded-2xl">
                Back
              </button>
              <button
                onClick={handleSubmit}
                disabled={editOrder.isLoading}
                className={`bg-[#4f4cd8] w-full text-white text-sm font-semibold border px-6 h-[49px] rounded-2xl ${
                  editOrder.isLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}>
                {editOrder.isLoading ? 'Updating ...' : 'Update & Save Changes'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* AddCustomer Modal */}
      {addCustomerPanel && (
        <AddCustomer
          closeModal={closeCustomerPanel}
          isOpen={addCustomerPanel}
        />
      )}

      {/* addproduct panel */}
      {addProduct && (
        <AddProduct closeModal={closeAddProduct} isOpen={addProduct} />
      )}

      {addDeliveryPanel && (
        <DeliveryCost
          closeModal={closeCustomerPanel}
          isOpen={addDeliveryPanel}
        />
      )}
    </div>
  );
};

export default EditOrder;
