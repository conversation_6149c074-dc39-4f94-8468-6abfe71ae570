/* eslint-disable @typescript-eslint/no-explicit-any */

import {
  LogoutCurve,
  // LogoutCurve,
  Notification,
  // SearchNormal1,
  TextalignRight,
} from 'iconsax-react';
import Logo from '../assets/whiteLogo.svg';
import { NavLink } from 'react-router-dom';
import { Pages, PagesOther } from '../Constants/MenuData';
import { useEffect, useState } from 'react';
import { useUserAuthStore } from '../store/auth';
import ConfirmLogout from './ConfirmLogout';
import NotificationModal from './NotificationModal';
import { Notifications } from '../utils/Services/Notification';
import { useQuery } from 'react-query';
import { toast } from 'react-toastify';

const SecondHeader = () => {
  const [show, setShow] = useState(false);
  const [showLogout, setShowLogout] = useState<boolean>(false);
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  const business = useUserAuthStore((state) => state.business);
  const isBusinessEmpty = Object.values(business).every(
    (value) => value === ''
  );
  const user = useUserAuthStore((state) => state.user);

  const showContent = () => {
    setShow(false);
  };

    useEffect(() => {
      if (show) {
        document.body.classList.add("no-scroll");
      } else {
        document.body.classList.remove("no-scroll");
      }
  
      return () => {
        document.body.classList.remove("no-scroll");
      };
    }, [show]);

  const showHandler = () => {
    setShow(!show);
  };

  const handleshowlog = () => {
    setShow(!show);
    setShowLogout((prev) => !prev);
  };
  const handleNotification = () => {
    setIsNotificationOpen((prev) => !prev);
    setActiveTab('all');
  };
  const { data, refetch } = useQuery(
    ['notifications', user.vendorId],
    () => Notifications.getNotification(user.vendorId),
    {
      enabled: !!user.vendorId,
      onSuccess: () => {},
      onError: (error: any) => {
        toast.error('Failed to fetch notifications');
        console.log('notification error:', error);
      },
    }
  );
  const NotificationData = data?.data;
  const unread = data?.data?.unreadCount;

  return (
    <div className=" fixed w-full z-30">
      <div className="md:hidden block">
        <div className="flex justify-between py-[22px]  bg-primary-purple px-4">
          <img className="object-contain z-50" src={Logo} alt="Logo" />
          {!isBusinessEmpty && (
            <TextalignRight size="40" color="#FCFCFC" onClick={showHandler} />
          )}
        </div>

        {show && (
          <div className="fixed font-sora  bg-black/10 top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-500 ease-in-out">
            <div
              className="fixed top-0  right-0 h-full w-[75%] bg-[#fcfcfc] text-white transform transition-transform duration-300 ease-in-out overflow-y-auto z-50 shadow-lg"
              style={{
                transform: show ? 'translateX(0)' : 'translateX(100%)',
              }}>
              <div className="h-[80%] relative list-none p-0">
                <div>
                  <button
                    className="absolute top-[-2px] left-[10px] bg-none border-none font-semibold text-[40px] text-black cursor-pointer"
                    id="closeMenu"
                    onClick={showHandler}>
                    ×
                  </button>
                </div>
                <div className="fixed top-[80px] left-0 px-[1.7rem] w-[100%]">
                  {/* Pages mapping */}
                  {Pages.map((nav, index) => (
                    <NavLink
                      key={index}
                      to={nav.path}
                      onClick={showContent}
                      className={({ isActive }) =>
                        `flex items-center justify-between p-[0.63rem] text-sm gap-5 font-sora my-[20px] transition-all duration-500 ease-in-out h-[40px] ${
                          isActive
                            ? 'text-primary-purple rounded-[16px] bg-[#e6e5f9]'
                            : ' text-[#7b7b7b]'
                        }`
                      }>
                      <span>
                        {nav.icon && <nav.icon size={20} color="#7e8494" />}
                      </span>
                      <p className="no-underline flex items-center gap-[1.5rem]">
                        {nav.name}
                      </p>
                    </NavLink>
                  ))}

                  {PagesOther.map((nav, index) => (
                    <NavLink
                      key={index}
                      to={nav.path}
                      onClick={showContent}
                      className={({ isActive }) =>
                        `flex items-center justify-between p-[0.63rem] text-sm gap-5 font-sora my-[20px] transition-all duration-500 ease-in-out h-[40px] ${
                          isActive
                            ? 'text-primary-purple rounded-[16px] bg-[#e6e5f9]'
                            : ' text-[#7b7b7b]'
                        }`
                      }>
                      <span>
                        {nav.icon && <nav.icon size={20} color="#7e8494" />}
                      </span>
                      <p className="no-underline flex items-center gap-[1.5rem]">
                        {nav.name}
                      </p>
                    </NavLink>
                  ))}

                  <div className="w-full ">
                    <button
                      onClick={handleshowlog}
                      className={
                        'font-sora flex w-full justify-between  gap-4 text-primary-neutral800 p-[8px] text-[14px] hover:text-primary-purple hover:rounded-[16px] hover:bg-primary-purple100'
                      }>
                      <LogoutCurve />
                      Logout
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      <div className=" w-full bg-[#fcfcfc] px-3">
        <div className="md:hidden flex items-center justify-between font-sora pt-6 pb-5  border-b border-b-[#DCDCDC]">
          <h2 className="text-[#1A1A1A] ml-2 capitalize">
            Welcome {user.firstName} 👋
          </h2>
          <div
            onClick={handleNotification}
            className="bg-[#e6e5f9] relative border-[1px] border-primary-purple p-[8px] rounded-[16px] mr-[15px]">
            <Notification size={24} variant="Outline" />
            {unread > 0 && (
              <p className="absolute w-[16px] h-[16px] text-[10px] -top-[8px] -right-[3px] rounded-full text-center bg-[#DC3545] text-primary-baseWhite">
                {unread}
              </p>
            )}
          </div>
          {showLogout && (
            <ConfirmLogout isOpen={showLogout} closeModal={handleshowlog} />
          )}
          {isNotificationOpen && (
            <NotificationModal
              isOpen={isNotificationOpen}
              onClose={handleNotification}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              data={NotificationData?.notifications}
              unreadCount={unread}
              refetch={refetch}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default SecondHeader;
