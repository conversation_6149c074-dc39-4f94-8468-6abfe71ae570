// /// <reference lib="webworker" />

// const CACHE_NAME = "my-pwa-cache-v1";
// const urlsToCache = [
//   "/",
//   "/manifest.json",
//   "/Favicon.png",
//   "icons/Purple192.png",
//   "icons/Purple512.png",
//   "icons/Purple180.png",
//   "/static/js/bundle.js",
// ];

// const sw = self;

// // Install event - Cache static assets
// sw.addEventListener("install", (event) => {
//   event.waitUntil(
//     caches.open(CACHE_NAME).then((cache) => {
//       return cache.addAll(urlsToCache);
//     })
//   );
//   sw.skipWaiting(); // Immediately activate the service worker
// });

// // Fetch event - Custom behavior for index.html and analytics.js
// sw.addEventListener("fetch", (event) => {
//   if (event.request.method === "GET") {
//     event.respondWith(
//       caches.match(event.request).then((cachedResponse) => {
//         if (cachedResponse) {
//           return cachedResponse; // Serve cached response if available
//         }
//         return fetch(event.request).then((networkResponse) => {
//           return caches.open(CACHE_NAME).then((cache) => {
//             cache.put(event.request, networkResponse.clone());
//             return networkResponse;
//           });
//         });
//       })
//     );
//   } else {
//     // Fetch non-GET requests from the network
//     event.respondWith(fetch(event.request));
//   }
// });

// // Activate event - Clean up old caches
// sw.addEventListener("activate", (event) => {
//   const cacheWhitelist = [CACHE_NAME];
//   event.waitUntil(
//     caches.keys().then((cacheNames) =>
//       Promise.all(
//         cacheNames.map((cacheName) => {
//           if (!cacheWhitelist.includes(cacheName)) {
//             return caches.delete(cacheName);
//           }
//         })
//       )
//     )
//   );
//   sw.clients.claim(); // Take control of all pages immediately
// });

// // Periodic Background Sync
// sw.addEventListener("periodicsync", (event) => {
//   if (event.tag === "content-sync") {
//     event.waitUntil(
//       // Your background sync task logic here
//       fetch("/api/refresh-content") // Example endpoint
//         .then((response) => {
//           if (!response.ok) {
//             throw new Error("Network response was not ok");
//           }
//           return response.json();
//         })
//         .then((data) => {
//           console.log("Periodic Sync: Content refreshed!", data);
//         })
//         .catch((error) => {
//           console.error("Periodic Sync failed:", error);
//         })
//     );
//   }
// });
importScripts(
  'https://storage.googleapis.com/workbox-cdn/releases/7.0.0/workbox-sw.js'
);

const { precacheAndRoute } = workbox.precaching;
const { registerRoute } = workbox.routing;
const { StaleWhileRevalidate, CacheFirst } = workbox.strategies;
const { ExpirationPlugin } = workbox.expiration;

// Precache and route all assets defined in the manifest
precacheAndRoute(self.__WB_MANIFEST);

// Cache static resources like scripts and styles
registerRoute(
  ({ request }) =>
    request.destination === 'script' || request.destination === 'style',
  new StaleWhileRevalidate({
    cacheName: 'static-resources',
  })
);

// Cache images
registerRoute(
  ({ request }) => request.destination === 'image',
  new CacheFirst({
    cacheName: 'images',
    plugins: [
      new ExpirationPlugin({
        maxEntries: 60,
        maxAgeSeconds: 30 * 24 * 60 * 60, // 30 Days
      }),
    ],
  })
);

// Cache API responses
registerRoute(
  ({ url }) => url.pathname.startsWith('/api'),
  new StaleWhileRevalidate({
    cacheName: 'api-responses',
  })
);

// Handle navigation requests with a fallback for offline
self.addEventListener('fetch', (event) => {
  if (event.request.mode === 'navigate') {
    event.respondWith(
      fetch(event.request).catch(async () => {
        const fallbackResponse = await caches.match('/offline.html');
        return (
          fallbackResponse ||
          new Response('Offline', {
            status: 200,
            headers: { 'Content-Type': 'text/html' },
          })
        );
      })
    );
  }
});

// Ensure the service worker takes control immediately
self.addEventListener('activate', (event) => {
  event.waitUntil(self.clients.claim());
});