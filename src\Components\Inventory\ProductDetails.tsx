/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  <PERSON>R<PERSON>,
  BoxTick,
  ReceiptEdit,
  Setting5,
  Trash,
} from 'iconsax-react';
import { Link } from 'react-router-dom';
import { RouteNames } from '../../utils/RouteNames';
import Chart from '../TrendChart';
import thumbnail from '../../assets/video_thumbnail.jpg'

const ProductDetails = ({
  onClose,
  isOpen,
  viewingId,
  openEditProduct,
  openRestockProduct,
  openAdjustProduct,
  openDeleteProduct,
  productDetails,
  isLoading,
  isError,
}: any) => {
  return (
    <div className="fixed font-sora  top-0 left-0 w-full h-full  bg-black/10 bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={onClose}
            className="text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]"
          >
            &times;
          </span>
          {isError ? (
            <div>
              <h2>An error occured, please refresh the page</h2>
            </div>
          ) : (
            <>
              <div className="overflow-y-auto h-screen scrollbar-none hidescroll">
                <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold">
                  <>
                    {isLoading ? (
                      <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[350px]"></div>
                    ) : (
                      <span>
                        {productDetails?.productName} ({productDetails?.SKU})
                      </span>
                    )}
                  </>
                </h2>
                {isLoading ? (
                  <div className="rounded-2xl animate-pulse bg-slate-200 h-[20px] w-[150px]"></div>
                ) : (
                  <p className=" font-sora text-sm text-[#919191]">
                    Category: <span>{productDetails?.category}</span>
                  </p>
                )}
                {/* Main image / video and additional images */}
                <div className="flex h-[424px] gap-5 mt-5 mb-7">
                  <div className="text-center">
                    {isLoading ? (
                      <div className="animate-pulse bg-slate-200 h-[424px] w-[352px] rounded-[16px] border"></div>
                    ) : (
                      // Check if the main item is a video
                      productDetails?.images[0]?.includes('.mp4')  ? (
                        <video
                          className="w-full md:w-[352px] h-[424px] p-2 rounded-[16px] border border-[#cccccc] object-contain"
                          controls
                          autoPlay
                        >
                          <source
                            src={productDetails?.images[0]}
                            type="video/mp4"
                          />
                        </video>
                      ) : (
                        <img
                          src={
                            productDetails?.images[0]?.previewUrl ||
                            productDetails?.images[0]
                          }
                          className="w-full max-w-[352px] h-[424px] p-2 rounded-[16px] border border-[#cccccc] object-cover"
                          alt="Main Product"
                        />
                      )
                    )}
                  </div>
                  <div className="flex flex-col gap-2">
                    {isLoading ? (
                      <>
                        <div className="animate-pulse bg-slate-200 w-[140px] h-[100px] rounded-[16px]"></div>
                        <div className="animate-pulse bg-slate-200 w-[140px] h-[100px] rounded-[16px]"></div>
                      </>
                    ) : (
                      productDetails?.images
                        .slice(1)
                        .map((item: any, index: number) =>
                          item.includes('mp4') ? (
                            <video
                              key={index}
                              className="rounded-[16px] p-1 border border-[#cccccc] w-[140px] h-[100px] object-cover"
                              controls
                              muted
                              poster={thumbnail}
                            >
                              <source src={item} type="video/mp4" />
                            </video>
                          ) : (
                            <img
                              key={index}
                              src={item}
                              alt={`Secondary image ${index + 1}`}
                              className="rounded-[16px] p-1 border border-[#cccccc] w-[140px] h-[100px] object-cover"
                            />
                          )
                        )
                    )}
                  </div>
                </div>
                <div>
                  <>
                    {isLoading ? (
                      <p className="w-[200px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-[#4f4cd8] text-2xl font-semibold font-sora">
                        ₦
                        <span className="ml-1">
                          {productDetails?.discountedPrice?.toLocaleString() ||
                            0}
                        </span>
                      </p>
                    )}
                  </>
                  <div className=" flex justify-between items-center">
                    <>
                      {isLoading ? (
                        <p className="w-[200px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                      ) : (
                        <p className=" line-through text-[#919191] text-sm font-sora mt-2">
                          ₦
                          <span className="ml-1">
                            {productDetails?.sellingPrice?.toLocaleString()}
                          </span>
                        </p>
                      )}
                    </>

                    <p className=" text-red-500 text-sm font-sora">
                      <>
                        {" "}
                        {isLoading ? (
                          <p className="w-[200px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                        ) : (
                          <span className="ml-1">
                            {productDetails?.discount || 0} % off
                          </span>
                        )}{" "}
                      </>
                    </p>
                  </div>
                </div>
                <div className=" p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6">
                  {isLoading ? (
                    <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                  ) : (
                    <h4 className="text-lg font-sora font-semibold text-[#2a2a2a]">
                      Product Information
                    </h4>
                  )}
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-xs font-normal text-[#919191] font-sora">
                        Description
                      </p>
                    )}
                    <>
                      {isLoading ? (
                        <div className="animate-pulse bg-slate-200 h-[40px] rounded-2xl"></div>
                      ) : (
                        <p className="text-sm font-normal text-[#5b5b5b] font-sora">
                          {productDetails?.description}
                        </p>
                      )}
                    </>
                  </div>
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-xs font-normal text-[#919191] font-sora">
                        Supplier
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-sm font-normal text-[#5b5b5b] font-sora">
                        {productDetails?.supplierName}
                      </p>
                    )}
                  </div>
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-xs font-normal text-[#919191] font-sora">
                        Current Stock Level
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-sm font-normal text-[#5b5b5b] font-sora">
                        <span> {productDetails?.stockLevel} </span> Units
                      </p>
                    )}
                  </div>
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-xs font-normal text-[#919191] font-sora">
                        Reorder Level
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-sm font-normal text-[#5b5b5b] font-sora">
                        <span> {productDetails?.reorderLevel}</span> Units
                      </p>
                    )}
                  </div>
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-xs font-normal text-[#919191] font-sora">
                        Status
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <span
                        className={`text-[12px] w-fit px-4 py-1 rounded-xl truncate ${
                          productDetails.status === "In Stock"
                            ? "text-[#28a745] bg-[#EAF7ED]"
                            : productDetails.status === "Low Stock"
                            ? "text-[#FFC107] bg-[#FFF3E0]"
                            : "text-[#DC3545] bg-[#F8D7DA]"
                        }`}
                      >
                        {productDetails?.status}{" "}
                      </span>
                    )}
                  </div>
                </div>
                <div className=" p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6">
                  {isLoading ? (
                    <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                  ) : (
                    <h4 className="text-lg font-sora font-semibold text-[#2a2a2a]">
                      Pricing Information
                    </h4>
                  )}
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-xs font-normal text-[#919191] font-sora">
                        Purchase Price
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-sm font-normal text-[#5b5b5b] font-sora">
                        ₦
                        <span>
                          {productDetails?.purchasePrice?.toLocaleString()}{" "}
                        </span>
                      </p>
                    )}
                  </div>
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-xs font-normal text-[#919191] font-sora">
                        Selling Price
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-sm font-normal text-[#5b5b5b] font-sora">
                        ₦
                        <span>
                          {productDetails?.sellingPrice?.toLocaleString()}{" "}
                        </span>
                      </p>
                    )}
                  </div>
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-xs font-normal text-[#919191] font-sora">
                        Discounted Price
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-sm font-normal text-[#5b5b5b] font-sora">
                        ₦{" "}
                        <span>
                          {productDetails?.discountedPrice?.toLocaleString()}
                        </span>{" "}
                        (<span>{productDetails?.discount || 0}</span>% off)
                      </p>
                    )}
                  </div>
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-xs font-normal text-[#919191] font-sora">
                        Profit Margin
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-sm font-normal text-[#5b5b5b] font-sora">
                        ₦{" "}
                        <span>
                          {productDetails?.profitMargin?.toLocaleString()}
                        </span>
                      </p>
                    )}
                  </div>
                </div>
                <div className=" p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6">
                  {isLoading ? (
                    <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                  ) : (
                    <h4 className="text-lg font-sora font-semibold text-[#2a2a2a]">
                      Inventory Details
                    </h4>
                  )}
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className="  text-xs font-normal text-[#919191] font-sora">
                        Last Updated
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-sm font-normal text-[#5b5b5b] font-sora">
                        {productDetails?.lastUpdated}
                      </p>
                    )}
                  </div>
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-xs font-normal text-[#919191] font-sora">
                        Location (Optional)
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-sm font-normal text-[#5b5b5b] font-sora">
                        {productDetails?.storageLocation}
                      </p>
                    )}
                  </div>
                  <div className=" last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-xs font-normal text-[#919191] font-sora">
                        Batch Number (Optional)
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className=" text-sm font-normal text-[#5b5b5b] font-sora">
                        {productDetails?.batchNumber}
                      </p>
                    )}
                  </div>
                </div>
                <div className=" p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6">
                  {isLoading ? (
                    <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                  ) : (
                    <h4 className="text-lg font-sora font-semibold text-[#2a2a2a]">
                      Sales Information
                    </h4>
                  )}
                  <div className="last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className="text-xs font-normal text-[#919191] font-sora">
                        Sales History
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className="text-sm font-normal text-[#5b5b5b] font-sora">
                        {productDetails?.lastSalesDate || "-"}
                      </p>
                    )}
                  </div>
                  <div className="last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className="text-xs font-normal text-[#919191] font-sora">
                        Average Monthly Sales
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className="text-sm font-normal text-[#5b5b5b] font-sora">
                        <span>{productDetails?.AverageMonthlySales} </span>
                        units
                      </p>
                    )}

                    {productDetails?.salesTrendData.length > 0 && (
                      <div className=" bg-white p-4 rounded-2xl my-5 flex flex-col gap-6">
                        <p className="font-sora   text-[#5B5B5B]">
                          Sales Trends over last{" "}
                          {productDetails?.salesTrendPeriod}
                        </p>

                        <Chart salesTrendData={productDetails?.salesTrendData} />
                      </div>
                    )}
                  </div>
                </div>
                <div className=" p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6">
                  <div className=" flex flex-col md:flex-row gap-2 md:items-center justify-between">
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <h4 className="text-lg font-sora font-semibold text-[#2a2a2a]">
                        Recent Stock Movement
                      </h4>
                    )}
                    {isLoading ? (
                      <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <Link
                        to={RouteNames.trackStock}
                        className="flex w-full items-center h-[49px] text-sm justify-between px-3 py-1.5 text-[#2a2a2a] font-sora font-semibold border border-[#dcdcdc] rounded-[16px] md:w-[180px] bg-transparent"
                      >
                        View Full Log
                        <ArrowRight size="16" color="#2A2A2A" />
                      </Link>
                    )}
                  </div>
                  <div className="overflow-x-auto">
                    <table className="w-full border border-[#cccccc] my-5 bg-[#f9f9f9] rounded-[18px] overflow-hidden border-collapse">
                      {isLoading ? (
                        <p className="w-[100px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                      ) : (
                        <thead>
                          <tr>
                            <th className="px-6 py-3 text-left bg-[#DEDEDE] text-[#919191] text-xs font-sora border-b border-[#dedede]">
                              QTY
                            </th>
                            <th className="px-6 py-3 text-left bg-[#DEDEDE] text-[#919191] text-xs font-sora border-b border-[#dedede]">
                              Type
                            </th>
                            <th className="px-6 truncate py-3 text-left bg-[#DEDEDE] text-[#919191] text-xs font-sora border-b border-[#dedede]">
                              Updated QTY
                            </th>
                            <th className="px-6 py-3 text-left bg-[#DEDEDE] text-[#919191] text-xs font-sora border-b border-[#dedede]">
                              Reason
                            </th>
                          </tr>
                        </thead>
                      )}
                      <tbody>
                        {productDetails?.recentStockMovements?.map(
                          (movement: any, index: any) => (
                            <tr key={index}>
                              {isLoading ? (
                                <p className="px-6 py-3 bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                              ) : (
                                <td className="px-6 py-3 text-left border-b border-[#cccccc] text-[#5A5A5A]">
                                  {movement?.quantity?.toLocaleString()}
                                </td>
                              )}
                              {isLoading ? (
                                <p className="px-6 py-3 bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                              ) : (
                                <td className="px-6 py-3 text-left border-b border-[#cccccc] text-[#5A5A5A] capitalize">
                                  {movement?.type}
                                </td>
                              )}
                              {isLoading ? (
                                <p className="px-6 py-3 bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                              ) : (
                                <td className="px-6 py-3 text-left border-b border-[#cccccc] text-[#5A5A5A]">
                                  {movement?.updatedQuantity?.toLocaleString()}
                                </td>
                              )}
                              {isLoading ? (
                                <p className="px-6 py-3 bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                              ) : (
                                <td className="px-6 py-3 text-left border-b border-[#cccccc] text-[#5A5A5A]">
                                  {movement?.reason}
                                </td>
                              )}
                            </tr>
                          )
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className=" p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6">
                  {isLoading ? (
                    <p className="w-[150px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                  ) : (
                    <h4 className="text-lg font-sora font-semibold text-[#2a2a2a]">
                      Supplier Information
                    </h4>
                  )}
                  <div className="last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[150px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className="text-xs font-normal text-[#919191] font-sora">
                        Contact Details
                      </p>
                    )}
                    {isLoading ? (
                      <p className="w-[150px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className="text-sm font-normal text-[#5b5b5b] font-sora">
                        Phone:{" "}
                        <span> {productDetails?.supplierPhone || "-"}</span>,
                        Email:
                        <span> {productDetails?.supplierEmail || "-"}</span>
                      </p>
                    )}
                  </div>
                  <div className="last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="w-[150px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className="text-xs font-normal text-[#919191] font-sora">
                        Reorder Information
                      </p>
                    )}
                    <p className="text-sm font-normal text-[#5b5b5b] font-sora">
                      {/* Minimum order quantity: 50 pcs, Lead time: 2 weeks */}
                    </p>
                  </div>
                </div>
                <div className=" mb-[290px] md:mb-48 p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6">
                  <div className=" flex flex-col md:flex-row md:items-center gap-2 justify-between">
                    {isLoading ? (
                      <p className="w-[150px] h-[10px] bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <h4 className="text-lg font-sora font-semibold text-[#2a2a2a]">
                        Alerts and Notifications
                      </h4>
                    )}
                    {isLoading ? (
                      <p className="py-3 px-4 bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <Link to={RouteNames.alert}>
                        <button className="flex w-full h-[49px] justify-between items-center gap-4 py-3 px-4 border border-[#DCDCDC] rounded-2xl text-sm">
                          Customize Alert{" "}
                          <ArrowRight size="16" color="#2A2A2A" />
                        </button>
                      </Link>
                    )}
                  </div>
                  <div className="last:border-b-0 p-0 pb-4 border-b border-[#cccccc] flex flex-col gap-2">
                    {isLoading ? (
                      <p className="py-3 px-4 bg-slate-200 rounded-2xl animate-pulse text-2xl font-semibold mt-6"></p>
                    ) : (
                      <p className="text-xs font-normal text-[#919191] font-sora">
                        Current Alerts
                      </p>
                    )}
                    <p className="text-sm font-normal text-[#5b5b5b] font-sora">
                      {/* Low Stock Alert set at 15 pcs{' '} */}
                    </p>
                  </div>
                </div>
              </div>
              <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2 md:p-5">
                <button
                  type="button"
                  onClick={() => openEditProduct(viewingId)}
                  className=" flex flex-col items-center cursor-pointer"
                >
                  <div className="bg-[#F5F5F5] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                    <ReceiptEdit size="24" color="#2A2A2A" />{" "}
                  </div>
                  <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                    Edit
                  </p>
                </button>
                <button
                  type="button"
                  onClick={() => openAdjustProduct(viewingId)}
                  className=" flex flex-col items-center cursor-pointer"
                >
                  <div className="bg-[#E6E5F9] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                    <Setting5 size="24" color="#4F4CD8" />{" "}
                  </div>
                  <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                    Adjust
                  </p>
                </button>
                <button
                  type="button"
                  onClick={() => openRestockProduct(viewingId)}
                  className=" flex flex-col items-center cursor-pointer"
                >
                  <div className="bg-[#EAF7ED] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                    <BoxTick size="24" color="#28A745" />{" "}
                  </div>
                  <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                    Restock
                  </p>
                </button>
                <button
                  type="button"
                  onClick={() => openDeleteProduct(viewingId)}
                  className=" flex flex-col items-center cursor-pointer"
                >
                  <div className="bg-[#FAF2F2] h-[44px] w-[44px] flex justify-center items-center rounded-lg">
                    <Trash size="24" color="#DC3545" />
                  </div>
                  <p className="text-sm font-sora text-[#5b5b5b] leading-[22.4px]">
                    Delete
                  </p>
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
export default ProductDetails;
