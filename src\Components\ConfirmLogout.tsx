import { CloseSquare } from "iconsax-react";
import { useUserAuthStore } from "../store/auth";
import { useDateStore } from "../store/date";
import { useNavigate } from "react-router-dom";
import { RouteNames } from "../utils/RouteNames";

interface Control {
  isOpen: boolean;
  closeModal: () => void;
}

const ConfirmLogout: React.FC<Control> = ({ isOpen, closeModal }) => {
  const userAuthStore = useUserAuthStore((state) => state);
  const navigate = useNavigate();
  const dateStore = useDateStore((state) => state);

  const handleLogout = () => {
    // Reset the user store and date store
    userAuthStore.resetStore();
    dateStore.resetDate();
    // Redirect to login immediately
    navigate(RouteNames.login);
    console.log("Logged out, token:", userAuthStore.token);
  };

  return (
    <div
      className={`fixed top-0 left-0 w-full h-full bg-black/10 backdrop-blur-sm z-50 transition-opacity duration-500 ease-in-out ${
        isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
      }`}
    >
      <div className="flex items-center justify-center w-full h-full">
        <div className="relative flex-col bg-white p-8 rounded-lg shadow-lg md:w-[580px] w-[90%] max-w-[580px]">
          <button onClick={closeModal} className="absolute top-3 right-3">
            <CloseSquare />
          </button>
          <div className="flex-col space-y-5">
            <p className="text-center text-[18px] font-bold font-sora text-[#2a2a2a]">
              You are now quitting your Huxxle
            </p>
            <div className="flex gap-6 justify-center">
              <button
                onClick={closeModal}
                className="text-[#A21C29] font-sora text-[16px] border-b-[1px] hover:-translate-x-4 border-[#E87883] px-4 py-2 duration-300"
              >
                Cancel
              </button>
              <button
                onClick={handleLogout}
                className="text-[#1C7631] font-sora text-[16px] hover:translate-x-4 border-b-[1px] border-[#5CD878] px-4 py-2 duration-300"
              >
                Quit Huxxle
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmLogout;
