/* eslint-disable @typescript-eslint/no-explicit-any */

interface Details {
  productImage?: string;
  productName?: string;
  productSKU?: string;
  status?: string;
  paymentStatus?: string;
  currentStock?: number;
  totalAmount?: number;
  itemTotal?: number;
  date?: string;
  movementType?: string;
  promoType?: string;
  promoValue?: number;
  promoStatus?: string;
  phoneNumber?: number;
  customerStatus?: string;
  onClick?: (x?: any) => void;
}

const ProductCard: React.FC<Details> = ({
  productImage,
  productName,
  productSKU,
  status,
  paymentStatus,
  totalAmount,
  onClick,
  currentStock,
  movementType,
  itemTotal,
  date,
  promoType,
  promoValue,
  promoStatus,
  phoneNumber,
  customerStatus,
}) => {
  return (
    <div
      onClick={onClick}
      className="pb-[12px] mb-[20px] border-b-[0.5px] border-primary-neutral200 w-full flex items-center justify-between"
    >
      <div className="flex gap-4 w-[215px] items-center">
        {productImage && (
          <div>
            <img
              className="w-[56px] object-contain h-[56px] rounded-2xl"
              src={productImage}
              alt="image"
            />
          </div>
        )}
        <div className="flex flex-col gap-2">
          <p className="font-sora text-primary-purple text-[10px] font-light">
            {productSKU}
          </p>
          <p className="font-sora text-[12px] font-bold text-[#1A1A1A]">
            {productName}
          </p>
          {itemTotal && (
            <p className=" font-sora text-[#7B7B7B] text-[10px]">
              Total of {itemTotal} {itemTotal > 1 ? "items" : "item"}
            </p>
          )}
          {promoType && (
            <p className=" font-sora text-[#7B7B7B] text-[10px]">{promoType}</p>
          )}
          {date && (
            <p className=" font-sora text-[#7B7B7B] text-[10px]">{date}</p>
          )}
        </div>
      </div>
      <div className="flex items-end  flex-col gap-2">
        {currentStock && (
          <p className="text-[#2A2A2A] text-[14px] font-sora font-bold">
            {currentStock}{" "}
            <span className="text-[12px] font-normal">Units</span>
          </p>
        )}

        {promoValue && (
          <p className="text-[#2A2A2A] text-[14px] font-sora font-bold">
            {promoType === "Coupon" ? "₦" : ""}{" "}
            {promoType === "Coupon" ? promoValue.toLocaleString() : promoValue}{" "}
            {promoType === "Coupon" ? "" : "%"}
          </p>
        )}

        {totalAmount && (
          <p className="text-[#2A2A2A] text-[14px] font-sora font-bold">
            ₦ {totalAmount.toLocaleString()}{" "}
            {/* <span className="text-[12px] font-normal">Units</span> */}
          </p>
        )}
        {phoneNumber && (
          <p className="text-[#2A2A2A] text-[14px] font-sora font-bold">
            {phoneNumber}
          </p>
        )}
        {status && (
          <p
            className={`text-center px-4 py-1 rounded-xl text-[8px] truncate ${
              status === "In Stock"
                ? "text-[#28a745] bg-[#EAF7ED]"
                : status === "Low Stock"
                ? "text-[#FFC107] bg-[#FFF3E0]"
                : "text-[#DC3545] bg-[#F8D7DA]"
            }`}
          >
            {status}
          </p>
        )}
        {paymentStatus && (
          <p
            className={`text-center px-4 py-1 rounded-xl text-[10px] truncate ${
              paymentStatus === "Paid"
                ? "text-[#28a745] bg-[#EAF7ED]"
                : paymentStatus === "Not Yet Paid"
                ? "text-[#FFC107] bg-[#FFF3E0]"
                : "text-[#DC3545] bg-[#F8D7DA]"
            }`}
          >
            {paymentStatus}
          </p>
        )}
        {movementType && (
          <p
            className={`text-center px-4 py-1 rounded-xl text-[10px] truncate ${
              movementType === "in"
                ? "text-[#28a745] bg-[#EAF7ED]"
                : "text-[#DC3545] bg-[#F8D7DA]"
            }`}
          >
            {movementType}
          </p>
        )}

        {promoStatus && (
          <p
            className={`text-center px-4 py-1 rounded-xl truncate ${
             promoStatus === "Active"
                ? "text-[#28a745] bg-[#EAF7ED]"
                : promoStatus === "Inactive"
                ? "text-[#FFC107] bg-[#FFF3E0]"
                : "text-[#DC3545] bg-[#F8D7DA]"
            }`}
          >
            {promoStatus}
          </p>
        )}
        {customerStatus && (
          <p
            className={`text-center px-4 py-1 rounded-xl text-[10px] truncate ${
              customerStatus === "New"
                ? "text-[#28a745] bg-[#EAF7ED]"
                : "text-[#FFC107] bg-[#FFF3E0]"
            }`}
          >
            {customerStatus}
          </p>
        )}
      </div>
    </div>
  );
};

export default ProductCard;
