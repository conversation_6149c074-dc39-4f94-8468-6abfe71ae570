import { NavLink, useLocation } from "react-router-dom";
import Logo from "../assets/Logo.svg";
import { Pages, PagesOther } from "../Constants/MenuData";
import { useUserAuthStore } from "../store/auth";
import { LogoutCurve } from "iconsax-react";

interface Logout{
  logoutControll:()=>void
}

const Sidebar:React.FC<Logout> = ({logoutControll}) => {
  const location = useLocation();
  const business = useUserAuthStore((state) => state.business);
  const user = useUserAuthStore((state) => state.user);
   const hasImage =
    business.business_image && business.business_image.trim() !== "";
  




  return (
    <div className="flex fixed z-20 border-r-[1px] border-primary-neutral200 flex-col  max-w-[250px] h-screen bg-primary-baseWhite">
      {/* Logo */}
      <div className="md:p-[14.3px]  w-[520px]">
        <img className="object-contain" src={Logo} alt="Logo" />
      </div>

      <div className="w-full  border-b-[1px] border-primary-neutral200"></div>

      {/* Search Box */}
      {/* <div className="px-[20px] mt-[20px] ">
        <input
          type="text"
          placeholder="Search"
          className="p-[8px] outline-none text-primary-neutral800 bg-[#f4f4f9] border-[1px] rounded-[16px] border-primary-neutral200"
        />
      </div> */}

      {/* Main Pages */}
      <div className="flex p-[20px] flex-col gap-[20px]">
        {Pages.map((page, index) => {
          return (
            <NavLink
              to={page.path}
              key={index}
              end
              className={({ isActive }) => {
                const isDisabled =
                  location.pathname === "/overview/add-business";

                return isDisabled
                  ? "pointer-events-none opacity-50 font-sora p-[8px] rounded-[16px] text-primary-neutral800 text-[14px] flex items-center gap-[10px]"
                  : isActive || location.pathname.startsWith(page.path)
                  ? "font-sora bg-primary-purple200 p-[8px] rounded-[16px] text-primary-purple text-[14px] flex items-center gap-[10px]"
                  : "font-sora text-primary-neutral800 p-[8px] text-[14px] hover:text-primary-purple hover:rounded-[16px] hover:bg-primary-purple100 flex items-center gap-[10px]";
              }}
              aria-disabled={location.pathname === "/overview/add-business"}
            >
              <span className="mr-[10px]">{page.icon && <page.icon />}</span>
              {page.name}
            </NavLink>
          );
        })}
      </div>

      {/* Bipay Section */}
      <div className="px-[20px] mt-auto ">
        <div className=" p-[16px] border-[1px] bg-[#e6e5f9] border-[#4f4cd8] rounded-[16px] flex flex-col my-[20px]">
          {hasImage ? (
            <img
              className="w-[32px] h-[32px] object-cover mb-[8px] rounded-full"
              src={business?.business_image}
              alt="Business Logo"
            />
          ) : (
            <div className="w-[32px] h-[32px] flex items-center justify-center bg-orange-500 text-white font-semibold text-sm rounded-full mb-[8px]">
              {/* Display the first letters of firstName and lastName */}
              {user?.firstName[0]?.toUpperCase()}
              {user?.lastName[0]?.toUpperCase()}
            </div>
          )}
          <p className="font-sora text-[12px] text-primary-neutral1000">
            {business?.business_name}
          </p>
          <p className="font-sora text-[9px] text-primary-neutral800">
            Owned by
          </p>
          <p className="font-sora text-[9px] text-primary-neutral800">
            {user?.fullName}
          </p>
        </div>
      </div>

      {/* Other Pages (Settings, Logout) */}
      <div className="flex px-[20px] pb-[10px] flex-col gap-[20px]">
        {PagesOther.map((page, index) => (
          <NavLink
            to={page.path}
            key={index}
            end
            className={({ isActive }) => {
              const isDisabled =
                location.pathname === "/overview/add-business" && page.name !== "Logout";

              return isDisabled
                ? "pointer-events-none opacity-50 font-sora flex p-[8px] rounded-[16px] text-primary-neutral800 text-[14px]"
                : isActive || location.pathname.startsWith(page.path)
                ? "font-sora flex bg-primary-purple200 p-[8px] rounded-[16px] text-primary-purple text-[14px]"
                : "font-sora flex text-primary-neutral800 p-[8px] text-[14px] hover:text-primary-purple hover:rounded-[16px] hover:bg-primary-purple100";
            }}
            aria-disabled={
              location.pathname === "/overview/add-business" && page.name !== "Logout"
            }
          >
            <span className="mr-[10px]">{page.icon && <page.icon />}</span>
            {page.name}
          </NavLink>
        ))}
        <button
          onClick={logoutControll}
          className={
            "font-sora flex gap-4 text-[#DC3545] p-[8px] text-[14px] hover:text-primary-purple hover:rounded-[16px] hover:bg-primary-purple100"
          }
        >
          <LogoutCurve />
          Logout
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
