import KatajereAPI from '../KatajereAPI';

export const AuthServices = {
  signup: async (payload: {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
  }) => {
    return await KatajereAPI().post('/auth/signup', payload);
  },
  verifyOtp: async (payload: { email: string; otp: string }) => {
    return await KatajereAPI().post('/auth/verify-otp', payload);
  },
  login: async (payload: { email: string }) => {
    return await KatajereAPI().post('/auth/login', payload);
  },
  addBusiness: async (payload: FormData) => {
    return await KatajereAPI().post("/business/save-business", payload, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }
};
