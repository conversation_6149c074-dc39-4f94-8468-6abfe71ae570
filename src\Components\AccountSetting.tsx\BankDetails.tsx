/* eslint-disable @typescript-eslint/no-explicit-any */
import { Edit, Save2 } from 'iconsax-react';
import SettingsInput from '../../Constants/SettingsInput';
import { useUserAuthStore } from '../../store/auth';
import { useState } from 'react';
import { orderServices } from '../../utils/Services/Settings';
import { useMutation } from 'react-query';
import { toast } from 'react-toastify';
import Dropdown from '../../Constants/DropDown';

interface profileSettings {
  account_number?: string;
  bank_name?: string;
  account_name?: string;
  bank_code?: string;
  vendorId: string;
  business_address: string;
}
const BankDetails = () => {
  const user = useUserAuthStore((state) => state.user);
  const bank = useUserAuthStore((state) => state.bank);
  const business = useUserAuthStore((state) => state.business);
  const updateBank = useUserAuthStore((state) => state.updateBank);
  const [profile, setProfile] = useState<profileSettings>({
    vendorId: user.vendorId,
    account_number: bank.account_number,
    bank_name: bank.bank_name,
    account_name: bank.account_name,
    bank_code: bank.bank_code,
    business_address: business.business_address,
  });
  const mutation = useMutation((formData: FormData) =>
    orderServices.editUserProfile(formData)
  );
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setProfile((prevDetails) => ({
      ...prevDetails,
      [name]: value,
    }));
  };

   const handleBankChange = (value: string) => {
     setProfile((prevDetails) => ({
       ...prevDetails,
       bank_name: value,
       bank_code: banks[value], // Get and store the corresponding bank code
     }));
   };
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append('vendorId', profile.vendorId);
    const bankDetails = {
      account_number: profile.account_number,
      bank_name: profile.bank_name,
      account_name: profile.account_name,
      bank_code: profile.bank_code,
    };
    formData.append('bankDetails', JSON.stringify(bankDetails));
    if (
      !profile.account_number ||
      !profile.bank_name ||
      !profile.account_name ||
      !profile.business_address
    ) {
      toast.error(
        'Please fill in all required fields: Account Number, Bank Name, Business address and Account Name.'
      );
      return;
    }

    if (profile?.account_number?.length !== 10) {
      toast.error('Incomplete Account Number');
      return;
    }
    // console.log('Form Data:', {
    //   vendorId: profile.vendorId,
    //   bankDetails,
    // });
    mutation.mutate(formData, {
      onSuccess: (data) => {
        updateBank(data?.data?.bank);
        toast.success(data?.data?.message || 'Profile updated successfully!');
      },
      onError: (error: any) => {
        const errorMessage =
          error?.response?.data?.error || 'Something went wrong';
        toast.error(errorMessage);
      },
    });
  };
  return (
    <form onSubmit={handleSubmit} className=" px-4 md:px-0">
      <div className="flex justify-between items-center mb-6">
        <p className="text-[#2A2A2A] text-lg font-semibold">Add Bank details</p>
        <div className="border border-[#DCDCDC] rounded-2xl p-3">
          <Edit size="16" color="#4F4CD" />
        </div>
      </div>
      <SettingsInput
        placeholder="Enter Account Name"
        label="Account Name"
        name="account_name"
        value={profile.account_name}
        readOnly
        onChange={handleChange}
      />
      <div className=" flex flex-col gap-2.5 mb-6">
        <p className="text-[#5B5B5B] font-sora text-sm">Bank Name</p>
      
        <Dropdown
          onSelect={handleBankChange}
          options={Object.keys(banks)}
          placeholder="Select Bank"
          value={profile.bank_name}
        />
      </div>
      <SettingsInput
        placeholder="Enter Account Number"
        label="Account Number (10digits)"
        name="account_number"
        value={profile.account_number}
        onChange={handleChange}
        readOnly
      />
      <SettingsInput
        placeholder="1234567"
        label="Bank Code"
        name="bank_code"
        readOnly
        value={profile.bank_code}
        onChange={handleChange}
      />
      <SettingsInput
        placeholder="<EMAIL>"
        label="Business Address"
        name="business_address"
        value={profile.business_address}
        readOnly
        onChange={handleChange}
      />
      <div className="flex justify-end md:justify-start">
        <button
          type="submit"
          disabled
          className={` flex gap-[31px] px-4 h-[41px] rounded-2xl items-center font-semibold text-[#FCFCFC] text-sm ${
            mutation.isLoading 
              ? "bg-[#4F4CD8]  opacity-50 cursor-not-allowed "
              : " bg-[#4F4CD8] opacity-50 cursor-not-allowed"
          }`}
        >
          <span>{mutation.isLoading ? "saving . . ." : "Save Changes"}</span>
          <Save2 size="16" />
        </button>
      </div>
    </form>
  );
};
export default BankDetails;

const banks: Record<string, string> = {
  "9mobile 9Payment Service Bank": "120001",
  "Abbey Mortgage Bank": "404",
  "Above Only MFB": "51204",
  "Abulesoro MFB": "51312",
  "Access Bank": "044",
  "Access Bank (Diamond)": "063",
  "Accion Microfinance Bank": "602",
  "Aella MFB": "50315",
  "AG Mortgage Bank": "90077",
  "Ahmadu Bello University Microfinance Bank": "50036",
  "Airtel Smartcash PSB": "120004",
  "AKU Microfinance Bank": "51336",
  "Akuchukwu Microfinance Bank Limited": "090561",
  "ALAT by WEMA": "035A",
  "Alternative Bank": "000304",
  "Amegy Microfinance Bank": "090629",
  "Amju Unique MFB": "50926",
  "Aramoko MFB": "50083",
  "ASO Savings and Loans": "401",
  "Assets Microfinance Bank": "50092",
  "Astrapolaris MFB LTD": "MFB50094",
  "AVUENEGBE MICROFINANCE BANK": "090478",
  "AWACASH MICROFINANCE BANK": "51351",
  "AZTEC MICROFINANCE BANK LIMITED": "51337",
  "Bainescredit MFB": "51229",
  "Banc Corp Microfinance Bank": "50117",
  "BANKLY MFB": "51341",
  "Baobab Microfinance Bank": "MFB50992",
  "BellBank Microfinance Bank": "51100",
  "Benysta Microfinance Bank Limited": "51267",
  "Beststar Microfinance Bank": "50123",
  "BOLD MFB": "50725",
  "Bosak Microfinance Bank": "650",
  "Bowen Microfinance Bank": "50931",
  "Branch International Finance Company Limited": "FC40163",
  "BuyPower MFB": "50645",
  "Carbon": "565",
  "Cashbridge Microfinance Bank Limited": "51353",
  "CASHCONNECT MFB": "865",
  "CEMCS Microfinance Bank": "50823",
  "Chanelle Microfinance Bank Limited": "50171",
  "Chikum Microfinance Bank": "312",
  "Citibank Nigeria": "023",
  "CITYCODE MORTAGE BANK": "070027",
  "Consumer Microfinance Bank": "50910",
  "Corestep MFB": "50204",
  "Coronation Merchant Bank": "559",
  "County Finance Limited": "FC40128",
  "Crescent MFB": "51297",
  "Crust Microfinance Bank": "090560",
  "CRUTECH MICROFINANCE BANK LTD": "MFB50216",
  "Davenport MICROFINANCE BANK": "51334",
  "Dot Microfinance Bank": "50162",
  "EBSU Microfinance Bank": "50922",
  "Ecobank Nigeria": "050",
  "Ekimogun MFB": "50263",
  "Ekondo Microfinance Bank": "098",
  "EXCEL FINANCE BANK": "090678",
  "Eyowo": "50126",
  "Fairmoney Microfinance Bank": "51318",
  "Fedeth MFB": "50298",
  "Fidelity Bank": "070",
  "Firmus MFB": "51314",
  "First Bank of Nigeria": "011",
  "First City Monument Bank": "214",
  "FIRST ROYAL MICROFINANCE BANK": "090164",
  "FIRSTMIDAS MFB": "51333",
  "FirstTrust Mortgage Bank Nigeria": "413",
  "FSDH Merchant Bank Limited": "501",
  "FUTMINNA MICROFINANCE BANK": "832",
  "Garun Mallam MFB": "MFB51093",
  "Gateway Mortgage Bank LTD": "812",
  "Globus Bank": "00103",
  "Goldman MFB": "090574",
  "GoMoney": "100022",
  "GOOD SHEPHERD MICROFINANCE BANK": "090664",
  "Goodnews Microfinance Bank": "50739",
  "Greenwich Merchant Bank": "562",
  "GROOMING MICROFINANCE BANK": "51276",
  "GTI MFB": "50368",
  "Guaranty Trust Bank": "058",
  "Hackman Microfinance Bank": "51251",
  "Hasal Microfinance Bank": "50383",
  "HopePSB": "120002",
  "IBANK Microfinance Bank": "51211",
  "Ibile Microfinance Bank": "51244",
  "Ikoyi Osun MFB": "50439",
  "Ilaro Poly Microfinance Bank": "50442",
  "Imowo MFB": "50453",
  "IMPERIAL HOMES MORTAGE BANK": "415",
  "Infinity MFB": "50457",
  "Infinity Trust Mortgage Bank": "070016",
  "ISUA MFB": "090701",
  "Jaiz Bank": "301",
  "Kadpoly MFB": "50502",
  "KANOPOLY MFB": "51308",
  "Keystone Bank": "082",
  "Kolomoni MFB": "899",
  "KONGAPAY": "100025",
  "Kredi Money MFB LTD": "50200",
  "Kuda Bank": "50211",
  "Lagos Building Investment Company Plc.": "90052",
  "Links MFB": "50549",
  "Living Trust Mortgage Bank": "031",
  "LOMA MFB": "50491",
  "Lotus Bank": "303",
  "MAINSTREET MICROFINANCE BANK": "090171",
  "Mayfair MFB": "50563",
  "Mint MFB": "50304",
  "Money Master PSB": "946",
  "Moniepoint MFB": "50515",
  "MTN Momo PSB": "120003",
  "MUTUAL BENEFITS MICROFINANCE BANK": "090190",
  "NDCC MICROFINANCE BANK": "090679",
  "NET MICROFINANCE BANK": "51361",
  "Nigerian Navy Microfinance Bank Limited": "51142",
  "Nombank MFB": "50072",
  "NOVA BANK": "561",
  "NPF MICROFINANCE BANK": "50629",
  "NSUK MICROFINANACE BANK": "51261",
  "OPay Digital Services Limited (OPay)": "999992",
  "Optimus Bank Limited": "107",
  "Paga": "100002",
  "PalmPay": "999991",
  "Parallex Bank": "104",
  "Parkway - ReadyCash": "311",
  "Paystack-Titan": "100039",
  "Polaris Bank": "076",
  "Providus Bank": "101",
  "Stanbic IBTC Bank": "221",
  "Standard Chartered Bank": "068",
  "Sterling Bank": "232",
  "Suntrust Bank": "100",
  "TAJ Bank": "302",
  "Titan Bank": "102",
  "Union Bank of Nigeria": "032",
  "United Bank For Africa": "033",
  "Unity Bank": "215",
  "VFD Microfinance Bank Limited": "566",
  "Wema Bank": "035",
  "Xpress Wallet": "100040",
  "Yes MFB": "594",
  "Zenith Bank": "057"
};


