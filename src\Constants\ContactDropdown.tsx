/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { useEffect, useState } from "react";
import { Profile, ArrowDown2 } from "iconsax-react";
import { useQuery } from "react-query";
import { orderServices } from "../utils/Services/Order";
import { useUserAuthStore } from "../store/auth";

interface Customer {
  customerName: string;
}

interface DropdownProps {
  onCustomerSelect: (customerName: string) => void; // Add a prop to pass the selected customer back to the parent
}

const Dropdown: React.FC<DropdownProps> = ({ onCustomerSelect }) => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false); // New state to track dropdown visibility
  const [customer, setCustomer] = useState<Customer[]>([]);

  const user = useUserAuthStore((state) => state.user);

  const { data, refetch } = useQuery(user.vendorId, () =>
    orderServices.getCustomers(user.vendorId)
  );
  console.log(data);

  useEffect(() => {
    if (data && data.customerTable) {
      setCustomer(data.customerTable);
    }
  }, [data]);

  const handleSelect = (customer: any): void => {
    setSelectedCustomer(customer?.customerName);
    onCustomerSelect(customer); // Call the parent component's callback with the selected customer
    setIsDropdownOpen(false);
  };

  const filteredCustomers = customer.filter((customer) =>
    customer?.customerName?.toLowerCase()?.includes(searchTerm.toLowerCase())
  );

  return (
    <div className="relative w-full">
      <h4 className="block text-primary-neutralt1 font-sora font-semibold text-[16px] mb-6">
        Who wants to buy?
      </h4>
      <p className="font-sora text-[14px] text-primary-neutralt2 mb-[12px]">
        Name
      </p>

      {/* Input Field with Dropdown Toggle */}
      <div
        className="flex items-center justify-between h-[48px] px-3 border border-gray-300 rounded-2xl cursor-pointer focus:outline-none"
        onClick={() => {
          setIsDropdownOpen(!isDropdownOpen);
          refetch();
        }} // Toggle dropdown visibility
      >
        <input
          type="text"
          className="w-full focus:outline-none "
          placeholder="Select customer from your customers list"
          value={selectedCustomer || ""}
          readOnly // This input is readonly so users can't type here
        />
        <ArrowDown2 className="text-gray-500 ml-2" />
      </div>

      {/* Dropdown List with search bar, only shows if isDropdownOpen is true */}
      {isDropdownOpen && (
        <div className="absolute w-full mt-1 bg-white shadow-lg ring-1 ring-black ring-opacity-5 rounded-md z-10">
          {/* Search input inside dropdown */}
          <div className="p-3 border-b border-gray-300">
            <input
              type="text"
              className="w-full p-2 border border-gray-300 rounded-2xl focus:outline-none"
              placeholder="Search customers"
              value={searchTerm}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setSearchTerm(e.target.value)
              }
            />
          </div>

          {/* Filtered customer list */}
          <ul className="max-h-60 overflow-y-auto">
            {filteredCustomers.length > 0 ? (
              filteredCustomers.map((customer, index) => (
                <li
                  key={index}
                  className={`flex items-center p-3 hover:bg-gray-100 cursor-pointer ${
                    selectedCustomer === customer.customerName
                      ? "bg-gray-200"
                      : ""
                  }`}
                  onClick={() => handleSelect(customer)}
                >
                  <Profile className="text-indigo-500 mr-2" />
                  <span className="text-gray-700">{customer.customerName}</span>
                </li>
              ))
            ) : (
              <li className="p-3 text-gray-500">No customers found</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default Dropdown;
