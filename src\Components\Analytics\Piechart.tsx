// Define the segments of the pie chart as an array of objects
const data = [
  { label: 'Online', value: 50, color: '#C9F3F1', amount: '₦ 4,00,000 ' },
  { label: 'Social Media', value: 35, color: '#CABDF9', amount: '₦ 5,00,000 ' },
  { label: 'In-Store', value: 15, color: '#FCCB7E', amount: '₦ 78,889 ' },
];

// Calculate the total of all values (should equal 100 for percentages)
const total = data.reduce((acc, item) => acc + item.value, 0);

// Function to convert percentage to SVG arc coordinates
const getCoordinatesForPercent = (percent: number) => {
  const x = Math.cos(2 * Math.PI * percent);
  const y = Math.sin(2 * Math.PI * percent);
  return [x, y];
};

const Piechart = () => {
  let cumulativePercent = 0;

  return (
    <div className="relative flex flex-col items-center justify-center my-14">
      <svg width="200" height="200" viewBox="-1 -1 2 2" className="w-48 h-48">
        {data.map((slice) => {
          const [startX, startY] = getCoordinatesForPercent(cumulativePercent);
          cumulativePercent += slice.value / total;
          const [endX, endY] = getCoordinatesForPercent(cumulativePercent);
          const largeArcFlag = slice.value / total > 0.5 ? 1 : 0;
          const pathData = [
            `M ${startX} ${startY}`, // Move to start point
            `A 1 1 0 ${largeArcFlag} 1 ${endX} ${endY}`, // Draw arc
            `L 0 0`, // Draw line back to the center
          ].join(' ');

          // Calculate the position of the percentage label
          const midPercent = cumulativePercent - slice.value / (2 * total);
          const [textX, textY] = getCoordinatesForPercent(midPercent);

          return (
            <g key={slice.label}>
              <path
                d={pathData}
                fill={slice.color}
                stroke="#fff"
                strokeWidth="0.01"
              />
              {/* Add percentage text inside the segment */}
              <text
                x={textX * 0.6} // Scale down the position slightly for proper placement
                y={textY * 0.6}
                textAnchor="middle"
                dominantBaseline="middle"
                className="fill-current text-black "
                fontSize="0.01em">
                {`${slice.value}%`}
              </text>
            </g>
          );
        })}
      </svg>
      <div
        className="absolute flex flex-col items-center"
        style={{ top: '0px', left: '80px' }}>
        <span className="relative text-sm text-[#2A2A2A] font-semibold">
          Online
        </span>
        {/* <span className="absolute left-0 top-full mt-2 w-[100px] h-[2px] bg-gray-300"></span> */}
      </div>
      <div
        className="absolute flex flex-col items-center"
        style={{ top: '200px', right: '155px' }}>
        <span className="relative text-sm text-[#2A2A2A] font-semibold">
          Social Media
        </span>
        {/* <span className="absolute left-0 top-full mt-2 w-[100px] h-[2px] bg-gray-300"></span> */}
      </div>
      <div
        className="absolute flex flex-col items-center"
        style={{ top: '30px', right: '50px' }}>
        <span className="relative text-sm text-[#2A2A2A] font-semibold">
          In-Store
        </span>
        {/* <span className="absolute left-0 top-full mt-2 w-[100px] h-[2px] bg-gray-300"></span> */}
      </div>
      <div className="mt-14 flex items-center justify-between w-full">
        {data.map((slice) => (
          <div key={slice.label} className="flex items-center mb-2">
            <div
              className="w-2 h-2 mr-2 rounded-full"
              style={{ backgroundColor: slice.color }}></div>
            <div>
              <span className="text-xs font-semibold text-[#2A2A2A]">
                {slice.label}
              </span>
              <span className="text-xs font-normal text-[#5B5B5B] block">
                {slice.amount}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Piechart;

