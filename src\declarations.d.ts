// Extend PermissionDescriptor to include periodic-background-sync
interface PermissionDescriptor {
  name: "periodic-background-sync" | (PermissionName & {});
}

// Extend ServiceWorkerRegistration to include periodicSync
interface PeriodicSyncManager {
  register(tag: string, options: { minInterval: number }): Promise<void>;
  getTags(): Promise<string[]>;
  unregister(tag: string): Promise<void>;
}

interface ServiceWorkerRegistration {
  periodicSync?: PeriodicSyncManager;
}

// src/declarations.d.ts
declare module "*.svg" {
  import * as React from "react";
  export const ReactComponent: React.FC<React.SVGProps<SVGSVGElement>>;
  const src: string;
  export default src;
}
