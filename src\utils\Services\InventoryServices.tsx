import KatajereAPI from '../KatajereAPI';

export const inventoryServices = {
  getAllProducts: async (vendorId: string) => {
    const data = await KatajereAPI().get(
      `/product/products?vendorId=${vendorId}`
    );
    return data.data;
  },
  addProduct: async (payload: FormData) => {
    return await KatajereAPI().post('/product/add-product', payload);
  },
  getProductDetails: async (vendorId: string, productId: string) => {
    return await KatajereAPI().get(
      `product/product-details?vendorId=${vendorId}&productId=${productId}`
    );
  },
  editProduct: async (payload: FormData) => {
    return await KatajereAPI().patch('/product/update-product', payload);
  },
  aiDescription: async (payload: {
    productName: string;
    category: string;
    description: string;
    vendorId: string;
  }) => {
    return await KatajereAPI().post('/ai/enhance-description', payload);
  },
  salesReport: async (payload: {
    businessName: string;
    startDate: string;
    endDate: string;
    format?: string;
    userEmail: string;
  }) => {
    return await KatajereAPI().post(
      '/download/generate-sales-summary-report',
      payload
    );
  },
  stockReport: async (payload: {
    businessName: string;
    startDate: string;
    endDate: string;
    format?: string;
    userEmail: string;
  }) => {
    return await KatajereAPI().post(
      'download/generate-stock-movement-report',
      payload
    );
  },
  valuationReport: async (payload: {
    businessName: string;
    startDate: string;
    endDate: string;
    format?: string;
    userEmail: string;
  }) => {
    return await KatajereAPI().post(
      '/download/generate-inventory-valuation-report',
      payload
    );
  },
  alertSettings: async (payload: {
    lowStockEnabled?: string;
    highStockEnabled?: string;
    outOfStockEnabled?: string;
    sensitivityEnabled?: string;
    lowStockThreshold?: number;
    highStockThreshold?: number;
    sensitivityLevel?: number;
    emailEnabled?: string;
    notificationFrequency?: string;
    vendorId: string;
  }) => {
    return await KatajereAPI().post('/alert/products', payload);
  },
  activateWebsite: async (payload: {
    websiteStatus: string;
    vendorId: string;
  }) => {
    return await KatajereAPI().post('/business/deactivate-website', payload);
  },
};
