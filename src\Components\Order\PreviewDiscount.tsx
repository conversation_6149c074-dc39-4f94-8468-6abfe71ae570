import { useEffect, useRef } from "react";

interface modal {
  isOpen: boolean;
  closeModal: () => void;
}
const PreviewDiscount: React.FC<modal> = ({ isOpen, closeModal }) => {
 const modalRef = useRef<HTMLDivElement>(null); // Create a reference to the modal
 
   useEffect(() => {
     if (modalRef.current) {
       modalRef.current.scrollTo({ top: 0, behavior: 'smooth' }); // Scroll the modal container to the top
     }
   }, []);
  return (
    <div className="fixed font-sora  bg-black/10 top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-500 ease-in-out">
      <div
      ref={modalRef}
        className={`fixed top-0 right-0 h-screen md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-5  transition-transform duration-500 ease-in-out ${
          isOpen ? "translate-x-0 " : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="overflow-y-auto h-screen scrollbar-none hidescroll ">
            <div className="pl-6 flex flex-col gap-10 mb-[200px]">
              <div>
                <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                  Preview Discount
                </h2>
                <p className=" text-sm text-[#919191] font-sora ">
                  Preview your promo information below
                </p>
              </div>

              <div className="bg-[#F5F5F5] py-6 px-4 rounded-2xl flex flex-col gap-6">
                <p className="font-sora text-[16px] font-semibold text-[#2A2A2A]">
                  Discount
                </p>
                <div className="flex flex-col gap-2 border-b-[0.5px] pb-[16px] border-[#CCCCCC]">
                  <p className="font-sora text-[12px] text-[#919191] leading-[19.2px]">
                    Product Name
                  </p>
                  <p className="text-sm font-sora text-[#5B5B5B]">
                    Summer Sale
                  </p>
                </div>

                <div className="flex flex-col gap-2 border-b-[0.5px] pb-[16px] border-[#CCCCCC]">
                  <p className="font-sora text-[12px] text-[#919191] leading-[19.2px]">
                    Code
                  </p>
                  <p className="text-sm font-sora text-[#5B5B5B]">Summer20</p>
                </div>

                <div className="flex flex-col gap-2 border-b-[0.5px] pb-[16px] border-[#CCCCCC]">
                  <p className="font-sora text-[12px] text-[#919191] leading-[19.2px]">
                    Value type
                  </p>
                  <p className="text-sm font-sora text-[#5B5B5B]">Percentage</p>
                </div>

                <div className="flex flex-col gap-2 border-b-[0.5px] pb-[16px] border-[#CCCCCC]">
                  <p className="font-sora text-[12px] text-[#919191] leading-[19.2px]">
                    Value percentage
                  </p>
                  <p className="text-sm font-sora text-[#5B5B5B]">20%</p>
                </div>

                <div className="flex flex-col gap-2 border-b-[0.5px] pb-[16px] border-[#CCCCCC]">
                  <p className="font-sora text-[12px] text-[#919191] leading-[19.2px]">
                    Description
                  </p>
                  <p className="text-sm font-sora text-[#5B5B5B]">
                    It is summer time, Purchase any product at 20% OFF, Hurry!!!
                  </p>
                </div>

                <div className="flex flex-col gap-2 border-b-[0.5px] pb-[16px] border-[#CCCCCC]">
                  <p className="font-sora text-[12px] text-[#919191] leading-[19.2px]">
                    Description
                  </p>
                  <p className="text-sm font-sora text-[#5B5B5B]">
                    It is summer time, Purchase any product at 20% OFF, Hurry!!!
                  </p>
                </div>

                <div className="flex flex-col gap-2 border-b-[0.5px] pb-[16px] border-[#CCCCCC]">
                  <p className="font-sora text-[12px] text-[#919191] leading-[19.2px]">
                    Valid Period
                  </p>
                  <p className="text-sm font-sora text-[#5B5B5B]">
                    01 - Sep - 2024 to 08 - Sep - 2024
                  </p>
                </div>

                <div className="flex flex-col gap-2 border-b-[0.5px] pb-[16px] border-[#CCCCCC]">
                  <p className="font-sora text-[12px] text-[#919191] leading-[19.2px]">
                    Eligible Products
                  </p>
                  <p className="text-sm font-sora text-[#5B5B5B]">
                    All Products
                  </p>
                </div>

                <div className="flex flex-col gap-2 border-b-[0.5px] pb-[16px] border-[#CCCCCC]">
                  <p className="font-sora text-[12px] text-[#919191] leading-[19.2px]">
                    Usage Limit
                  </p>
                  <p className="text-sm font-sora text-[#5B5B5B]">Limited</p>
                </div>

                <div className="flex flex-col gap-2 border-b-[0.5px] pb-[16px] border-[#CCCCCC]">
                  <p className="font-sora text-[12px] text-[#919191] leading-[19.2px]">
                    Maximum Limit
                  </p>
                  <p className="text-sm font-sora text-[#5B5B5B]">500</p>
                </div>
              </div>
            </div>

            <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5  p-10">
              <div className="flex w-full px-7.5 gap-2.5">
                <button
                  onClick={closeModal}
                  className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 py-4 rounded-2xl cursor-pointer flex-1/2"
                >
                  Back
                </button>
                <button className="bg-[#4f4cd8] w-full text-[#fcfcfc] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl cursor-pointer flex-2">
                  Publish Discount
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewDiscount;
