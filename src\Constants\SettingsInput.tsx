interface SettingsInputProps {
  id?: string;
  label?: string;
  placeholder: string;
  type?: string;
  name?: string;
  value?: string;
  defaultValue?: string;
  readOnly?: boolean;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  className?: string;
}

const SettingsInput: React.FC<SettingsInputProps> = ({
  id,
  label,
  placeholder,
  type,
  name,
  value,
  readOnly,
  onChange,
  className,
  defaultValue,
}) => {
  return (
    <div className="mb-6 w-full">
      <p className="text-[#5B5B5B] font-sora text-sm">{label}</p>
      <input
        id={id}
        type={type}
        placeholder={placeholder}
        defaultValue={defaultValue}
        value={value}
        name={name}
        readOnly={readOnly}
        onChange={onChange}
        className={`appearance-none md:max-w-[492px]  bg-[#FCFCFC] border border-[#CCCCCC] font-sora text-sm mt-2 rounded-2xl w-full h-[48px] px-4 text-[#5B5B5B] focus:outline-none focus:shadow-outline ${
          readOnly && 'bg-[#e6e5f9] border-none'
        } ${className}`}
      />
    </div>
  );
};

export default SettingsInput;
