/* eslint-disable @typescript-eslint/no-explicit-any */

import HomeButtons from "../../Constants/HomeButtons";
import {
  // DocumentDownload,
  DollarCircle,
  ShoppingBag,
  BagTick2,
  Box,
  ArrowUp,
  ArrowDown,
  Copy,
  CloseSquare,
  // Add,
  // Copy,
} from "iconsax-react";
import DatePicker from "../../utils/DatePicker";
import { FormatPrice } from "../../utils/FormatPrice";
import { useState, useEffect, useRef } from "react";
import LineChart from "../Chart";
import AddProduct from "../Inventory/AddProduct";
import { StatsService } from "../../utils/Services/StatsServices";
import { useQuery, useMutation } from "react-query";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../store/auth";
import { useDateStore } from "../../store/date";
import CardLoader from "../../Constants/CardLoader";
import PaginationTables from "./AITable";
import { Link } from "react-router-dom";
import { RouteNames } from "../../utils/RouteNames";
import { inventoryServices } from "../../utils/Services/InventoryServices";
import ConfirmationModal from "../../Constants/ConfirmationModal";
import orderEmptyLogo from "../../assets/oderEmptyLogo.svg";
import AddBankDetails from "../../Constants/AddBankDetails";
import thumbnail from "../../assets/video_thumbnail.jpg";
import { TickCircle } from "iconsax-react";
import CreateOrder from "../Order/CreateOrder";
import InputField from "../../Constants/InputField";
import { orderServices } from "../../utils/Services/Settings";
import { FaSpinner } from "react-icons/fa";

interface Payload {
  startDate?: string;
  endDate?: string;
}
interface RecentOrder {
  orderId: string;
  productName: string;
  totalAmount: string;
  totalItems: number;
  orderStatus: string;
  productImage: string;
}

interface TopPerformers {
  productName: string;
  unitsSold: number;
  productId: number;
}

interface SocialPayload {
  social_facebook: string;
  social_instagram: string;
  social_twitter: string;
  social_tiktok: string;
}

const Overview: React.FC<Payload> = () => {
  const [copy, setCopy] = useState<string | null>("Copy");
  const [addProduct, setAddProduct] = useState<boolean>(false);
  const [activateBank, setActivateBank] = useState<boolean>(false);
  // New state to control the new user tooltip
  const [showNewUserTooltip, setShowNewUserTooltip] = useState<boolean>(true);
  const userDetails = useUserAuthStore((state) => state.user);
  const businessDetails = useUserAuthStore((state) => state.business);
  const updateBusiness = useUserAuthStore((state) => state.updateBusiness);

  const accountStatus = useUserAuthStore((state) => state.newAccount);
  const socialStatus = useUserAuthStore((state) => state.businessSocialMedia);
  const [socialHandles, setSocialHandles] = useState<SocialPayload>({
    social_facebook: "",
    social_instagram: "",
    social_tiktok: "",
    social_twitter: "",
  });

  const userStatus = useUserAuthStore((state) => state.userStatus);
  const businessWebsite = useUserAuthStore((state) => state.businessWebsite);
  const { startDates, endDates } = useDateStore();
  const [isEnabled, setIsEnabled] = useState(
    businessDetails?.website_status === "Active"
  );
  // const salesReportMutation = useMutation(inventoryServices.salesReport);
  // const downloadFormat = 'csv';
  const userAuthStore = useUserAuthStore((state) => state);
  // Remove the showTooltip state

  const [showActivateModal, setShowActivateModal] = useState(false);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [setUp, setSetUp] = useState(false);
  const newAccount = accountStatus;
  const [isCopied, setIsCopied] = useState(false);
  const [createOrder, setCreateOrder] = useState(false);

  const handleCreateOrder = () => {
    setCreateOrder((prev) => !prev);
  };

  const activateMutation = useMutation((data: any) =>
    inventoryServices.activateWebsite(data)
  );

  useEffect(() => {
    setIsEnabled(businessDetails.website_status === "Active");
  }, [businessDetails.website_status]);

  const handleSocialMediaInput = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    if (name === "instagram") {
      setSocialHandles((prev) => ({
        ...prev,
        social_instagram: value,
      }));
    } else if (name === "twitter") {
      setSocialHandles((prev) => ({
        ...prev,
        social_twitter: value,
      }));
    } else if (name === "tiktok") {
      setSocialHandles((prev) => ({
        ...prev,
        social_tiktok: value,
      }));
    } else {
      setSocialHandles((prev) => ({
        ...prev,
        social_facebook: value,
      }));
    }
  };

  const handleToggleClick = () => {
    if (isEnabled) {
      setShowDeactivateModal(true);
    } else {
      setShowActivateModal(true);
    }
  };
  const handleSetUp = () => {
    if (newAccount === true) {
      setSetUp(true);
      setActivateBank(true);
    } else {
      setSetUp(false);
    }
  };
  const closeBankActivation = () => {
    setSetUp((prev) => !prev);
  };

  const handleActivateConfirm = () => {
    const data = {
      vendorId: userDetails.vendorId,
      websiteStatus: "Active",
    };

    activateMutation.mutate(data, {
      onSuccess: async (data) => {
        toast.success(data?.data?.message || "Activated successfully");
        userAuthStore.updateBusiness(data?.data?.business);
        setShowActivateModal(false);
      },
      onError: (error: any) => {
        if (error?.response?.status === 429) {
          toast.error(error?.response?.data?.message || "Too many requests");
        } else {
          toast.error(
            error?.response?.data?.error ||
              error?.message ||
              "An error occurred"
          );
        }
      },
    });
  };

  const handleDeactivateConfirm = () => {
    const data = {
      vendorId: userDetails.vendorId,
      websiteStatus: "Inactive",
    };

    activateMutation.mutate(data, {
      onSuccess: async (data) => {
        toast.success(data?.data?.message || "Deactivated successfully");
        userAuthStore.updateBusiness(data?.data?.business);
        setShowDeactivateModal(false);
      },
      onError: (error: any) => {
        if (error?.response?.status === 429) {
          toast.error(error?.response?.data?.message || "Too many requests");
        } else {
          toast.error(
            error?.response?.data?.error ||
              error?.message ||
              "An error occurred"
          );
        }
      },
    });
  };

  const formattedStartDate = startDates
    ? new Date(startDates).toISOString().split("T")[0]
    : undefined;
  const formattedEndDate = endDates
    ? new Date(endDates).toISOString().split("T")[0]
    : undefined;
  const { data, error, isLoading, refetch } = useQuery(
    [
      "overviewstats",
      userDetails.vendorId,
      formattedStartDate,
      formattedEndDate,
    ],
    () =>
      StatsService.OverviewStats({
        vendorId: userDetails.vendorId,
        startDate: formattedStartDate,
        endDate: formattedEndDate,
      }),
    {
      enabled:
        !!userDetails.vendorId && !!formattedStartDate && !!formattedEndDate,
      onSuccess: (data) => {
        console.log("Overview Stats Response:", data);
      },
      onError: (error: any) => {
        toast.error("Failed to load stats");
        console.error("Error fetching stats:", error);
      },
    }
  );

  // Get recommendations
  const { data: AI } = useQuery(
    ["recommendations", userDetails.vendorId],
    () => StatsService.AiRecommendations({ vendorId: userDetails.vendorId }),
    {
      enabled: !!userDetails.vendorId,
      onSuccess: () => {},
      onError: (error: any) => {
        toast.error("Failed to fetch recommendations");
        console.log("ai error:", error);
      },
    }
  );
  const stats = data?.data;
  const recommendations = AI?.data;

  // const handleReport = async () => {
  //   const payload = {
  //     businessName: businessDetails.business_name,
  //     startDate: formattedStartDate || '',
  //     endDate: formattedEndDate || '',
  //     userEmail: userDetails.userEmail,
  //     vendorId: userDetails.vendorId,
  //     format: downloadFormat,
  //   };

  //   const handleResponse = (response: any) => {
  //     if (response && response.data) {
  //       const { downloadUrl, message } = response.data;
  //       if (downloadUrl) {
  //         const link = document.createElement('a');
  //         link.href = downloadUrl;
  //         link.download = 'report';
  //         link.target = '_blank';
  //         link.click();
  //         toast.success('Report downloaded successfully.');
  //       } else {
  //         toast.info(message || 'No data found for the given period.');
  //       }
  //     }
  //   };

  //   try {
  //     salesReportMutation.mutate(payload, {
  //       onSuccess: handleResponse,
  //       onError: (error) => {
  //         console.error('Error generating sales report:', error);
  //         toast.error('Failed to generate sales report.');
  //       },
  //     });
  //   } catch (error) {
  //     console.log(error);
  //   }
  // };

  useEffect(() => {
    const shouldPreventScroll =
      addProduct || setUp || (newAccount && !isEnabled && showNewUserTooltip);

    if (shouldPreventScroll) {
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    document.title = setUp
      ? "Set Up Account - Huxxle"
      : addProduct
      ? "Add Product - Huxxle"
      : "Overview";

    return () => {
      document.body.classList.remove("no-scroll");
      document.title = "Overview";
    };
  }, [addProduct, setUp, newAccount, isEnabled, showNewUserTooltip]);

  if (error) return <div>Error loading data</div>;

  const colors = ["#A5A6F6", "#F6D3A5", "#AEEBBC"];

  const maxUnitsSold = stats?.topProducts?.length
    ? Math.max(
        ...stats.topProducts.map((product: TopPerformers) => product.unitsSold)
      )
    : 0;

  const copyLink = (): void => {
    const linkElement = document.getElementById(
      "link-to-copy"
    ) as HTMLAnchorElement | null;

    if (linkElement && linkElement.href) {
      const link: string = linkElement.href;
      navigator.clipboard
        .writeText(link)
        .then(() => {
          setCopy("Copied");
          setIsCopied(true);
          setTimeout(() => setIsCopied(false), 2000);
        })
        .catch((err) => {
          console.error("Failed to copy: ", err);
        });
    } else {
      console.error("Anchor element or href not found");
    }
  };

  const toggleAddProduct = () => {
    setAddProduct((prev) => !prev);
  };
  const originalThemeColor = useRef<string | null>(null);
  useEffect(() => {
    const meta = document.querySelector<HTMLMetaElement>(
      'meta[name="theme-color"]'
    );
    if (!meta) return;

    // on first ever run, capture the original content
    if (originalThemeColor.current === null) {
      originalThemeColor.current = meta.content;
    }

    // toggle between white (modal open) and the saved original
    meta.content =
      addProduct || createOrder || setUp
        ? "#FFFFFF"
        : originalThemeColor.current;
  }, [addProduct, createOrder, setUp]);

  const handleSocialClose = () => {
    userAuthStore.updateBusinessSocialMedia(true);
  };

  const mutation = useMutation((formData: FormData) =>
    orderServices.editUserProfile(formData)
  );
  const submitSocials = () => {
    const businessData = {
      business_name: businessDetails.business_name,
      business_type: businessDetails.business_type,
      business_phone: businessDetails.business_phone,
      business_website: businessDetails.business_website,
      business_address: businessDetails.business_address,
      business_city: businessDetails.business_city,
      business_state: businessDetails.business_state,
      business_zip_code: businessDetails.business_zip_code,
      social_facebook: socialHandles.social_facebook,
      social_instagram: socialHandles.social_instagram,
      social_twitter: socialHandles.social_twitter,
      social_tiktok: socialHandles.social_tiktok,
    };
    const formData = new FormData();
    formData.append("businessData", JSON.stringify(businessData));
    formData.append("vendorId", userDetails.vendorId);

    mutation.mutate(formData, {
      onSuccess: (response) => {
        toast.success(response?.data?.message);
        console.log("data sent:", formData);
        userAuthStore.updateBusinessSocialMedia(
          response?.data?.businessSocialMedia
        );
        updateBusiness(response?.data?.business);

        handleSocialClose();
      },
      onError: (error: any) => {
        toast.error(error?.response?.data?.error || error?.message);
      },
    });
  };

  return (
    <div className="flex p-[16px] md:mb-[30px] md:p-[0px] flex-col gap-[10px] pb-[30px]">
      {/* Overview Header */}
      <div className="flex flex-col md:flex-row gap-5 md:justify-between w-full ">
        <div className="w-[300px]">
          <h3 className="font-sora leading-[28.8px] text-[20px] md:text-[24px] text-primary-baseBlack font-semibold">
            Your Business Summary
          </h3>
          <p className="font-sora text-primary-neutral800 text-[14px] leading-[22.4px]">
            Here is a summary of your business
          </p>
        </div>

        {/* Date and Download CSV */}
        <div className="flex  md:flex-row w-full md:w-[560px] items-center gap-3">
          <div className="w-full flex-1">
            <DatePicker />
          </div>
          <div className="w-full flex-1">
            {/* <HomeButtons
              title={
                salesReportMutation.isLoading
                  ? 'Downloading File'
                  : 'Download csv'
              }
              textColor="text-primary-neutralst1"
              bgColor="bg-primary-baseWhite"
              border="border-[1px]"
              borderColor="border-primary-neutral200"
              image={DocumentDownload}
              onClick={handleReport}
            /> */}

            <HomeButtons
              bgColor="bg-[#E6E5F9]"
              textColor="text-primary-purple"
              title="+ Add New Product"
              onClick={toggleAddProduct}
            />
          </div>
        </div>
      </div>
      {/* Stats - Cards */}
      <div className="mt-[20px] md:gap-y-2 flex lg:justify-between overflow-x-scroll scrollbar-hide md:overflow-hidden md:flex-wrap gap-2 lg:gap-y-0 lg:flex-nowrap">
        {/* Total Sales */}
        {isLoading ? (
          <CardLoader />
        ) : (
          <div className="border-[1px] flex-grow flex-1 min-w-[265px] w-full border-primary-neutral200 rounded-[16px] bg-primary-baseWhite px-[24px] py-[16px]">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <DollarCircle
                  className="text-[#1976D2] bg-[#E8F2FB] px-[4px] rounded-md"
                  size={32}
                  variant="Bold"
                />
                <h2 className="font-sora text-primary-neutral1000 text-[14px]">
                  Total Sales
                </h2>
              </div>
              {stats?.totalSalesChange >= 0 ? (
                <p className="border-[1px] flex gap-1 items-center text-[12px] border-[#AEEBBC] font-sora text-[#28A745] rounded-xl p-1">
                  {stats?.totalSalesChange}%<ArrowUp size={12} />
                </p>
              ) : (
                <p className="border-[1px] flex gap-1 items-center text-[14px] border-[#F3BCC1] font-sora text-[#DC3545] rounded-xl p-1">
                  {stats?.totalSalesChange}%<ArrowDown size={12} />
                </p>
              )}
            </div>
            <p className="font-sora mt-[15px] text-[24px] text-primary-neutralt1 font-bold">
              ₦ <span>{FormatPrice(stats?.totalSales)}</span>
            </p>
          </div>
        )}

        {/* Avg. Order Value */}
        {isLoading ? (
          <CardLoader />
        ) : (
          <div className="border-[1px] flex-grow flex-1 min-w-[265px] w-full border-primary-neutral200 rounded-[16px] bg-primary-baseWhite px-[24px] py-[16px]">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <ShoppingBag
                  className="text-[#FF9900] bg-[#FFF5E6] px-[4px] rounded-md"
                  size={32}
                  variant="Bold"
                />
                <h2 className="font-sora text-primary-neutral1000 text-[14px]">
                  Avg. Order Value
                </h2>
              </div>
              {stats?.avgOrderValueChange >= 0 ? (
                <p className="border-[1px] flex gap-1 items-center text-[12px] border-[#AEEBBC] font-sora text-[#28A745] rounded-xl p-1">
                  {stats?.avgOrderValueChange}%<ArrowUp size={12} />
                </p>
              ) : (
                <p className="border-[1px] flex gap-1 items-center text-[14px] border-[#F3BCC1] font-sora text-[#DC3545] rounded-xl p-1">
                  {stats?.avgOrderValueChange}%<ArrowDown size={12} />
                </p>
              )}
            </div>
            <p className="font-sora mt-[15px] text-[24px] text-primary-neutralt1 font-bold">
              ₦ <span>{FormatPrice(stats?.avgOrderValue)}</span>
            </p>
          </div>
        )}
        {/* Completed Orders */}
        {isLoading ? (
          <CardLoader />
        ) : (
          <div className="border-[1px] flex-grow flex-1 min-w-[265px] w-full border-primary-neutral200 rounded-[16px] bg-primary-baseWhite px-[24px] py-[16px]">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <BagTick2
                  className="text-[#28A745] bg-[#EAF7ED] px-[4px] rounded-md"
                  size={32}
                  variant="Bold"
                />
                <h2 className="font-sora text-primary-neutral1000 text-[14px]">
                  Completed Orders
                </h2>
              </div>
              {stats?.completedOrdersChange >= 0 ? (
                <p className="border-[1px] flex gap-1 items-center text-[12px] border-[#AEEBBC] font-sora text-[#28A745] rounded-xl p-1">
                  {stats?.completedOrdersChange}%<ArrowUp size={12} />
                </p>
              ) : (
                <p className="border-[1px] flex gap-1 items-center text-[14px] border-[#F3BCC1] font-sora text-[#DC3545] rounded-xl p-1">
                  {stats?.completedOrdersChange}%<ArrowDown size={12} />
                </p>
              )}
            </div>
            <p className="font-sora mt-[15px] text-[24px] text-primary-neutralt1 font-bold">
              <span>{stats?.completedOrders}</span>
            </p>
          </div>
        )}
        {/* Available Products */}
        {isLoading ? (
          <CardLoader />
        ) : (
          <div className="border-[1px] flex-grow flex-1 min-w-[265px] border-primary-neutral200 rounded-[16px] bg-primary-baseWhite px-[24px] py-[16px]">
            <div className="flex items-center gap-2">
              <Box
                className="text-[#855121] bg-[#F3EEE9] px-[4px] rounded-md"
                size={32}
                variant="Bold"
              />
              <h2 className="font-sora text-primary-neutral1000 text-[14px]">
                Available Products (Units)
              </h2>
            </div>
            <p className="font-sora mt-[15px] text-[24px] text-primary-neutralt1 font-bold">
              <span>{stats?.availableProducts}</span>
            </p>
          </div>
        )}
      </div>
      {/* Share Storefront Link Section */}
      <div
        className={`w-full my-[30px] border-[1px] ${
          businessDetails?.website_status === "Active"
            ? "bg-[#EAF7ED] border-[#28A745]"
            : "bg-[#FFF2DF] border-[#FF9900]"
        } rounded-[16px] p-[24px] flex-col md:flex-row flex items-center justify-between`}
      >
        <div>
          <h5 className="font-sora text-[18px] mb-[10px] font-semibold leading-[30px] md:leading-[21.6px] text-primary-baseBlack">
            {businessDetails?.website_status === "Active"
              ? "Your Website is Live!"
              : newAccount
              ? "Your Website is Off. Turn It On to Start Selling!"
              : "Your Website is Off. Turn It On to Start Selling!"}
          </h5>
          <p className="text-primary-neutral1000 font-sora text-[14px]">
            {businessDetails?.website_status === "Active"
              ? "Copy the link to share with customers, to place orders directly."
              : "Start selling online and reach a wider audience."}
          </p>
        </div>
        <div className="w-full lg:w-fit gap-2 mt-3 md:mt-[0px] flex items-center rounded-3xl md:pl-[24px] pl-[10px] justify-between border-[1px] border-primary-neutral200 bg-primary-baseWhite relative">
          <a
            id="link-to-copy"
            href={
              userStatus === "PremiumUser"
                ? `https://${businessWebsite}`
                : `https://${businessDetails.subdomain}.huxxle.shop`
            }
            target="_blank"
            className="font-sora break-all text-primary-neutral1000 text-[10px]"
          >
            {userStatus === "PremiumUser"
              ? `${businessWebsite}`
              : `https://${businessDetails.subdomain}.huxxle.shop`}
          </a>

          <button
            onClick={copyLink}
            className="bg-primary-baseWhite flex gap-2 items-center py-[12px] px-[16px] text-primary-neutral1000 border-[1px] font-semibold rounded-3xl"
          >
            {isCopied ? (
              <TickCircle size={16} color="#28A745" />
            ) : (
              <Copy size={16} />
            )}
            <span className="hidden md:block">
              {isCopied ? "Link Copied!" : `${copy}`}
            </span>
          </button>
        </div>
        {/* Toggle Button & Tooltip */}
        <div
          onClick={newAccount ? handleSetUp : handleToggleClick}
          className={`border-[2px] mt-4 md:mt-0 w-full md:w-fit justify-between gap-2 cursor-pointer flex items-center rounded-3xl p-3 ${
            businessDetails?.website_status === "Active"
              ? "bg-[#EAF7ED] border-[#28A745]"
              : "bg-[#FFF2DF] border-[#FF9900]"
          }  relative`}
        >
          {newAccount && (
            <p className="font-semibold font-sora text-[#2A2A2A] text-[14px]">
              Set-Up Website
            </p>
          )}
          {!newAccount && (
            <p className="font-semibold font-sora text-[#2A2A2A] text-[14px]">
              {businessDetails?.website_status === "Active"
                ? "Website Active"
                : "Turn On Your Website"}
            </p>
          )}

          <button
            type="button"
            disabled={
              businessDetails?.website_status_by_admin?.toLowerCase() === "yes"
            }
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out ${
              isEnabled ? "bg-blue-600" : "bg-gray-200"
            } ${
              businessDetails?.website_status_by_admin?.toLowerCase() === "yes"
                ? "cursor-default opacity-50"
                : ""
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out ${
                isEnabled ? "translate-x-6" : "translate-x-1"
              }`}
            />
          </button>

          {businessDetails?.website_status_by_admin?.toLowerCase() ===
            "yes" && (
            <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-3 py-1 text-[10px] text-primary-neutral1000 bg-white border border-gray-200 rounded-md shadow whitespace-nowrap">
              Deactivated by the admin
              <div className="absolute top-full left-1/2 -translate-x-1/2 -mt-px border-4 border-transparent border-t-gray-900" />
            </div>
          )}
        </div>
      </div>
      {/* Tooltip for new users */}
      {newAccount && !isEnabled && showNewUserTooltip && (
        <>
          {/* overlay */}
          <div className="fixed inset-0 bg-black/5  backdrop-blur-[2px] z-40" />

          {/* centred card wrapper */}
          <div className="fixed inset-0 flex items-center px-4 justify-center z-50">
            <div className="bg-gradient-to-br w-[480px] from-white to-[#EEEEEE] p-[6px] rounded-md">
              <div className="bg-white rounded-md p-4 md:p-6 w-full text-left">
                <p className="font-sora text-[12px] text-[#7B7B7B] mb-4">
                  COMPLETE WEBSITE SETUP
                </p>
                <h3 className="font-sora text-[16px] font-semibold text-[#212529] mb-2">
                  Welcome to Huxxle! 👋
                </h3>
                <p className="font-sora text-[14px] text-[#7B7B7B] mb-6">
                  Let’s quickly set up your website in just a few minutes. Your
                  customers are waiting.
                </p>
                <div className="flex items-center gap-4 justify-start">
                  <button
                    onClick={() => {
                      setShowNewUserTooltip(false);
                      handleSetUp();
                    }}
                    className="px-4 h-[41px] bg-[#4f4cd8] text-white text-[12px] rounded-xl font-sora"
                  >
                    Setup Website
                  </button>
                  <button
                    onClick={() => setShowNewUserTooltip(false)}
                    className="px-4 h-[41px] border border-[#DCDCDC] text-[#2A2A2A] text-[12px] rounded-xl font-sora"
                  >
                    Not Now
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Quick Action */}
      <div className="flex-col flex md:flex-row justify-between gap-[10px]">
        {/* Left */}
        <div className="flex-1 w-full flex flex-col gap-[35px]">
          {/* Quick Action Card */}
          <div className="border-[1px] bg-primary-baseWhite flex flex-col gap-5 border-x-primary-neutral200 rounded-2xl py-6 px-[16px]">
            <div className="flex z-0 flex-col gap-3 md:gap-0 md:flex-row justify-between md:items-center">
              <div className="w-[376px]">
                <h5 className="font-sora text-[18px] font-semibold leading-[21.6px] text-primary-baseBlack">
                  Quick Action
                </h5>
                <p className="text-[#9B9B9B] mt-[8px] font-sora text-[14px]">
                  Take a look at the following and act on them
                </p>
              </div>
              <div className="hidden md:block">
                <HomeButtons
                  bgColor="bg-primary-purple"
                  textColor="text-primary-baseWhite"
                  title="Create Order"
                  onClick={handleCreateOrder}
                />
              </div>
            </div>
            {isLoading ? (
              <div className="flex flex-col md:flex-row gap-2 md:gap-4 justify-between w-full h-full">
                <div className="bg-slate-200 animate-pulse w-full overflow-hidden relative flex flex-col gap-2 p-4 rounded-2xl h-full"></div>
                <div className="bg-slate-200 animate-pulse w-full overflow-hidden relative flex flex-col gap-2 p-4 rounded-2xl h-full"></div>
              </div>
            ) : (
              <div>
                {stats?.runningLowProducts === 0 &&
                stats.outOfStockProducts === 0 ? (
                  <div className="flex flex-1 text-center justify-center items-center h-auto">
                    <div>
                      {stats?.availableProducts > 0 ? (
                        <h4 className="text-[#28A745] font-bold font-sora text-[14px]">
                          Yaay!!! Your inventory is in excellent condition!
                        </h4>
                      ) : (
                        <div className="flex flex-col md:flex-row gap-2 md:gap-4 justify-between h-[250px] w-[346px] md:w-full md:h-full">
                          <div className="bg-[#FCF7EF] overflow-hidden relative flex flex-col gap-2 p-4 rounded-2xl h-full">
                            <p className="flex items-center gap-4 font-sora text-[14px] text-primary-neutral1000">
                              <Box
                                variant="Bold"
                                color="white"
                                className="bg-[#FF9900] p-[4px] rounded-md"
                              />
                              Running Low
                            </p>
                            <p className="font-sora text-primary-baseBlack text-[14px]">
                              {stats?.runningLowProducts} Products are almost
                              out of stock
                            </p>
                            <p className="absolute font-lora -bottom-[25px] right-4 text-[#FF9900] text-[144px] font-bold opacity-20 leading-none select-none">
                              {stats?.runningLowProducts}
                            </p>
                          </div>
                          <div className="bg-[#F9E8EA] overflow-hidden relative flex flex-col gap-2 p-4 rounded-2xl h-full">
                            <p className="flex items-center gap-4 font-sora text-[14px] text-primary-neutral1000">
                              <Box
                                variant="Bold"
                                color="white"
                                className="bg-[#DC3545] p-[4px] rounded-md"
                              />
                              Out of Stock
                            </p>
                            <p className="font-sora text-primary-baseBlack text-[14px]">
                              {stats?.outOfStockProducts} Products are out of
                              stock
                            </p>
                            <p className="absolute -bottom-[30px] font-lora right-0 text-[#DC3545] text-[144px] font-bold opacity-20 leading-none select-none">
                              {stats?.outOfStockProducts}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col md:flex-row gap-2 md:gap-4 justify-between h-[250px] w-[346px] md:w-full md:h-full">
                    <div className="bg-[#FCF7EF] overflow-hidden relative flex flex-col gap-2 p-4 rounded-2xl h-full">
                      <p className="flex items-center gap-4 font-sora text-[14px] text-primary-neutral1000">
                        <Box
                          variant="Bold"
                          color="white"
                          className="bg-[#FF9900] p-[4px] rounded-md"
                        />
                        Running Low
                      </p>
                      <p className="font-sora text-primary-baseBlack text-[14px]">
                        {stats?.runningLowProducts} Products are almost out of
                        stock
                      </p>
                      <p className="absolute font-lora -bottom-[25px] right-4 text-[#FF9900] text-[144px] font-bold opacity-20 leading-none select-none">
                        {stats?.runningLowProducts}
                      </p>
                    </div>
                    <div className="bg-[#F9E8EA] overflow-hidden relative flex flex-col gap-2 p-4 rounded-2xl h-full">
                      <p className="flex items-center gap-4 font-sora text-[14px] text-primary-neutral1000">
                        <Box
                          variant="Bold"
                          color="white"
                          className="bg-[#DC3545] p-[4px] rounded-md"
                        />
                        Out of Stock
                      </p>
                      <p className="font-sora text-primary-baseBlack text-[14px]">
                        {stats?.outOfStockProducts} Products are out of stock
                      </p>
                      <p className="absolute -bottom-[30px] font-lora right-0 text-[#DC3545] text-[144px] font-bold opacity-20 leading-none select-none">
                        {stats?.outOfStockProducts}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="mt-6 md:hidden mb-4">
              <HomeButtons
                bgColor="bg-primary-purple"
                textColor="text-primary-baseWhite"
                title="Create Order"
                onClick={handleCreateOrder}
              />
            </div>
          </div>

          {/* Top Performing Products */}
          <div className="border-[1px] bg-primary-baseWhite flex flex-col gap-5 border-x-primary-neutral200 rounded-2xl p-6">
            <div className="flex flex-col gap-3 md:gap-0 md:flex-row justify-between md:items-start">
              <div className="w-full">
                <div className="flex w-full justify-between gap-16 items-center">
                  <h5 className="font-sora text-[18px] font-semibold leading-[21.6px] text-primary-baseBlack">
                    Best Sellers
                  </h5>
                  <div className="hidden md:block">
                    <Link to={RouteNames.analytics}>
                      <HomeButtons
                        bgColor=""
                        textColor="text-primary-baseBlack"
                        title="View All Items"
                        border="border-[1px]"
                        borderColor="border-primary-neutral200"
                      />
                    </Link>
                  </div>
                </div>
                <p className="text-[#9B9B9B] mt-[8px] font-sora text-[14px]">
                  See your best selling products here
                </p>
              </div>
            </div>
            {isLoading ? (
              <div className="flex flex-col md:flex-row gap-2 md:gap-4 justify-between w-full h-full">
                <div className="bg-slate-200 animate-pulse w-full overflow-hidden relative flex flex-col gap-2 p-4 rounded-2xl h-full"></div>
                <div className="bg-slate-200 animate-pulse w-full overflow-hidden relative flex flex-col gap-2 p-4 rounded-2xl h-full"></div>
              </div>
            ) : (
              <div>
                {stats?.topProducts.length === 0 ? (
                  <div className="flex flex-col w-full">
                    <div
                      className="h-[36px] mt-2 bg-[#B4B2EE]"
                      style={{ width: "100%" }}
                    ></div>
                    <div className="flex mt-[20px] gap-2 items-center">
                      <span className="h-2 w-2 rounded-full bg-[#B4B2EE]"></span>
                      <div className="flex flex-col">
                        <p className="font-sora text-[14px] font-semibold text-primary-baseBlack">
                          No Products Yet
                        </p>
                        <p className="text-primary-neutral1000 text-[12px]">
                          0 units sold
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col md:flex-row w-full">
                    {stats?.topProducts.map(
                      (product: TopPerformers, index: number) => {
                        const widthPercentage =
                          (product.unitsSold / maxUnitsSold) * 100;
                        const color = colors[index % colors.length];
                        return (
                          <div
                            key={product.productId}
                            className="flex flex-col w-full"
                          >
                            <div className="flex items-center gap-2"></div>
                            <div
                              className="h-[36px] mt-2"
                              style={{
                                width: `${widthPercentage}%`,
                                backgroundColor: color,
                              }}
                            ></div>
                            <div className="flex mt-[20px] gap-2 items-center">
                              <span
                                className="h-2 w-2"
                                style={{ backgroundColor: color }}
                              ></span>
                              <div className="flex flex-col">
                                <p className="font-sora text-[14px] text-primary-baseBlack">
                                  {product.productName}
                                </p>
                                <p className="text-primary-neutral1000 text-[12px]">
                                  {product.unitsSold} units sold
                                </p>
                              </div>
                            </div>
                          </div>
                        );
                      }
                    )}
                  </div>
                )}
              </div>
            )}

            <div className="mt-6 md:hidden mb-4">
              <Link
                to={RouteNames.analytics}
                className="w-full flex justify-center"
              >
                <HomeButtons
                  bgColor="bg-white"
                  textColor="text-primary-baseBlack"
                  title="View All Items"
                  border="border-[1px]"
                  borderColor="border-primary-neutral200"
                  customStyles="w-full text-center py-2 px-4 rounded-xl hover:bg-gray-100"
                />
              </Link>
            </div>
          </div>
        </div>

        {/* Right: Recent Orders */}
        <div className="bg-primary-baseWhite w-full border-[1px] flex-1 flex flex-col justify-between border-x-primary-neutral200 rounded-2xl p-6">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col md:flex-row gap-2 md:gap-10 justify-between md:items-start">
              <div className="flex flex-col md:flex-row justify-between  md:justify-between w-full gap-4 md:gap-7">
                <div className="flex flex-col justify-start md:justify-center">
                  <h5 className="font-sora text-[18px] font-semibold text-primary-baseBlack">
                    Your Customer Orders
                  </h5>
                  <p className="text-[#9B9B9B] font-sora text-[14px]">
                    See your latest customer orders
                  </p>
                </div>
                <div className="hidden md:block">
                  <Link to={RouteNames.order}>
                    <button className="flex items-center justify-center gap-2 px-4 py-2 border border-primary-neutral200 rounded-2xl text-primary-baseBlack font-sora text-[14px] font-medium">
                      View All Orders
                      <span className="text-lg">→</span>
                    </button>
                  </Link>
                </div>
              </div>
            </div>
            {isLoading ? (
              <div className="flex flex-col animate-pulse gap-3">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div
                    key={index}
                    className="flex w-full justify-between pb-[12px] items-center"
                  >
                    <div className="flex gap-3 items-center">
                      <div className="w-[30px] animate-pulse object-contain rounded-full bg-slate-200 h-[30px]"></div>
                      <div className="flex flex-col gap-2">
                        <p className="text-[10px] w-[60px] rounded-full h-[7px] bg-slate-200 font-light text-primary-purple font-sora"></p>
                        <p className="text-[10px] w-[200px] rounded-full h-[7px] bg-slate-200 font-light text-primary-purple font-sora"></p>
                        <p className="text-[10px] w-[60px] rounded-full h-[7px] bg-slate-200 font-light text-primary-purple font-sora"></p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div>
                {stats?.recentOrders.length === 0 ? (
                  <div className="flex flex-1 text-center justify-center items-center h-[300px]">
                    <div className="flex flex-col items-center justify-center text-center mt-8 space-y-4">
                      <img
                        src={orderEmptyLogo}
                        alt="No Data"
                        className="w-16 h-16"
                      />
                      <p className="text-sm font-sora text-[#5B5B5B]">
                        You currently have no orders. Once you have orders, they
                        will appear here.
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="flex mt-8 flex-col justify-between gap-4">
                    {stats?.recentOrders
                      .slice(0, 4)
                      .map(
                        (
                          i: RecentOrder,
                          index: number,
                          slicedData: RecentOrder[]
                        ) => (
                          <div
                            key={i.orderId}
                            className={`flex w-full justify-between pb-[12px] items-center ${
                              index !== slicedData.length - 1
                                ? "border-b-[1px] border-primary-neutral200"
                                : "pb-[0px]"
                            }`}
                          >
                            <div className="flex gap-3 items-center">
                              <img
                                className="w-[56px] object-contain rounded-2xl h-[56px]"
                                src={i.productImage}
                                onError={(e: any) => {
                                  e.target.src = thumbnail;
                                }}
                                alt={i.productName}
                              />
                              <div>
                                <p className="text-[10px] font-light text-primary-purple font-sora">
                                  {i.orderId}
                                </p>
                                <p className="font-sora text-primary-neutralt1 font-semibold leading-[16.8px] text-[14px]">
                                  {i.productName}
                                </p>
                                <p className="text-[12px] font-sora text-primary-neutral800 leading-[19.2px]">
                                  Total of {i.totalItems}{" "}
                                  {i.totalItems > 1 ? "items" : "item"}
                                </p>
                              </div>
                            </div>
                            <div className="flex flex-col text-right">
                              <h3 className="font-bold text-[16px] text-primary-neutralt1 leading-[19.2px]">
                                ₦ {FormatPrice(i.totalAmount)}
                              </h3>
                              <p
                                className={`
                                text-sm font-semibold mt-[5px]
                                ${
                                  i.orderStatus === "Paid" ||
                                  i.orderStatus === "Shipped"
                                    ? "text-[#28A745] text-center bg-[#EAF7ED] px-[16px] py-[4px] rounded-3xl"
                                    : ""
                                }
                                ${
                                  i.orderStatus === "Delivered"
                                    ? "text-primary-purple text-center bg-primary-purple200 px-[16px] py-[4px] rounded-3xl"
                                    : ""
                                }
                                ${
                                  i.orderStatus === "Cancelled"
                                    ? "text-[#DC3545] text-center bg-[#FBEBEC] px-[16px] py-[4px] rounded-3xl"
                                    : ""
                                }
                                ${
                                  i.orderStatus === "Not Yet Paid" ||
                                  i.orderStatus === "Pending"
                                    ? "text-[#FFC107] text-center bg-[#FFF9E6] px-[16px] py-[4px] rounded-3xl"
                                    : ""
                                }
                              `}
                              >
                                {i.orderStatus}
                              </p>
                            </div>
                          </div>
                        )
                      )}
                  </div>
                )}
              </div>
            )}
            {stats?.recentOrders.length > 0 && (
              <div className="mt-6 md:hidden mb-4">
                <Link
                  to={RouteNames.order}
                  className="w-full flex justify-center"
                >
                  <HomeButtons
                    bgColor="bg-white"
                    textColor="text-primary-baseBlack"
                    title="View All Orders"
                    border="border-[1px]"
                    borderColor="border-primary-neutral200"
                    customStyles="w-full text-center py-2 px-4 rounded-xl hover:bg-gray-100"
                  />
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* AI Recommendation and MOM Chart */}
      <div className="flex flex-col md:flex-row justify-between max-h-[450px] mb-[500px] md:mb-[30px] gap-[24px] w-full">
        {/* Month on Month Sales */}
        <div className="border-[1px] flex-1 flex flex-col p-[24px] border-x-primary-neutral200 rounded-2xl">
          <h3 className="text-[18px] font-semibold text-primary-neutralt1 font-sora">
            Month on Month Revenue
          </h3>
          {isLoading ? (
            <div>
              <p className="text-[10px] mt-[30px] w-[300px] rounded-full h-[10px] bg-slate-200 font-light text-primary-purple font-sora"></p>
              <div className="mt-auto">
                <p className="text-[10px] mt-[30px] h-[300px] w-[500px] bg-slate-200 font-light text-primary-purple font-sora"></p>
              </div>
            </div>
          ) : (
            <div>
              <p className="mt-[30px] text-[#1E1B39] font-sora font-bold text-[28px] leading-[33.6px]">
                ₦ {FormatPrice(stats?.totalSales)}
              </p>
              <div className="mt-auto flex flex-col">
                <div className="flex-1 mt-auto max-h-[300px] max-w-[600px] overflow-hidden">
                  <LineChart salesOverTime={stats?.salesOverTime} />
                </div>
              </div>
            </div>
          )}
        </div>
        <PaginationTables
          data={recommendations}
          isLoading={isLoading}
          recommendations={recommendations}
        />
      </div>
      {addProduct && (
        <AddProduct
          isOpen={addProduct}
          closeModal={toggleAddProduct}
          refetch={refetch}
        />
      )}
      {createOrder && (
        <CreateOrder
          refetch={refetch}
          isOpen={createOrder}
          closeModal={handleCreateOrder}
        />
      )}
      {showActivateModal && (
        <ConfirmationModal
          isOpen={showActivateModal}
          title="Are you sure you want to activate?"
          text="This action will make your website active for customers to use and send in orders."
          actionButtonText={
            activateMutation.isLoading ? "Activating ..." : "Activate"
          }
          cancelButtonText="No, Cancel"
          onActionClick={handleActivateConfirm}
          onCancelClick={() => setShowActivateModal(false)}
          actionButtonStyle="bg-[#4F4CD8]"
          actionButtonDisabled={activateMutation.isLoading}
        />
      )}
      {showDeactivateModal && (
        <ConfirmationModal
          isOpen={showDeactivateModal}
          title="Are you sure you want to Deactivate?"
          text="This action will make your website not accessible for customers to use and send in orders."
          actionButtonText={
            activateMutation.isLoading ? "Deactivating ..." : "Deactivate"
          }
          cancelButtonText="No, Cancel"
          onActionClick={handleDeactivateConfirm}
          onCancelClick={() => setShowDeactivateModal(false)}
          actionButtonStyle="bg-[#4F4CD8]"
          actionButtonDisabled={activateMutation.isLoading}
        />
      )}
      {setUp && (
        <AddBankDetails
          isOpen={activateBank}
          closeModal={closeBankActivation}
          closeSetup={closeBankActivation}
        />
      )}

      {/* social form */}
      {!socialStatus && (!newAccount || !showNewUserTooltip) && (
        <div className="fixed inset-0 bg-black/5  backdrop-blur-[2px] z-40">
          <div className="fixed inset-0 flex items-center px-4 justify-center z-50">
            <div className="bg-gradient-to-br w-[480px] from-white to-[#EEEEEE] p-[6px] rounded-md">
              <div className="bg-white border-l-[4px] border-primary-purple rounded-md p-4 md:p-6 w-full text-left flex flex-col gap-4">
                <div className="flex items-center justify-between w-full">
                  <p className="font-sora text-[14px] text-[#7b7b7b]">
                    Achieve More By Adding Your Social Media Handles
                  </p>
                  <button
                    onClick={handleSocialClose}
                    className="flex justify-end "
                  >
                    <CloseSquare />
                  </button>
                </div>
                <div>
                  <div className="flex flex-col md:flex-row w-full md:flex-wrap gap-4">
                    <InputField
                      placeholder="Enter your Instagram"
                      label="Instagram"
                      name="instagram"
                      value={socialHandles.social_instagram}
                      onChange={handleSocialMediaInput}
                    />
                    <InputField
                      placeholder="Enter your Twitter (X)"
                      label="Twitter (X)"
                      name="twitter"
                      value={socialHandles.social_twitter}
                      onChange={handleSocialMediaInput}
                    />
                    <InputField
                      placeholder="Enter your TikTok"
                      label="TikTok"
                      name="tiktok"
                      value={socialHandles.social_tiktok}
                      onChange={handleSocialMediaInput}
                    />
                    <InputField
                      placeholder="Enter your Facebook"
                      label="Facebook"
                      name="facebook"
                      value={socialHandles.social_facebook}
                      onChange={handleSocialMediaInput}
                    />

                    <button
                      type="submit"
                      onClick={submitSocials}
                      disabled={mutation.isLoading}
                      className="text-white w-full flex justify-center items-center rounded-2xl font-sora bg-primary-purple h-[49px]"
                    >
                      {mutation.isLoading ? (
                        <p className="animate-spin">
                          {" "}
                          <FaSpinner />{" "}
                        </p>
                      ) : (
                        " Submit"
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Overview;
