// import { create } from 'zustand';


//  interface Customer {
//     id: string;
//     customerName: string;
//     phoneNumber: string;
//     email: string;
//     avgSpendPerOrder: number;
//     dateAdded: string;
//     status: string;
// }

//  interface CustomerState {
//     customers: Customer[];
//     addCustomer: (customer: Customer) => void;
//     removeCustomer: (customerId: string) => void;
//     updateCustomer: (customer: Customer) => void;
// }

// const useCustomerStore = create<CustomerState>((set) => ({
//     customers: [],
//     addCustomer: (customer) => set((state) => ({
//         customers: [...state.customers, customer],
//     })),

//     removeCustomer: (customerId) => set((state) => ({
//         customers: state.customers.filter(customer => customer.id !== customerId),
//     })),

//     updateCustomer: (updatedCustomer) => set((state) => ({
//         customers: state.customers.map(customer =>
//             customer.id === updatedCustomer.id ? updatedCustomer : customer
//         ),
//     })),
// }));

// export default useCustomerStore



import { create } from 'zustand';

interface Customer {
    customerId: string;
    customerName: string;
    customerEmail: string;
    customerPhone: string;
}

interface CustomerStore {
    customer: Customer | null;
    setCustomer: (customer: Customer) => void;
}

const useCustomerStore = create<CustomerStore>((set) => ({
    customer: null,
    setCustomer: (customer: Customer) => set({ customer }),
}));

export default useCustomerStore;
