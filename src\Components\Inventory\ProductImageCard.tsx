interface ProductImageCardProps {
  imageUrl: string;
  imageName: string;
  imageSize: string;
  imageNumber: number;
  isCover: boolean;
  onSetCover: () => void;
  onRemove: () => void;
  isVideo?: boolean;          // Optional flag to indicate if this is a video
  videoThumbnail?: string;    // Thumbnail URL to use if it's a video
}

const ProductImageCard: React.FC<ProductImageCardProps> = ({
  imageUrl,
  imageName,
  imageSize,
  onSetCover,
  onRemove,
  isCover,
  imageNumber,
  isVideo = false,
  videoThumbnail,
}) => {
  return (
    <div>
      <div className="flex justify-between">
        <p>Product Image {imageNumber}</p>
        <div className="flex items-center space-x-2">
          <input
            type="radio"
            className="form-radio h-4 w-4 text-blue-600"
            checked={isCover}
            onChange={onSetCover}
          />
          <label className="text-sm">Set as cover</label>
        </div>
      </div>
      <div className="flex items-center justify-between w-full p-4 shadow-sm rounded-lg border border-[#CCCCCC] my-4">
        <div className="flex items-center space-x-4">
          <img
            src={isVideo && videoThumbnail ? videoThumbnail : imageUrl}
            alt={imageName}
            className="w-16 h-16 rounded-lg object-cover"
          />
          <div>
            <p className="text-sm font-medium truncate max-w-36">{imageName}</p>
            <p className="text-xs text-gray-500">{imageSize}</p>
          </div>
        </div>
        <div>
          <span
            onClick={onRemove}
            className="text-base font-bold cursor-pointer my-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <button
            type="button"
            onClick={onRemove}
            className="text-red-500 text-sm hover:text-red-600 transition"
          >
            Remove
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductImageCard;
