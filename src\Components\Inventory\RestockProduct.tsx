/* eslint-disable @typescript-eslint/no-explicit-any */
import { MessageText1, Tick<PERSON>ir<PERSON>, Whatsapp } from "iconsax-react";
import InputField from "../../Constants/InputField";
import { useEffect, useState } from "react";
import PreviewRestock from "./PreviewRestock";
import { Link } from "react-router-dom";

interface Product {
  productId: string;
  itemID: string;
  userEmail: string;
  purchasePrice: string;
  sellingPrice: string;
  stockLevel: string;
  supplierName: string;
  supplierPhone: string;
  supplierEmail: string;
}
interface Preview {
  productId: string;
  itemID: string;
  userEmail: string;
  purchasePrice: string;
  sellingPrice: string;
  stockLevel: string;
}

const RestockProduct = ({
  onClose,
  isOpen,
  viewingId,
  refetch,
  productDetails,
  isError,
  productRefetch,
  stockRefetch,
  oldStock,
}: any) => {
  const [isPreview, setIsPreview] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [product, setProduct] = useState<Product>({
    productId: "",
    itemID: "",
    userEmail: "",
    purchasePrice: "",
    sellingPrice: "",
    stockLevel: "",
    supplierName: "",
    supplierPhone: "",
    supplierEmail: "",
  });
  const [previewData, setPreviewData] = useState<Preview>({
    productId: "",
    itemID: "",
    userEmail: "",
    purchasePrice: "",
    sellingPrice: "",
    stockLevel: "",
  });
  useEffect(() => {
    if (productDetails) {
      setProduct({
        ...productDetails.data,
      });
    }
  }, [productDetails]);
        console.log("product:", product);

  const areRequiredFieldsFilled = () => {
    const { stockLevel } = previewData;
    return stockLevel;
  };
  const handleBack = () => {
    setIsPreview(false);
  };
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setPreviewData((prevDetails) => ({
      ...prevDetails,
      [name]: value,
    }));

    if (name === "purchasePrice" || name === "sellingPrice") {
      const updatedPurchasePrice =
        name === "purchasePrice" ? +value : +previewData.purchasePrice;
      const updatedSellingPrice =
        name === "sellingPrice" ? +value : +previewData.sellingPrice;

      if (updatedSellingPrice < updatedPurchasePrice) {
        setErrorMessage("Selling price cannot be less than purchase price");
        console.log("Selling price cannot be less than purchase price");
      } else {
        setErrorMessage(null);
      }
    }
  };
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setPreviewData(previewData);
    if (areRequiredFieldsFilled() && !errorMessage) {
      setIsPreview(true);
    }
  };
  const handleEmailSupplier = () => {
    if (product.supplierEmail) {
      window.location.href = `mailto:${product.supplierEmail}?subject=Restock%20Needed`;
    }
  };

  console.log("productDetails:", productDetails);

  return (
    <div className="fixed font-sora  top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-700 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-5 transition-transform duration-700 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={onClose}
            className="close-drawer text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]"
          >
            &times;
          </span>
          {isError ? (
            <div>
              <h2>An error occured, please refresh the page</h2>
            </div>
          ) : product ? (
            <div className="overflow-y-auto h-screen scrollbar-none hidescroll">
              {!isPreview ? (
                <form onSubmit={handleSubmit}>
                  <div className="pl-3 ">
                    <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                      Restock Product
                    </h2>
                    <p className=" text-sm text-[#919191] font-sora ">
                      Provide the Information below to restock product{" "}
                    </p>
                  </div>

                  <div>
                    <div className="p-6 ml-0 md:ml-2 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6 last:mb-[200px]">
                      <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
                        Supplier Information
                      </h4>
                      <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
                        <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                          Supplier Name
                        </p>
                        <p className="text-sm font-sora font-normal text-[#5b5b5b]">
                          {product.supplierName ||
                            productDetails.supplierName ||
                            "-"}{" "}
                        </p>
                      </div>
                      <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
                        <p className="text-xs font-sora font-normal text-[#5b5b5b]">
                          Contact Info
                        </p>
                        <p className="text-sm font-sora font-normal text-[#4F4CD8] ">
                          {product.supplierPhone || productDetails.supplierPhone || "-"}
                        </p>
                      </div>
                      <div className="pb-4  flex flex-col gap-2">
                        <div className="flex flex-col md:flex-row items-center w-full gap-5">
                          <Link
                            target="_blank"
                            to={
                              product.supplierPhone || productDetails.supplierPhone
                                ? `https://wa.me/${product?.supplierPhone || productDetails?.supplierPhone}`
                                : ""
                            }
                            className="flex items-center w-full bg-[#28A745] text-white py-4 px-6 rounded-[24px] text-sm font-sora gap-2 flex-1 justify-center"
                          >
                            <Whatsapp size="20" color="#FCFCFC" />
                            <span> Text on Whatsapp</span>
                          </Link>
                          <Link
                            to="#"
                            onClick={handleEmailSupplier}
                            className="text-[#2A2A2A] w-full py-4 px-6 justify-center rounded-[24px] flex items-center gap-2 text-sm font-sora bg-[#FCFCFC] border border-[#4f4cd8] flex-1"
                          >
                            <MessageText1 size="20" color="#2A2A2A" />{" "}
                            <span>Send an email</span>
                          </Link>
                        </div>
                      </div>
                    </div>

                    <div className="p-6 rounded-[16px] my-5 flex flex-col gap-6 mb-44">
                      <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
                        Restock Details
                      </h4>

                      <InputField
                        label=" Restock Quantiy"
                        placeholder="Enter the amount of units to be restocked"
                        name="stockLevel"
                        type="number"
                        value={previewData.stockLevel}
                        onChange={handleChange}
                        onKeyDown={(e) => {
                          const allowedKeys = ["Backspace", "Tab", "ArrowLeft", "ArrowRight"];
                          if (
                            !/[0-9]/.test(e.key) &&
                            !allowedKeys.includes(e.key)
                          ) {
                            e.preventDefault();
                          }
                        }}
                      />

                      <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
                        Update Price Information
                      </h4>

                      <InputField
                        label=" Update Purchase Price per unit (Optional)"
                        placeholder="Enter the amount you purchased a unit"
                        name="purchasePrice"
                        type="number"
                        value={previewData.purchasePrice}
                        onChange={handleChange}
                        onKeyDown={(e) => {
                          const allowedKeys = ["Backspace", "Tab", "ArrowLeft", "ArrowRight"];
                          if (
                            !/[0-9]/.test(e.key) &&
                            !allowedKeys.includes(e.key)
                          ) {
                            e.preventDefault();
                          }
                        }}
                        price={true}
                      />
                      <div className="flex text-[#28A745] text-xs items-center gap-1.5 -mt-4">
                        <TickCircle size="16" color="#28A745" />
                        <p>
                          Current purchase price is ₦
                          {product?.purchasePrice?.toLocaleString()}
                        </p>
                      </div>
                      <InputField
                        label="Update Selling Price per unit (Optional)"
                        placeholder="Enter the amount you purchased a unit"
                        name="sellingPrice"
                        type="number"
                        value={previewData.sellingPrice}
                        onChange={handleChange}
                        onKeyDown={(e) => {
                          const allowedKeys = ["Backspace", "Tab", "ArrowLeft", "ArrowRight"];
                          if (
                            !/[0-9]/.test(e.key) &&
                            !allowedKeys.includes(e.key)
                          ) {
                            e.preventDefault();
                          }
                        }}
                        price={true}
                      />
                      <div className="flex text-[#28A745] text-xs items-center gap-1.5 -mt-4">
                        <TickCircle size="16" color="#28A745" />
                        <p>
                          Current purchase price is ₦
                          {product?.sellingPrice?.toLocaleString()}
                        </p>
                      </div>
                      {errorMessage && (
                        <p className="text-[#DC3545] border border-[#DC3545] bg-[#FAF2F2] py-1 px-2.5 rounded-full text-xs font-sora">
                          {errorMessage}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2  md:p-5">
                    <div className="flex w-full px-7.5 gap-2.5">
                      <button
                        onClick={onClose}
                        className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2"
                      >
                        Back
                      </button>
                      <button
                        type="submit"
                        disabled={!areRequiredFieldsFilled() || !!errorMessage}
                        className={` w-full text-[#fcfcfc] bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 h-[49px] rounded-2xl cursor-pointer flex-2 ${
                          !areRequiredFieldsFilled() || !!errorMessage
                            ? " opacity-70 cursor-default"
                            : ""
                        }`}
                      >
                        Save & Preview
                      </button>
                    </div>
                  </div>
                </form>
              ) : (
                <PreviewRestock
                  onBack={handleBack}
                  productDetails={previewData}
                  oldStockPrice={product.stockLevel || productDetails.stockLevel}
                  productId={viewingId}
                  itemID={product.itemID || productDetails.itemID}
                  onClose={onClose}
                  refetch={refetch}
                  productRefetch={productRefetch}
                  stockRefetch={stockRefetch}
                  oldStock={oldStock}
                />
              )}
            </div>
          ) : (
            <div>
              <h2>No product details available</h2>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
export default RestockProduct;
