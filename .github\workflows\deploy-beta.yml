name: Deploy Vendor App to DigitalOcean Droplet

on:
  push:

    branches: [ beta-test ]  

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.5.4
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'  # Ensure compatibility with ES modules

      - name: Install Dependencies
        run: |
          export NODE_OPTIONS="--max-old-space-size=2048"
          npm install

      - name: Create Crypto Polyfill
        run: |
          # Create CommonJS files with .cjs extension
          echo "// Crypto polyfill for GitHub Actions" > crypto-polyfill.cjs
          echo "const crypto = require('crypto');" >> crypto-polyfill.cjs
          echo "if (!globalThis.crypto) globalThis.crypto = {};" >> crypto-polyfill.cjs
          echo "if (!globalThis.crypto.getRandomValues) {" >> crypto-polyfill.cjs
          echo "  globalThis.crypto.getRandomValues = function(buffer) { return crypto.randomFillSync(buffer); };" >> crypto-polyfill.cjs
          echo "}" >> crypto-polyfill.cjs
          echo "module.exports = globalThis.crypto;" >> crypto-polyfill.cjs
          
          # Create ESM vite runner that uses execSync with npx
          echo "// ESM Vite Runner" > vite-runner.mjs
          echo "import { createRequire } from 'module';" >> vite-runner.mjs
          echo "import { execSync } from 'child_process';" >> vite-runner.mjs
          echo "const require = createRequire(import.meta.url);" >> vite-runner.mjs
          echo "require('./crypto-polyfill.cjs');" >> vite-runner.mjs
          echo "try {" >> vite-runner.mjs
          echo "  console.log('Starting Vite build...');" >> vite-runner.mjs
          echo "  execSync('npx vite build', { stdio: 'inherit' });" >> vite-runner.mjs
          echo "  console.log('Vite build completed successfully');" >> vite-runner.mjs
          echo "} catch (error) {" >> vite-runner.mjs
          echo "  console.error('Vite build failed:', error);" >> vite-runner.mjs
          echo "  process.exit(1);" >> vite-runner.mjs
          echo "}" >> vite-runner.mjs

      - name: Run TypeScript Compiler
        run: npx tsc -b

      - name: Build with Crypto Polyfill and Increased Memory
        run: |
          export NODE_OPTIONS="--max-old-space-size=2048"
          node vite-runner.mjs

      - name: Deploy to Droplet
        uses: appleboy/ssh-action@v0.1.7
        with:
          host: ${{ secrets.DO_HOST }}
          username: ${{ secrets.DO_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd ~/VendorApp/WebApp
            git checkout beta-test
            git pull origin beta-test
            npm install
            npm run build

            sudo mkdir -p /var/www/vendor.huxxle.com
            sudo cp -r dist/* /var/www/vendor.huxxle.com/
            sudo chown -R www-data:www-data /var/www/vendor.huxxle.com
            sudo chmod -R 755 /var/www/vendor.huxxle.com
