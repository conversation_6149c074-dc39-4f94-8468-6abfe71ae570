export function register(): void {
 if ("serviceWorker" in navigator) {
   navigator.serviceWorker
     .register("/service-worker.js")
     .then(async (registration) => {
       if ("PeriodicSyncManager" in window) {
         const status = await navigator.permissions.query({
           name: "periodic-background-sync" as PermissionName,
         });
         console.log("Permission state:", status.state); // "granted", "denied", or "prompt"

         if (status.state === "granted" && registration.periodicSync) {
           await registration.periodicSync.register("content-sync", {
             minInterval: 60 * 60 * 1000, // 1 hour
           });
           console.log("Periodic Background Sync registered.");
         } else {
           console.warn("Periodic Background Sync permission not granted.");
         }
       } else {
         console.warn(
           "Periodic Background Sync is not supported in this browser."
         );
       }
     })
     .catch(error => {
       console.error("Service Worker registration failed:", error);
     });
 }
}

export function applyUpdate(): void {
  if ("serviceWorker" in navigator) {
    navigator.serviceWorker.ready.then(registration => {
      if (registration.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      }
    });
    
    let refreshing = false;
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      if (!refreshing) {
        refreshing = true;
        window.location.reload();
      }
    });
  }
}

export function unregister(): void {
  if ("serviceWorker" in navigator) {
    navigator.serviceWorker.getRegistrations().then((registrations) => {
      registrations.forEach((registration) => {
        registration.unregister().then((success) => {
          if (success) {
            console.log(
              `Service Worker unregistered successfully with scope: ${registration.scope}`
            );
          } else {
            console.error(
              `Failed to unregister Service Worker with scope: ${registration.scope}`
            );
          }
        });
      });
    });
  } else {
    console.warn("Service Workers are not supported in this browser.");
  }
}
