/* eslint-disable @typescript-eslint/no-explicit-any */

import { useMutation, useQuery } from "react-query";
import { inventoryServices } from "../utils/Services/InventoryServices";
import { useUserAuthStore } from "../store/auth";
import { toast } from "react-toastify";
import { Notifications } from "../utils/Services/Notification";
import { useEffect, useRef, useState } from "react";
import Congrats from "./Congrats";
import logo from "../assets/video_thumbnail.jpg";
import { FaSpinner } from "react-icons/fa";

const PreviewAddProduct = ({
  productDetails,
  coverImageId,
  onBack,
  activateWebsite,
  closeSetup,
  refetch,
  onSetCover,
}: {
  productDetails: any;
  coverImageId: any;
  onBack: () => void;
  closeSetup: () => void;
  activateWebsite?: () => void; // Marked as optional
  refetch?: () => void;
  onSetCover: (index: number) => void;
}) => {
  const modalRef = useRef<HTMLDivElement>(null); // Create a reference to the modal

  useEffect(() => {
    if (modalRef.current) {
      modalRef.current.scrollTop = 0; // Scroll the modal container to the top
    }
  }, []);
  const [congrats, setCongrats] = useState(false);
  const user = useUserAuthStore((state) => state.user);
  const updateAccount = useUserAuthStore((state) => state.updateAccount);

  const mutation = useMutation((formData: FormData) =>
    inventoryServices.addProduct(formData)
  );

  const [mainImageIndex, setMainImageIndex] = useState(coverImageId);

  const handleSetCover = (index: number) => {
    setMainImageIndex(index);
    onSetCover(index); // Keep the parent component in sync
  };

  const handleCongrats = () => {
    setCongrats((prev) => !prev);
  };

  const { refetch: notificationRefetch } = useQuery(
    ["notifications", user.vendorId],
    () => Notifications.getNotification(user.vendorId),
    {
      enabled: !!user.vendorId,
      onSuccess: () => {},
      onError: (error: any) => {
        toast.error("Failed to fetch notifications");
        console.log("notification error:", error);
      },
    }
  );
  const addProduct = () => {
    const formData = new FormData();
    formData.append("SKUNumber", productDetails.SKUNumber);
    formData.append("productName", productDetails.productName);
    formData.append("category", productDetails.category);
    formData.append("description", productDetails.description);
    formData.append("initialStockLevel", productDetails.initialStockLevel);
    formData.append("reorderLevel", productDetails.reorderLevel);
    formData.append("supplierName", productDetails.supplierName);
    formData.append(
      "discountedPrice",
      productDetails.discountedPrice.toString()
    );
    formData.append("storageLocation", productDetails.storageLocation);
    formData.append("purchasePrice", productDetails.purchasePrice);
    formData.append("supplierEmail", productDetails.supplierEmail);
    formData.append("sellingPrice", productDetails.sellingPrice.toString());
formData.append("discount", (productDetails?.discount ?? 0).toString());
    formData.append("batchNumber", productDetails.batchNumber);
    formData.append("notes", productDetails.notes);
    formData.append("supplierPhoneNumber", productDetails.businessPhone);
    formData.append("vendorId", user.vendorId);
    productDetails.productImages.forEach((imageObject: any) => {
      formData.append("productImages", imageObject.file);
    });

    mutation.mutate(formData, {
      onSuccess: (response) => {
        toast.success(response?.data?.message);
        activateWebsite?.();
        refetch?.();
        handleCongrats();
        updateAccount(false);
        notificationRefetch();
      },
      onError: (error: any) => {
        toast.error(error?.response?.data?.error || error?.message);
      },
    });
  };

  return (
    <div ref={modalRef} className="p-2  flex flex-col gap-5  md:p-0  ">
      <div>
        <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
          Preview Your Product
        </h2>
        <p className=" text-sm text-[#919191] font-sora ">
          Kindly review your entry and confirm
        </p>
      </div>

      <div className="flex items-center justify-between h-[264px] md:h-[368px] mt-5">
        <div className="md:w-[368px] h-full overflow-clip w-[264px] rounded-2xl ">
          {productDetails.productImages.length > 0 && (
            <>
              {productDetails.productImages[mainImageIndex]?.type ===
              "video" ? (
                <video
                  className="rounded-lg object-cover"
                  autoPlay
                  muted
                  controls
                >
                  <source
                    src={
                      productDetails.productImages[mainImageIndex]?.previewUrl
                    }
                    type="video/mp4"
                  />
                </video>
              ) : (
                <img
                  src={productDetails.productImages[mainImageIndex]?.previewUrl}
                  className="rounded-lg w-full h-full object-cover"
                  alt="Main Product"
                />
              )}
            </>
          )}
        </div>
        <div className="flex flex-col gap-[10px]">
          {/* {productDetails.productImages
            .slice(1)
            .map((image: any, index: any) => (
              <img
                key={index}
                src={image.previewUrl}
                className="md:w-[116px] overflow-clip w-[83px] h-[83px] md:h-[116px] rounded-lg object-cover cursor-pointer"
                alt="Additional Product"
              />
            ))} */}
          {productDetails.productImages.map((item: any, index: number) => {
            // Skip the image if it's the current main image
            if (index === mainImageIndex) return null;
            return (
              <div
                key={index}
                onClick={() => handleSetCover(index)}
                className="cursor-pointer"
              >
                {item.type === "video" ? (
                  <video
                    className="md:w-[116px] overflow-clip w-[83px] h-[83px] md:h-[116px] rounded-lg object-cover cursor-pointer"
                    muted
                    controls
                    poster={logo} // Use a placeholder thumbnail or a generated poster if available
                  >
                    <source src={item.previewUrl} type="video/mp4" />
                  </video>
                ) : (
                  <img
                    src={item.previewUrl}
                    className="md:w-[116px] overflow-clip w-[83px] h-[83px] md:h-[116px] rounded-lg object-cover cursor-pointer"
                    alt="Additional Product"
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>
      <div className=" flex flex-col gap-2">
        <p className="text-[12px] font-sora font-normal text-[#6C757D]">
          {productDetails?.category}
        </p>
      </div>
      <div className="flex justify-between items-center">
        <p className="text-[20px] break-all font-sora font-semibold text-[#2A2A2A]">
          {productDetails?.productName}
        </p>
        <p className="text-[20px] font-sora font-semibold text-[#4f4cd8]">
          ₦ <span>{productDetails?.discountedPrice.toLocaleString()}</span>
        </p>
      </div>
      <div>
        <p className="text-[14px] pb-4 border-b-[0.5px] border-[#DEE2E6] font-sora font-normal text-[#6C757D]">
          {" "}
          {productDetails?.description}
        </p>
      </div>
      <div className="pb-[40px] flex flex-col gap-4 border-b-[1px] border-[#DEE2E6]">
        <p className="text-[12px] font-sora font-normal text-[#6C757D]">
          Quantity
        </p>
        <div className="flex flex-col md:flex-row px-4 md:px-0 w-full items-center border-[1px] border-[#CED4DA] rounded-2xl ">
          <p
            className={`font-sora flex-1 py-[14px] w-full md:px-5 text-[16px] ${
              productDetails?.reorderLevel
                ? " md:border-r-[1px] border-b-[1px] md:border-b-0"
                : ""
            }  border-[#CED4DA] text-primary-purple font-semibold`}
          >
            {" "}
            {productDetails?.initialStockLevel}{" "}
            <span className="font-sora text-[14px] font-normal text-[#495057]">
              {" "}
              available
            </span>
          </p>
          {productDetails?.reorderLevel && (
            <p className="font-sora md:px-5 py-[14px] w-full flex-1 text-[16px] text-primary-purple font-semibold">
              {" "}
              {productDetails?.reorderLevel}{" "}
              <span className="font-sora text-[14px] font-normal text-[#495057]">
                {" "}
                restock level
              </span>
            </p>
          )}
        </div>
      </div>

      {(productDetails?.purchasePrice || productDetails?.discount) && (
        <div className="pb-[40px] flex flex-col gap-4 border-b-[1px] border-[#DEE2E6] ">
          <p className="text-[12px] font-sora font-normal text-[#6C757D]">
            BUYING PRICE & DISCOUNT
          </p>
          <div className="flex flex-col md:flex-row px-4 md:px-0 w-full  items-center border-[1px] border-[#CED4DA] rounded-2xl ">
            {productDetails?.purchasePrice && (
              <p
                className={`font-sora w-full flex-1 py-[14px] md:px-5 text-[16px] ${
                  productDetails?.purchasePrice && productDetails?.discount
                    ? " md:border-r-[1px] border-b-[1px] md:border-b-0"
                    : ""
                }  border-[#CED4DA] text-primary-purple font-semibold`}
              >
                {" "}
                ₦ {productDetails?.purchasePrice.toLocaleString()}{" "}
                <span className="font-sora text-[14px] font-normal text-[#495057]">
                  {" "}
                  Cost Price
                </span>
              </p>
            )}
          
           {productDetails?.discount > 0 && productDetails?.discount ?  (
 <p className="font-sora md:px-5 py-[14px] w-full flex-1 text-[16px] text-primary-purple font-semibold">
              {" "}
              {productDetails?.discount}{" "}
              <span className="font-sora text-[14px] font-normal text-[#495057]">
                {" "}
                %
              </span>
            </p>
):null}

          </div>
        </div>
      )}

      {productDetails?.supplierName && productDetails?.businessPhone && (
        <div className="pb-[40px] flex flex-col gap-4 border-b-[1px] border-[#DEE2E6]">
          <p className="text-[12px] font-sora font-normal text-[#6C757D]">
            SUPPLIER INFORMATION
          </p>
          <div className="flex flex-col md:flex-row px-4 md:px-0 w-full  items-center border-[1px] border-[#CED4DA] rounded-2xl ">
            <p
              className={`font-sora flex-1 w-full md:px-5 py-[14px] ${
                productDetails?.supplierName
                  ? " md:border-r-[1px] border-b-[1px] md:border-b-0"
                  : ""
              }  border-[#dee2e6] flex-1 text-[14px] text-[#2a2a2a] font-semibold`}
            >
              <span className="font-sora text-[14px] font-normal text-[#495057]">
                {" "}
                Name:
              </span>{" "}
              {productDetails?.supplierName}{" "}
            </p>
            <p className="font-sora md:px-5 w-full py-[14px] flex-1 text-[14px] text-[#2a2a2a] font-semibold">
              <span className="font-sora text-[14px] font-normal text-[#495057]">
                {" "}
                Phone:
              </span>{" "}
              {productDetails?.businessPhone}{" "}
            </p>
          </div>
        </div>
      )}

      {productDetails?.notes && (
        <div className="pb-[40px] flex flex-col gap-4">
          <p className="text-[12px] font-sora font-normal text-[#6C757D]">
            EXTRA INFORMATION
          </p>
          <p className="text-[12px] font-sora font-normal text-[#6C757D]">
            {productDetails?.notes}
          </p>
        </div>
      )}

      <div className="mb-[350px] md:mb-[350px]"> </div>

      {/* <div className="p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6 last:mb-[200px]">
        <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
          Product Information
        </h4>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Product Name
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {productDetails?.productName}
          </p>
        </div>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            SKU Number
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {productDetails?.SKUNumber}
          </p>
        </div>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Category
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {productDetails?.category}
          </p>
        </div>
        <div className="pb-4  flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Description
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {" "}
            {productDetails?.description}
          </p>
        </div>
      </div>

      <div className="p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6 last:mb-[200px]">
        <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
          Product Images
        </h4>
        <div className="add-product-photo flex flex-col gap-5 items-center">
          <div>
            <p className="text-sm font-sora font-normal text-[#5b5b5b] mb-4 text-left">
              Main Image
            </p>

            {productDetails.productImages.length > 0 && (
              <img
                src={productDetails.productImages[coverImageId]?.previewUrl}
                className="w-[490px] h-[424px] rounded-lg object-cover"
                alt="Main Product"
              />
            )}
          </div>
          <div className="additional-images-container flex flex-col mt-2.5 w-full">
            <p className="text-sm font-sora font-normal text-[#5b5b5b] mb-4 text-left">
              Additional Images
            </p>
            <div className="additional-images flex gap-2.5 w-full justify-start">
              {productDetails.productImages
                .slice(1)
                .map((image: any, index: any) => (
                  <img
                    key={index}
                    src={image.previewUrl}
                    className="w-[140px] h-[100px] rounded-lg object-cover cursor-pointer"
                    alt="Additional Product"
                  />
                ))}
            </div>
          </div>
        </div>
      </div>

      <div className="p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6 last:mb-[200px]">
        <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
          Inventory Details
        </h4>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Initial Stock Level
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {" "}
            {productDetails?.initialStockLevel}
          </p>
        </div>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Reorder Stock Level
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {" "}
            {productDetails?.reorderLevel}
          </p>
        </div>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Supplier Name
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {" "}
            {productDetails?.supplierName}
          </p>
        </div>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Supplier Phone Number (Optional)
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {" "}
            {productDetails?.businessPhone}
          </p>
        </div>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Supplier Email (Optional)
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {productDetails?.supplierEmail}
          </p>
        </div>

        <div className="p-0 flex flex-col gap-2 relative">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Storage Location (Optional)
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {productDetails?.storageLocation}
          </p>
        </div>
      </div>

      <div className="p-6 bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6 last:mb-[200px]">
        <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
          Pricing Information
        </h4>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Purchase Price
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            ₦<span> {productDetails?.purchasePrice?.toLocaleString()}</span>
          </p>
        </div>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Selling Price
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            ₦ <span>{productDetails?.sellingPrice?.toLocaleString()}</span>
          </p>
        </div>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Apply Discount % (Optional)
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {`${productDetails?.discount || "0"} ${"%"}`}
          </p>
        </div>
        <div className="pb-4  flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Discounted Price
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            ₦ <span>{productDetails?.discountedPrice.toLocaleString()}</span>
          </p>
        </div>
      </div>

      <div className="p-6  bg-[#f5f5f5] rounded-2xl my-5 flex flex-col gap-6  mb-[300px] ">
        <h4 className="text-base font-sora font-semibold text-[#2a2a2a]">
          Additional Details
        </h4>
        <div className="pb-4 border-b-[0.5px] border-[#cccccc] flex flex-col gap-2">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">
            Batch Number (Optional)
          </p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {productDetails?.batchNumber}
          </p>
        </div>
        <div className=" flex flex-col gap-2 ">
          <p className="text-xs font-sora font-normal text-[#5b5b5b]">Notes</p>
          <p className="text-sm font-sora font-normal text-[#5b5b5b]">
            {productDetails?.notes}
          </p>
        </div>
      </div> */}
      <div className=" fixed bottom-0 left-0 w-full flex justify-around z-90 bg-white py-2.5 p-2  md:p-10">
        <div className="flex flex-col md:flex-row w-full px-7.5 gap-2.5">
          <button
            onClick={onBack}
            className="bg-transparent order-2 md:order-1 text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 py-4 rounded-2xl cursor-pointer flex-1/2"
          >
            Back
          </button>
          <button
            type="button"
            disabled={mutation.isLoading}
            onClick={addProduct}
            // onClick={handleCongrats}
            className={`${
              mutation.isLoading ? "opacity-50 cursor-auto" : ""
            } w-full text-[#fcfcfc] order-1 md:order-2 bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl cursor-pointer flex-2 `}
          >
            {mutation.isLoading ? (
              <p className="animate-spin flex justify-center">
                <FaSpinner />
              </p>
            ) : (
              "Launch Website"
            )}
          </button>
        </div>
      </div>
      {congrats && <Congrats isOpen={congrats} closeSetup={closeSetup} />}
    </div>
  );
};
export default PreviewAddProduct;
