// import { ChartCircle } from "iconsax-react";
import ActiveState from './ActiveState';
import { useEffect } from 'react';
import { analyticsServices } from '../../utils/Services/Analytics';
import { useUserAuthStore } from '../../store/auth';
import { useDateStore } from '../../store/date';
import { useQuery } from 'react-query';

const Analytic = () => {
  useEffect(() => {
    document.title = ' Analytics';
  });
  const user = useUserAuthStore((state) => state.user);
  const { startDates, endDates } = useDateStore();
  const formattedStartDate = startDates
    ? new Date(startDates).toISOString().split('T')[0]
    : undefined;
  const formattedEndDate = endDates
    ? new Date(endDates).toISOString().split('T')[0]
    : undefined;

  // const analyticData = {
  //   salesData: {/* ... */ },
  //   inventoryData: {/* ... */ },
  //   customerInsights: {/* ... */ },
  //   orderData: {/* ... */ },
  //   financialHealth: {/* ... */ }
  // };

  const { data, isLoading, isError } = useQuery(
    ['getAnalytics', user.vendorId, formattedEndDate, formattedStartDate],
    () =>
      analyticsServices.getAnalytics(
        user.vendorId,
        formattedStartDate,
        formattedEndDate
      )
  );

  return (
    <div className="pb-20">
      <div className="flex flex-col md:flex-row justify-between gap-4 font-sora border-b pb-6 px-4 mt-4 md:px-0 md:mt-0">
        <div className="">
          <h1 className="text-2xl font-bold m-0">Analytics Management</h1>
          <p className="text-base text-[#7e8494]">
            View your analytics, manage trends, and act on smart AI insights
            easily
          </p>
        </div>

        {/* <button className=" justify-center border border-gray-300 rounded-2xl px-3 py-2 text-base flex items-center cursor-pointer  font-bold md:ml-5">
          <ChartCircle size="16" color="#1a1a1a" className="mr-2" />
          Analytics Reports
        </button> */}
      </div>

      <ActiveState
        data={data}
        isLoading={isLoading}
        isError={isError}
        formattedStartDate={formattedStartDate}
        formattedEndDate={formattedEndDate}
      />
    </div>
  );
};
export default Analytic;
