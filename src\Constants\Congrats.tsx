/* eslint-disable @typescript-eslint/no-explicit-any */
import progress from "../assets/Final.svg";
import image from "../assets/Group 1000011163.svg";
import { useUserAuthStore } from "../store/auth";
import { Link } from "react-router-dom";
import { useEffect, useState } from "react";
import { Copy, TickCircle } from "iconsax-react";

const Congrats = ({ isOpen, closeSetup }: any) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("no-scroll");
    } else {
      document.body.classList.remove("no-scroll");
    }

    return () => {
      document.body.classList.remove("no-scroll");
    };
  }, [isOpen]);
    const [isCopied, setIsCopied] = useState(false);
      const [copy, setCopy] = useState<string | null>("Copy");

   const copyLink = (): void => {
    const linkElement = document.getElementById(
      "link-to-copy"
    ) as HTMLAnchorElement | null;

    if (linkElement && linkElement.href) {
      const link: string = linkElement.href;
      navigator.clipboard
        .writeText(link)
        .then(() => {
          setCopy("Copied");
          setIsCopied(true);
          setTimeout(() => setIsCopied(false), 2000);
        })
        .catch((err) => {
          console.error("Failed to copy: ", err);
        });
    } else {
      console.error("Anchor element or href not found");
    }
  };
  const businessDetails = useUserAuthStore((state) => state.business);
  const userStatus = useUserAuthStore((state)=>state.userStatus)
  const businesWeb = useUserAuthStore((state)=>state.businessWebsite)
  return (
    <div className="fixed font-sora  bg-black/10 top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-500 ease-in-out">
      <div
        className={`fixed top-0 right-0 h-screen md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-1 md:p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? "translate-x-0 " : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeSetup}
            className="close-drawer text-4xl font-bold cursor-pointer my-0 pr-4 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="my-[20px]  grid justify-center">
            <img src={progress} alt="progress bar" />
          </div>
          <div className="flex-col flex mx-auto gap-6 justify-center items-center">
            <div className="flex justify-center">
              <img src={image} alt="congrats" />
            </div>
            <h3 className="text-[#1A1A1A] font-sora text-[24px] md:text-[28px]">
              {" "}
              Yaay You're All Set
            </h3>
            <p className="text-center text-[#7B7B7B] font-sora text-[14px]">
              Congratulations your website is now live and your customers can
              now purchase products from your store
            </p>
             <div className="w-full lg:w-fit gap-2 mt-3 md:mt-[0px] flex items-center rounded-3xl md:pl-[24px] pl-[10px] justify-between border-[1px] border-primary-neutral200 bg-primary-baseWhite relative">
                      <a
                        id="link-to-copy"
                        href={
                          userStatus === "PremiumUser"
                            ? `https://${businesWeb}`
                            : `https://${businessDetails.subdomain}.huxxle.shop`
                        }
                        target="_blank"
                        className="font-sora break-all text-primary-neutral1000 text-[8px]"
                      >
                        {userStatus === "PremiumUser"
                          ? `${businesWeb}`
                          : `https://${businessDetails.subdomain}.huxxle.shop`}
                      </a>
            
                      <button
                        onClick={copyLink}
                        className="bg-primary-baseWhite flex gap-2 items-center py-[12px] px-[16px] text-primary-neutral1000 border-[1px] font-semibold rounded-3xl"
                      >
                        {isCopied ? (
                          <TickCircle size={16} color="#28A745" />
                        ) : (
                          <Copy size={16} />
                        )}
                        <span className="hidden md:block">
                          {isCopied ? "Link Copied!" : `${copy}`}
                        </span>
                      </button>
                    </div>
            <div className="flex flex-col md:flex-row w-full px-7.5 gap-2.5">
             
              <Link
                to={userStatus === 'PremiumUser' ? `https://${businesWeb}`: `https://${businessDetails.subdomain}.huxxle.shop`}
                target="_blank"
                rel="noreferrer"
                className="bg-transparent md:flex-1 items-center flex justify-center  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 h-[49px] rounded-2xl cursor-pointer flex-1/2"
                //   onClick={handleSubmit}
              >
                Go to Website
              </Link>
               <button
                onClick={closeSetup}
                className={` w-full text-[#fcfcfc] md:flex-1 bg-[#4f4cd8] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 h-[49px] rounded-2xl cursor-pointer flex-2 `}
              >
                Start Dashboard Tour
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Congrats;
