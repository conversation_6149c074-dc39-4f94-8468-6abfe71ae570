/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useRef, useState } from 'react';
import { FormatPrice } from '../../utils/FormatPrice';
import { AddCircle, ArrowDown2, ArrowUp2, MinusCirlce } from 'iconsax-react';
import { orderServices } from './../../utils/Services/Order';
import { useMutation, useQuery } from 'react-query';
import { toast } from 'react-toastify';
import { Notifications } from '../../utils/Services/Notification';
import { useUserAuthStore } from '../../store/auth';
import thumbnail from '../../assets/video_thumbnail.jpg'

interface CreateOrderProps {
  closeModal: () => void;
  isOpen: boolean;
  orderDetail: any;
  allData: any;
  allClose: any;
  refetch: any;
}

const PreviewOrder: React.FC<CreateOrderProps> = ({
  closeModal,
  isOpen,
  orderDetail,
  allData,
  allClose,
  refetch,
}) => {
 const modalRef = useRef<HTMLDivElement>(null); // Create a reference to the modal
 
   useEffect(() => {
     if (modalRef.current) {
       modalRef.current.scrollTo({ top: 0, behavior: 'smooth' }); // Scroll the modal container to the top
     }
   }, []);
  const [isMoreDetails, setIsMoreDetails] = useState<number | null>(null);
  const handleMoreDetails = (productId: number) => {
    setIsMoreDetails((prevId) => (prevId === productId ? null : productId));
  };

  const createOrderMutation = useMutation(orderServices.createOrder);
  const user = useUserAuthStore((state)=>state.user)

  // const [quantities, setQuantities] = useState<{ [key: string]: number }>({});
  // // Function to increase the quantity of a product
  // const handleIncreaseQuantity = (productID: any) => {
  //   setQuantities((prevQuantities: any) => ({
  //     ...prevQuantities,
  //     [productID]: (prevQuantities[productID] || 0) + 1, // Increment quantity
  //   }));
  // };

  // // Function to decrease the quantity of a product
  // const handleDecreaseQuantity = (productID: string) => {
  //   setQuantities((prevQuantities) => {
  //     const currentQuantity = prevQuantities[productID] || 1;
  //     if (currentQuantity > 1) {
  //       return {
  //         ...prevQuantities,
  //         [productID]: currentQuantity - 1,
  //       };
  //     }
  //     return prevQuantities;
  //   });
  // };

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('no-scroll');
    } else {
      document.body.classList.remove('no-scroll');
    }

    return () => {
      document.body.classList.remove('no-scroll');
    };
  }, [isOpen]);
  const totalPrice = orderDetail.cartProducts.reduce(
    (acc: number, product: any) => {
      return acc + (product.discountedPrice || 0) * (product.quantity || 1);
    },
    0
  );
  const grandTotal = totalPrice + (orderDetail.deliveryFee || 0);

   const { refetch: notificationRefetch } = useQuery(
     ["notifications", user.vendorId],
     () => Notifications.getNotification(user.vendorId),
     {
       enabled: !!user.vendorId,
       onSuccess: () => {},
       onError: (error: any) => {
         console.log("notification error:", error);
       },
     }
   );

  const handleClick = async () => {
    const transformedCartProducts = orderDetail.cartProducts.map(
      (product: any) => ({
        product_id: product.product_id,
        quantity: product.quantity,
        real_price: product.sellingPrice,
        discount: product.discountAmount,
        amountAfterDiscount:
          product.discountedPrice || product.amountAfterDiscount,
      })
    );

    const payload = {
      ...orderDetail,
      cartProducts: transformedCartProducts,
      orderDate: orderDetail.orderDate
        ? new Date(orderDetail.orderDate).toISOString()
        : null,
    };
    try {
      const response: any = await createOrderMutation.mutateAsync(payload);
      if (response) {
        toast.success(response.message);
        refetch();
        notificationRefetch()
        allClose();
      } else {
        toast.error(response?.error);
      }
    } catch (error) {
      console.error('Error during order creation:', error); // Improved error logging
      toast.error('Failed to create order. Please try again.'); // Notify user of failure
    }
  };

  return (
    <div className="fixed font-sora  bg-black/10 top-0 left-0 w-full h-full bg-[rgba(0, 0, 0, 0.233)] backdrop-blur-sm z-50 transition-transform duration-500 ease-in-out">
      <div
      ref={modalRef}
        className={`fixed top-0 right-0 h-full w-full md:w-[580px] bg-white backdrop-blur-[10px] z-30 p-1 md:p-5 transition-transform duration-500 ease-in-out ${
          isOpen ? "translate-x-0 " : "translate-x-full"
        }`}
      >
        <div>
          <span
            onClick={closeModal}
            className="close-drawer text-4xl font-bold cursor-pointer my-0 flex justify-end z-[999999]"
          >
            &times;
          </span>
          <div className="overflow-y-auto h-screen scrollbar-none hidescroll ">
            <div className="pl-6 ">
              <h2 className="text-2xl font-sora leading-7 mb-2.5 font-semibold ">
                Preview Order
              </h2>
              <p className=" text-sm text-[#919191] font-sora ">
                Preview your customer order below
              </p>
            </div>
            <div>
              <div className="p-2 md:p-6 l flex flex-col gap-6 last:mb-[200px]">
                <div className=" rounded-2xl flex flex-col gap-2 md:gap-6 bg-[#F5F5F5] px-[24px] py-[16px]">
                  <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                    Who wants to buy ?
                  </h4>
                  <div className="p-0 flex flex-col gap-1 md:gap-2.5 relative last:border-b-0 ">
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Name
                    </p>
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      {orderDetail?.customerName}
                    </p>
                  </div>
                </div>
                <div className=" rounded-2xl flex flex-col gap-2 md:gap-6 bg-[#F5F5F5] px-[8px] md:px-[24px] py-[16px]">
                  {" "}
                  <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                    When are they buying?
                  </h4>
                  <div className="p-0 flex flex-col gap-1 md:gap-2.5 relative last:border-b-0 ">
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Date
                    </p>
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      {orderDetail.orderDate.toISOString().split("T")[0]}
                    </p>
                  </div>
                </div>
                <div className=" rounded-2xl flex flex-col gap-6 bg-[#F5F5F5] px-[24px] py-[16px]">
                  {" "}
                  <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                    What are they buying?
                  </h4>
                  <div className="p-2 md:p-6 rounded-2xl flex bg-primary-baseWhite flex-col gap-6 relative last:border-none ">
                    {orderDetail.cartProducts.length > 0 && (
                      <div className="bg-primary-baseWhite p-2  rounded-2xl flex flex-col gap-3">
                        {orderDetail.cartProducts.map(
                          (product: any, index: any) => (
                            <div
                              key={index}
                              className="border-b-[1px] last:border-none justify-between items-start gap-4 flex flex-col"
                            >
                              <div className="flex-col flex md:flex-row items-start w-full space-y-4 md:space-y-0 justify-between">
                                <div className="flex items-center gap-2">
                                  <div className="flex-shrink-0">
                                    <img
                                      className="w-[100px] h-[100px] object-contain rounded-3xl"
                                      src={
                                        allData.find(
                                          (p: any) =>
                                            p.productId === product.product_id
                                        )?.productImage
                                      }onError={(e: any) => {
                                        e.target.src = thumbnail; // Fallback to thumbnail on error
                                      }}
                                      // src={product.productImage}
                                      alt="productImage"
                                    />
                                  </div>
                                  <div className="flex flex-col gap-3">
                                    <p className="text-[#919191] font-sora text-[12px]">
                                      Product
                                    </p>
                                    <h4 className="text-[16px] font-sora text-primary-neutralt1">
                                      {product.product_name}
                                    </h4>
                                    {isMoreDetails === product.product_id ? (
                                      ""
                                    ) : (
                                      <div className="flex w-[120px]  justify-evenly py-1 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                                        <MinusCirlce className="text-primary-neutral300" />
                                        <p className="text-[14px] font-sora text-primary-neutralt1">
                                          {" "}
                                          {product.quantity}
                                        </p>
                                        <AddCircle className="text-primary-neutral300" />
                                      </div>
                                    )}
                                  </div>
                                </div>
                                <div className="flex justify-between w-full md:flex-col md:h-[100px] md:text-right">
                                  {isMoreDetails === product.product_id ? (
                                    ""
                                  ) : (
                                    <div className="flex gap-2 md:flex-col">
                                      <h4 className="font-sora text-primary-neutralt2 text-[16px] font-semibold">
                                        ₦
                                        {FormatPrice(
                                          product.discountedPrice || 0
                                          // *
                                          //   (quantities[product.product_id] || 1)
                                        )}
                                      </h4>
                                      <p className="text-[14px] text-[#DC3545] line-through">
                                        ₦{" "}
                                        {FormatPrice(
                                          product.sellingPrice || 0
                                          // *
                                          //   (quantities[product.product_id] || 1)
                                        )}
                                      </p>
                                    </div>
                                  )}

                                  <div
                                    onClick={() =>
                                      handleMoreDetails(product.product_id)
                                    }
                                    className={`${
                                      isMoreDetails === product.product_id
                                        ? "cursor-pointer flex justify-end w-full  self-end"
                                        : "mt-auto cursor-pointer self-end"
                                    }`}
                                  >
                                    {isMoreDetails === product.product_id ? (
                                      <ArrowUp2 className="text-primary-neutralt1 text-right" />
                                    ) : (
                                      <ArrowDown2 className="text-primary-neutralt1 text-right" />
                                    )}
                                  </div>
                                </div>
                              </div>
                              {isMoreDetails === product.product_id && (
                                <div className="flex flex-col w-full gap-5">
                                  <h4 className="text-[16px] font-sora font-semibold text-primary-neutralt1">
                                    Pricing and Quantity
                                  </h4>
                                  {/* <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                                    <p className="font-sora text-primary-neutralt2 text-[14px]">
                                      Quantity
                                    </p>
                                    <div className="flex w-[150px] md:w-[210px]  justify-between p-1 md:p-4 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                                      <MinusCirlce className="text-primary-neutral300" />
                                      <p className="text-[14px] font-sora text-primary-neutralt1">
                                        {' '}
                                        {product.quantity || 1}
                                      </p>
                                      <AddCircle className="text-primary-neutralt1" />
                                    </div>
                                  </div> */}
                                  <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                                    <p className="font-sora text-primary-neutralt2 text-[14px]">
                                      Selling Price
                                    </p>
                                    <div className="flex w-[150px] md:w-[210px]  p-4 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                                      <p className="font-sora  text-primary-neutralt2 ">
                                        ₦{" "}
                                        {FormatPrice(
                                          product.discountedPrice
                                          // *
                                          //   quantities[product.product_id]
                                        )}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                                    <p className="font-sora text-primary-neutralt2 text-[14px]">
                                      Discounted Price
                                    </p>
                                    <div className="flex w-[190px] text-[14px] md:w-[210px] items-center  justify-between p-4 border-[1px] rounded-3xl border-primary-neutral300 ">
                                      <p className="font-sora text-[#DC3545] ">
                                        ₦ {FormatPrice(product.discountAmount)}
                                      </p>
                                      <div className="flex items-center gap-3">
                                        {" "}
                                        <p className="font-sora text-[#DC3545] ">
                                          {product.discountPercentage || 0}%
                                        </p>
                                        {/* <p className="font-sora text-[18px] text-primary-neutralt1">
                                          X
                                        </p> */}
                                      </div>
                                    </div>
                                  </div>
                                  <div className="flex w-full pb-4 border-b-[1px] border-primary-neutral300 justify-between items-center">
                                    <p className="font-sora text-primary-neutralt2 text-[14px]">
                                      Delivery
                                    </p>
                                    <div className="flex w-[150px] md:w-[210px] p-4 border-[1px] rounded-3xl border-primary-neutral300 gap-2">
                                      <p className="font-sora text-primary-neutralt2 ">
                                        ₦ {FormatPrice(orderDetail.deliveryFee)}
                                        {orderDetail.deliveryFee === 0 &&
                                          "(free)"}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex w-full pb-4  border-primary-neutral300 justify-between items-center">
                                    <p className="font-sora text-primary-neutralt2 text-[14px]">
                                      Total
                                    </p>
                                    <div className="self-end">
                                      <p className="font-sora text-[16px] text-primary-neutralt2 ">
                                        ₦{" "}
                                        {FormatPrice(
                                          product.discountedPrice *
                                            product.quantity +
                                            orderDetail.deliveryFee
                                        )}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          )
                        )}
                      </div>
                    )}
                  </div>
                  <div className="self-end text-right">
                    <p className="text-[12] font-sora text-[#919191]">
                      Delivery
                    </p>
                    <p className="text-[14px] font-sora text-[#2A2A2A] mt-[8px]">
                      ₦ {FormatPrice(orderDetail.deliveryFee)}
                      {orderDetail.deliveryFee === 0 && "(free)"}
                    </p>
                  </div>
                  <div className="flex border-t-[1px] py-[24px] border-primary-neutral300 mt-[10px] w-full justify-between items-center">
                    <p className="text-primary-neutralt2 font-sora  text-[14px]">
                      Grand Total
                    </p>
                    <p className="text-[#28A745] font-bold font-sora">
                      ₦ {FormatPrice(grandTotal)}
                    </p>
                  </div>
                </div>

                <div className=" rounded-2xl flex flex-col gap-6 bg-[#F5F5F5] px-[24px] py-[16px]">
                  {" "}
                  <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                    Where are they buying from ?
                  </h4>
                  <div className="p-0 flex flex-col gap-2.5 relative last:border-b-0 ">
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      Channel
                    </p>
                    <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                      {orderDetail.channel}
                    </p>
                  </div>
                </div>
                <div className=" rounded-2xl flex flex-col gap-6 bg-[#F5F5F5] px-[24px] py-[16px]">
                  {" "}
                  <h4 className="font-semibold font-sora text-[#2a2a2a] text-base">
                    How are they paying ?
                  </h4>
                  <div className=" flex flex-col gap-5 relative  ">
                    <div className="flex flex-col gap-3 pb-6 border-b-[1px] border-primary-neutral300">
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        Payment status
                      </p>
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        {orderDetail.paymentStatus || "N/A"}
                      </p>
                    </div>
                    <div className="flex flex-col gap-3 ">
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        Payment Method
                      </p>
                      <p className="text-[14px] font-sora font-normal text-[#5b5b5b]">
                        {orderDetail.paymentMethod}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className=" fixed bottom-0 left-0 w-full flex justify-around z-50 bg-white py-2.5 p-2  md:p-10">
              <div className="flex w-full px-7.5 gap-2.5">
                <button
                  type="button"
                  onClick={closeModal}
                  className="bg-transparent  text-[#2a2a2a] text-sm font-sora font-semibold border border-[#dcdcdc] px-12 py-4 rounded-2xl cursor-pointer flex-1/2"
                >
                  Back
                </button>
                <button
                  onClick={handleClick}
                  disabled={createOrderMutation.isLoading}
                  className={`${
                    createOrderMutation.isLoading
                      ? " opacity-70 cursor-default"
                      : "cursor-pointer"
                  } bg-[#4f4cd8] w-full text-[#fcfcfc] text-sm font-sora font-semibold border border-[#dcdcdc] px-6 py-4 rounded-2xl  flex-2`}
                >
                  {createOrderMutation.isLoading
                    ? "Creating Order..."
                    : "Create Order"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default PreviewOrder;
