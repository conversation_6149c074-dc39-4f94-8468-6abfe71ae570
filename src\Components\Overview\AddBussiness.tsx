/* eslint-disable @typescript-eslint/no-explicit-any */
import { Gallery } from "iconsax-react";
import AuthInput from "../../Constants/AuthInput";
import pattern from "../../assets/pattern.png";
import { useEffect, useState, useRef } from "react";
import { useMutation } from "react-query";
import { AuthServices } from "../../utils/Services/AuthServices";
import { toast } from "react-toastify";
import { useUserAuthStore } from "../../store/auth";
import { useNavigate } from "react-router-dom";
import { RouteNames } from "../../utils/RouteNames";
import ReactCrop, { Crop, PixelCrop } from "react-image-crop";
import "react-image-crop/dist/ReactCrop.css";
import { FaSpinner } from "react-icons/fa";
import loadImage from "blueimp-load-image";

interface BusinessDetails {
  email: string;
  businessName: string;
  businessType: string;
  businessPhone: string;
  businessAddress: string;
  businessCity: string;
  businessState: string;
  businessZipCode: string;
  businessImage: any; // This will store the cropped image file
  businessWebsite?: string;
}

const AddBussiness = () => {
  const [enableButton, setEnableButton] = useState<boolean>(false);
  const user = useUserAuthStore((state) => state.user);
  const [businessDetails, setBusinessDetails] = useState<BusinessDetails>({
    email: user?.userEmail || "",
    businessName: "",
    businessType: "",
    businessPhone: "",
    businessAddress: "",
    businessCity: "",
    businessState: "",
    businessZipCode: "",
    businessImage: null,
    businessWebsite: "",
  });
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  // States for image cropping
  const [upImg, setUpImg] = useState<string>("");
  const [crop, setCrop] = useState<Crop>({
    unit: "%",
    x: 25,
    y: 25,
    width: 50,
    height: 50,
  });
  const [completedCrop, setCompletedCrop] = useState<PixelCrop | null>(null);
  const [isCropModalOpen, setIsCropModalOpen] = useState<boolean>(false);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const imageRef = useRef<HTMLImageElement | null>(null);

  const userAuthStore = useUserAuthStore((state) => state);
  const navigate = useNavigate();

  const mutation = useMutation((formData: FormData) =>
    AuthServices.addBusiness(formData)
  );

  // Update business details for text/select inputs
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBusinessDetails((prevDetails) => ({
      ...prevDetails,
      [name]: value,
    }));
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setBusinessDetails((prevDetails) => ({
      ...prevDetails,
      [name]: value,
    }));
  };

  // When user selects an image, set up the cropping modal
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    // loadImage will correct EXIF orientation and give us a canvas:
    loadImage(
      file,
      (canvasOrImg) => {
        // canvasOrImg is an HTMLCanvasElement when canvas:true
        if (!(canvasOrImg instanceof HTMLCanvasElement)) return;
        canvasOrImg.toBlob((blob) => {
          if (!blob) return;
          const jpegFile = new File([blob], file.name, { type: blob.type });
          const url = URL.createObjectURL(blob);
          setOriginalFile(jpegFile);
          setUpImg(url);
          setIsCropModalOpen(true);
        }, "image/jpeg");
      },
      { canvas: true, orientation: true }
    );
  };

  // When the image is loaded in the cropper, keep its reference
  const onImageLoaded = (img: HTMLImageElement) => {
    imageRef.current = img;
  };

  // Helper: Generate cropped image blob from canvas
  const getCroppedImg = (
    image: HTMLImageElement,
    crop: PixelCrop,
    fileName: string
  ): Promise<Blob | null> => {
    const canvas = document.createElement("canvas");
    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;
    canvas.width = crop.width;
    canvas.height = crop.height;
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      return Promise.resolve(null);
    }

    ctx.drawImage(
      image,
      crop.x * scaleX,
      crop.y * scaleY,
      crop.width * scaleX,
      crop.height * scaleY,
      0,
      0,
      crop.width,
      crop.height
    );

    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            console.error("Canvas is empty");
            return reject(new Error("Canvas is empty"));
            console.log("file:", fileName);
          }
          resolve(blob);
        },
        "image/jpeg",
        1
      );
    });
  };

  // When the user confirms the crop, generate the cropped image
  const handleCropComplete = async () => {
    if (imageRef.current && completedCrop) {
      try {
        const croppedBlob = await getCroppedImg(
          imageRef.current,
          completedCrop,
          "cropped.jpeg"
        );
        if (croppedBlob) {
          const croppedFile = new File(
            [croppedBlob],
            originalFile?.name || "cropped.jpeg",
            { type: croppedBlob.type }
          );
          // Update the businessDetails with the cropped image
          setBusinessDetails((prev) => ({
            ...prev,
            businessImage: croppedFile,
          }));
          const croppedImageUrl = URL.createObjectURL(croppedBlob);
          setPreviewImage(croppedImageUrl);
        }
      } catch (err) {
        console.error(err);
      }
    }
    setIsCropModalOpen(false);
  };

  useEffect(() => {
    const requiredFields = [
      "businessName",
      "businessType",
      // "businessPhone",
      "businessAddress",
      "businessCity",
      "businessState",

      // 'businessZipCode',
    ];

    const allFieldsFilled = requiredFields.every(
      (field) =>
        businessDetails[field as keyof BusinessDetails]?.toString().trim() !==
        ""
    );

    setEnableButton(allFieldsFilled);
  }, [businessDetails]);

  useEffect(() => {
    // Update email only when userEmail changes
    setBusinessDetails((prevDetails) => ({
      ...prevDetails,
      email: user?.userEmail || "",
    }));
  }, [user?.userEmail]);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData();

    formData.append("email", businessDetails.email);
    formData.append("businessName", businessDetails.businessName);
    formData.append("businessType", businessDetails.businessType);
    formData.append("businessPhone", businessDetails.businessPhone);
    formData.append("businessAddress", businessDetails.businessAddress);
    formData.append("businessCity", businessDetails.businessCity);
    formData.append("businessState", businessDetails.businessState);
    formData.append("businessZipCode", businessDetails.businessZipCode);
    formData.append("vendorId", user.vendorId);

    if (businessDetails.businessImage) {
      formData.append("businessImage", businessDetails.businessImage);
    }

    if (businessDetails.businessWebsite) {
      formData.append("businessWebsite", businessDetails.businessWebsite);
    }

    mutation.mutate(formData, {
      onSuccess: (data) => {
        toast.success("Business details successfully added!");
        userAuthStore.updateBusiness(data?.data?.business);

        console.log(businessDetails);
        navigate(RouteNames.overview, {
          state: { email: businessDetails.email },
        });
      },
      onError: (error: any) => {
        const errorMessage =
          error?.response?.data?.error || "Something went wrong";
        toast.error(errorMessage);
        console.log(error?.response?.data?.error);
        console.log("user:", user);
      },
    });
  };

  return (
    <div className="lg:relative mt-2 md:mt-[70px] bg-[#fcfcfc]  flex-1 flex flex-col  overflow-auto font-sora pb-5">
      <div className="lg:relative flex-grow flex flex-col md:py-6 md:px-3 md:mx-auto md:w-[580px] md:shadow-md md:shadow-gray-300  md:rounded-2xl ">
        <div className="hidden  w-full h-[100%] bg-gradient-to-br from-[#4f4cd8] to-[#2a2872] relative rounded-[40px]">
          <div className="absolute top-0 left-0 w-[55%] h-[15%] bg-[#fcfcfc] rounded-br-[40px]"></div>
          <div className="absolute bottom-0 right-0 w-[10%] h-[17%] bg-[#fcfcfc] rounded-tl-[40px]"></div>
          <img
            src={pattern}
            alt="pattern"
            className="absolute right-0 w-[400px] h-[300px]"
          />
        </div>

        <div className="flex gap-1 md:gap-2 flex-col left-0 px-4 w-full lg:px-0">
          <h2 className="sm:text-[28px] text-lg text-[#1a1a1a] font-semibold">
            Let's Get Your Business Ready!
          </h2>
          <p className="text-sm text-[#7B7B7B] font-light">
            We just need a few details to set things up.
          </p>
        </div>
        <div className="w-full  px-4 md:px-0  flex justify-center items-center">
          <form
            onSubmit={handleSubmit}
            encType="multipart/form-data"
            className="flex  flex-col  gap-4 py-4 h-full lg:pt-10  items-center lg:overflow-y-hidden  w-full  lg:px-3 text-[#333333] bg-[#FCFCFC] rounded-[22px]"
          >
            <div className="flex w-full flex-col flex-1 border-[#dcdcdc]">
              <div className="my-6 md:my-0 items-center flex flex-col gap-1">
                {" "}
                <div
                  className="flex  w-[100px] mx-auto md:mx-0 cursor-pointer h-[100px] items-center justify-center bg-[#e6e5f9] rounded-[20px] mb-4 text-center text-[14px] relative"
                  id="add-photo"
                >
                  <label
                    htmlFor="business-image"
                    className="flex w-[100px] h-[100px]
                 items-center justify-center
                 bg-[#e6e5f9] rounded-[20px]
                 mb-0 cursor-pointer
                 relative overflow-hidden"
                  >
                    {previewImage ? (
                      
                        <img
                          src={previewImage}
                          alt="Selected Business"
                          className="block w-full h-full object-cover"
                        />
                    
                    ) : (
                      <div className="flex flex-col items-center gap-3 justify-center h-full">
                        <Gallery size="32" color="#4F4CD8" />
                        <span className="text-[#4F4CD8] text-xs font-normal">
                          Add Photo
                        </span>
                      </div>
                    )}
                    <input
                      type="file"
                      id="business-image"
                      name="businessImage"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                  </label>
                </div>{" "}
                <p className="font-sora text-[#5B5B5B] text-[12px]">
                  A logo makes your website stand out.
                </p>{" "}
              </div>

              <div>
                <AuthInput
                  label="Business Name"
                  type="text"
                  name="businessName"
                  value={businessDetails.businessName}
                  placeholder="Enter your business name"
                  onChange={handleChange}
                />
              </div>

              <div className="">
                <p className="text-[#5B5B5B] font-sora text-sm">
                  Business Type
                </p>
                <select
                  id="business-type"
                  name="businessType"
                  value={businessDetails.businessType}
                  onChange={handleSelectChange}
                  className="border font-sora text-[#ABABAB] bg-[#FCFCFC] text-[14px] mt-2 rounded-[16px] w-full h-12 px-3 focus:outline-none focus:shadow-outline"
                >
                  <option value="" disabled hidden>
                    Select Your Business Type
                  </option>
                  <option value="Fashion and Apparel">
                    👗 Fashion & Clothing
                  </option>
                  <option value="Food and Beverage">🍔 Food & Drinks</option>
                  <option value="Home and Living">🏠 Home & Living</option>
                  <option value="Beauty and Personal Care">
                    ✂️ Beauty & Personal Care
                  </option>
                  <option value="Electronics and Gadgets">
                    {" "}
                    💻 Electronics & Gadgets{" "}
                  </option>
                  <option value="Agriculture"> 🚜 Agriculture</option>
                  <option value="Other">❓ Others</option>
                </select>
              </div>
              {/* comment out phone number */}
              {/* <div>
                <p className="text-[#5B5B5B] font-sora text-sm mt-4">
                  Business Phone Number (do not include the first zero of your number)
                </p>
                <div className="flex items-center mt-2 mb-4">
                  <select className="mr-2.5 bg-[#FCFCFC] flex-shrink-0 flex-[0.3] text-xs text-[#ABABAB] h-12 px-2 border rounded-2xl outline-none">
                    <option>🇳🇬+234</option>
                  </select>
                  <input
                    className="text-xs flex-1 placeholder:text-[#ABABAB] bg-[#FCFCFC] px-2.5 h-12 border hover:border-primary-purple500 rounded-2xl outline-none"
                    type="text"
                    id="businessPhone"
                    name="businessPhone"
                    value={businessDetails.businessPhone}
                    onChange={handleChange}
                    placeholder="************"
                  />
                </div>
              </div> */}

              {/* comment out website */}
              {/* <AuthInput
                label="Business Website (Optional)"
                type="text"
                name="businessWebsite"
                value={businessDetails.businessWebsite}
                onChange={handleChange}
                placeholder="Enter your business website here"
              /> */}
              <div className="mt-4">
                <AuthInput
                  label="Business Address"
                  type="text"
                  name="businessAddress"
                  value={businessDetails.businessAddress}
                  onChange={handleChange}
                  placeholder="Enter your business address"
                />
              </div>

              <AuthInput
                label="City"
                type="text"
                name="businessCity"
                value={businessDetails.businessCity}
                onChange={handleChange}
                placeholder="Enter City here"
              />
            </div>

            <div className="flex flex-col w-full flex-1 border-none">
              <div className="flex justify-between">
                <div className=" w-full">
                  <p className="text-[#5B5B5B] font-sora text-sm">State</p>
                  <select
                    id="business-state"
                    name="businessState"
                    value={businessDetails.businessState}
                    onChange={handleSelectChange}
                    className="border font-sora bg-[#FCFCFC] text-sm mt-3 rounded-[16px] w-full h-12 px-3 focus:outline-none focus:shadow-outline"
                  >
                    <option value="">Select State</option>
                    <option value="abia">Abia</option>
                    <option value="adamawa">Adamawa</option>
                    <option value="akwa-ibom">Akwa Ibom</option>
                    <option value="anambra">Anambra</option>
                    <option value="bauchi">Bauchi</option>
                    <option value="bayelsa">Bayelsa</option>
                    <option value="benue">Benue</option>
                    <option value="borno">Borno</option>
                    <option value="cross-river">Cross River</option>
                    <option value="delta">Delta</option>
                    <option value="ebonyi">Ebonyi</option>
                    <option value="edo">Edo</option>
                    <option value="ekiti">Ekiti</option>
                    <option value="enugu">Enugu</option>
                    <option value="federal-capital-territory">
                      Federal Capital Territory
                    </option>
                    <option value="gombe">Gombe</option>
                    <option value="imo">Imo</option>
                    <option value="jigawa">Jigawa</option>
                    <option value="kaduna">Kaduna</option>
                    <option value="kano">Kano</option>
                    <option value="katsina">Katsina</option>
                    <option value="kebbi">Kebbi</option>
                    <option value="kogi">Kogi</option>
                    <option value="kwara">Kwara</option>
                    <option value="lagos">Lagos</option>
                    <option value="nasarawa">Nasarawa</option>
                    <option value="niger">Niger</option>
                    <option value="ogun">Ogun</option>
                    <option value="ondo">Ondo</option>
                    <option value="osun">Osun</option>
                    <option value="oyo">Oyo</option>
                    <option value="plateau">Plateau</option>
                    <option value="rivers">Rivers</option>
                    <option value="sokoto">Sokoto</option>
                    <option value="taraba">Taraba</option>
                    <option value="yobe">Yobe</option>
                    <option value="zamfara">Zamfara</option>
                  </select>
                </div>

                {/* comment out zip-code */}

                {/* <div className="w-full">
                  <AuthInput
                    label="ZIP Code"
                    type="text"
                    onChange={handleChange}
                    name="businessZipCode"
                    value={businessDetails.businessZipCode}
                    placeholder="Enter ZIP code here"
                  />
                </div> */}
              </div>
              <button
                type="submit"
                disabled={!enableButton || mutation.isLoading}
                className={`mt-[22px] h-12 px-3 ${
                  !enableButton || mutation.isLoading
                    ? "cursor-default bg-[#9392e4]"
                    : "bg-[#4f4cd8] hover:bg-[#3e3abf] cursor-pointer"
                } text-white border-none rounded-[16px] text-[16px] transition ease-in-out duration-300`}
              >
                {mutation.isLoading ? (
                  <p className="flex justify-center animate-spin ">
                    {" "}
                    <FaSpinner size={20} />{" "}
                  </p>
                ) : (
                  "Finish Setup"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Cropping Modal */}
      {isCropModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-md w-[90%] max-w-md">
            <h3 className="mb-2 font-semibold">Crop Your Image</h3>
            {upImg && (
              <ReactCrop
                crop={crop}
                onChange={(newCrop) => setCrop(newCrop)}
                onComplete={(c) => setCompletedCrop(c)}
                aspect={1}
                circularCrop
              >
                <img
                  src={upImg}
                  alt="to be cropped"
                  onLoad={(e) => onImageLoaded(e.currentTarget)}
                />
              </ReactCrop>
            )}
            <div className="mt-4 flex justify-end">
              <button
                className="mr-2 px-4 py-2 bg-gray-300 rounded"
                onClick={() => setIsCropModalOpen(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-primary-purple text-white rounded"
                onClick={handleCropComplete}
                disabled={!completedCrop?.width || !completedCrop?.height}
              >
                Crop
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddBussiness;
