import { useUserAuthStore } from "../store/auth";

const apiBase = 'https://api.huxxle.com/pg/alert';

/**
 * Check if push notifications are supported in the current browser
 */
export function isPushNotificationSupported(): boolean {
  return 'serviceWorker' in navigator && 
         'PushManager' in window &&
         'Notification' in window;
}

/**
 * Check if the user is already subscribed to push notifications
 */
export async function isPushNotificationSubscribed(): Promise<boolean> {
  if (!isPushNotificationSupported()) {
    return false;
  }
  
  try {
    // Wait for service worker to be ready with a timeout
    const maxWaitTime = 3000; // 3 seconds
    const startTime = Date.now();
    
    let registration;
    while (!registration && (Date.now() - startTime < maxWaitTime)) {
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        registration = await navigator.serviceWorker.ready;
        break;
      } else {
        // Wait a short time before trying again
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }
    
    if (!registration) {
      console.warn("Service worker not ready when checking subscription status");
      return false;
    }
    
    const subscription = await registration.pushManager.getSubscription();
    
    if (!subscription) {
      return false;
    }

    // Attempt to validate the subscription by fetching its details
    try {
      const response = await fetch(subscription.endpoint, { method: 'HEAD' });
      if (!response.ok) {
        console.warn("Subscription is invalid:", response.status, response.statusText);
        return false;
      }
    } catch (error) {
      console.warn("Error validating subscription:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error checking subscription status:', error);
    return false;
  }
}

/**
 * Convert base64 string to Uint8Array (for VAPID key)
 */
function urlB64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

/**
 * Get the VAPID public key from the server
 */
async function getVapidPublicKey(): Promise<string> {
  try {
    // Directly fetch from the API server
    console.log("Fetching VAPID key from server");
    const response = await fetch(`${apiBase}/getPublicKey`);
    if (!response.ok) {
      throw new Error(`Failed to get public key: ${response.status}`);
    }
    const data = await response.json();
    return data.publicKey;
  } catch (error) {
    console.error('Error fetching public key:', error);
    throw error;
  }
}

/**
 * Subscribe to push notifications
 */
export async function subscribeToPushNotifications(vendorId?: string): Promise<boolean> {
  const actualVendorId = vendorId || useUserAuthStore.getState().user.vendorId;
  
  if (!actualVendorId) {
    console.error("Vendor ID is missing.");
    return false;
  }

  if (!isPushNotificationSupported()) {
    console.warn("Push notifications are not supported in this browser.");
    return false;
  }

  try {
    // Check if already subscribed first
    const swRegistration = await navigator.serviceWorker.ready;
    const existingSubscription = await swRegistration.pushManager.getSubscription();
    
    if (existingSubscription) {
      console.log("User already has an active subscription");
      
      // Send the existing subscription to the server to ensure it's registered
      const response = await fetch(`${apiBase}/subscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription: existingSubscription,
          vendorId: actualVendorId,
        }),
      });
      
      if (!response.ok) {
        console.warn(`Failed to register existing subscription: ${response.status}`);
      }
      
      return true; // Already subscribed
    }
    
    // Request notification permission
    const permission = await Notification.requestPermission();
    if (permission !== 'granted') {
      console.warn('Notification permission denied');
      return false;
    }

    // Get the VAPID public key
    const publicKey = await getVapidPublicKey();
    
    // Subscribe the user
    const subscription = await swRegistration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlB64ToUint8Array(publicKey)
    });
    
    // Send the subscription to the server
    const response = await fetch(`${apiBase}/subscribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        subscription: subscription,
        vendorId: actualVendorId,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to subscribe: ${response.status}`);
    }
    
    return true;
  } catch (error) {
    console.error('Subscription failed:', error);
    return false;
  }
}

/**
 * Unsubscribe from push notifications
 */
export async function unsubscribeFromPushNotifications(vendorId?: string): Promise<boolean> {
    const actualVendorId = vendorId || useUserAuthStore.getState().user.vendorId;
    
    if (!actualVendorId) {
      console.error("Vendor ID is missing.");
      return false;
    }
    
    if (!isPushNotificationSupported()) {
      return false;
    }
  
    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();
      
      if (!subscription) {
        return true; // Already unsubscribed
      }
      
      // First, unsubscribe locally
      const result = await subscription.unsubscribe();
      if (!result) {
        return false;
      }
      
      // Then, notify the server
      const response = await fetch(`${apiBase}/unsubscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription: subscription,
          vendorId: actualVendorId,
        }),
      });
  
      return response.ok;
    } catch (error) {
      console.error('Unsubscription failed:', error);
      return false;
    }
}
